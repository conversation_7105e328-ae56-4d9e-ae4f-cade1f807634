{"cells": [{"cell_type": "code", "execution_count": 6, "id": "725df4e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pip in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (25.1.1)\n", "Requirement already satisfied: wheel in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (0.45.1)\n", "Requirement already satisfied: setuptools in /Users/<USER>/.local/lib/python3.11/site-packages (80.9.0)\n", "Found existing installation: numpy 1.26.4\n", "Uninstalling numpy-1.26.4:\n", "  Successfully uninstalled numpy-1.26.4\n", "Found existing installation: scipy 1.11.4\n", "Uninstalling scipy-1.11.4:\n", "  Successfully uninstalled scipy-1.11.4\n", "\u001b[33mWARNING: Skipping tensorflow as it is not installed.\u001b[0m\u001b[33m\n", "\u001b[0mFound existing installation: tensorflow-macos 2.15.0\n", "Uninstalling tensorflow-macos-2.15.0:\n", "  Successfully uninstalled tensorflow-macos-2.15.0\n", "Found existing installation: tensorflow-metal 1.1.0\n", "Uninstalling tensorflow-metal-1.1.0:\n", "  Successfully uninstalled tensorflow-metal-1.1.0\n", "Found existing installation: tensorflow-estimator 2.15.0\n", "Uninstalling tensorflow-estimator-2.15.0:\n", "  Successfully uninstalled tensorflow-estimator-2.15.0\n", "Found existing installation: tensorflow-io-gcs-filesystem 0.37.1\n", "Uninstalling tensorflow-io-gcs-filesystem-0.37.1:\n", "  Successfully uninstalled tensorflow-io-gcs-filesystem-0.37.1\n", "Found existing installation: h5py 3.14.0\n", "Uninstalling h5py-3.14.0:\n", "  Successfully uninstalled h5py-3.14.0\n", "Found existing installation: protobuf 3.20.3\n", "Uninstalling protobuf-3.20.3:\n", "  Successfully uninstalled protobuf-3.20.3\n", "Files removed: 6068 (5468.4 MB)\n", "Collecting numpy==1.24.3\n", "  Downloading numpy-1.24.3-cp311-cp311-macosx_11_0_arm64.whl.metadata (5.6 kB)\n", "Downloading numpy-1.24.3-cp311-cp311-macosx_11_0_arm64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m16.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: numpy\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "scikit-learn 1.7.0 requires scipy>=1.8.0, which is not installed.\n", "onnx 1.16.0 requires protobuf>=3.20.2, which is not installed.\n", "onnxruntime 1.22.0 requires protobuf, which is not installed.\n", "pynndescent 0.5.13 requires scipy>=1.0, which is not installed.\n", "ase 3.25.0 requires scipy>=1.6.0, which is not installed.\n", "statsmodels 0.14.4 requires scipy!=1.9.2,>=1.8, which is not installed.\n", "mlflow 3.1.1 requires scipy<2, which is not installed.\n", "tensorboard 2.15.2 requires protobuf!=4.24.0,>=3.19.6, which is not installed.\n", "pgmpy 1.0.0 requires scipy, which is not installed.\n", "scikit-image 0.25.2 requires scipy>=1.11.4, which is not installed.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed numpy-1.24.3\n", "Collecting scipy==1.10.1\n", "  Downloading scipy-1.10.1-cp311-cp311-macosx_12_0_arm64.whl.metadata (100 kB)\n", "Requirement already satisfied: numpy<1.27.0,>=1.19.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scipy==1.10.1) (1.24.3)\n", "Downloading scipy-1.10.1-cp311-cp311-macosx_12_0_arm64.whl (28.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m28.7/28.7 MB\u001b[0m \u001b[31m32.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: scipy\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "scikit-image 0.25.2 requires scipy>=1.11.4, but you have scipy 1.10.1 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed scipy-1.10.1\n", "Collecting h5py==3.9.0\n", "  Downloading h5py-3.9.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (2.5 kB)\n", "Requirement already satisfied: numpy>=1.17.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from h5py==3.9.0) (1.24.3)\n", "Downloading h5py-3.9.0-cp311-cp311-macosx_11_0_arm64.whl (2.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.6/2.6 MB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: h5py\n", "Successfully installed h5py-3.9.0\n", "Collecting protobuf==3.20.3\n", "  Downloading protobuf-3.20.3-py2.py3-none-any.whl.metadata (720 bytes)\n", "Downloading protobuf-3.20.3-py2.py3-none-any.whl (162 kB)\n", "Installing collected packages: protobuf\n", "Successfully installed protobuf-3.20.3\n", "Collecting tensorflow-macos==2.13.0\n", "  Downloading tensorflow_macos-2.13.0-cp311-cp311-macosx_12_0_arm64.whl.metadata (3.2 kB)\n", "Requirement already satisfied: absl-py>=1.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (2.3.1)\n", "Requirement already satisfied: astunparse>=1.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (1.6.3)\n", "Requirement already satisfied: flatbuffers>=23.1.21 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (25.2.10)\n", "Collecting gast<=0.4.0,>=0.2.1 (from tensorflow-macos==2.13.0)\n", "  Downloading gast-0.4.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: google-pasta>=0.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (0.2.0)\n", "Requirement already satisfied: h5py>=2.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (3.9.0)\n", "Requirement already satisfied: libclang>=13.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (18.1.1)\n", "Requirement already satisfied: numpy<=1.24.3,>=1.22 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (1.24.3)\n", "Requirement already satisfied: opt-einsum>=2.3.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (3.4.0)\n", "Requirement already satisfied: packaging in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (25.0)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (3.20.3)\n", "Requirement already satisfied: setuptools in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (80.9.0)\n", "Requirement already satisfied: six>=1.12.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (1.17.0)\n", "Requirement already satisfied: termcolor>=1.1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (3.1.0)\n", "Collecting typing-extensions<4.6.0,>=3.6.6 (from tensorflow-macos==2.13.0)\n", "  Downloading typing_extensions-4.5.0-py3-none-any.whl.metadata (8.5 kB)\n", "Requirement already satisfied: wrapt>=1.11.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (1.14.1)\n", "Requirement already satisfied: grpcio<2.0,>=1.24.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-macos==2.13.0) (1.73.1)\n", "Collecting tensorboard<2.14,>=2.13 (from tensorflow-macos==2.13.0)\n", "  Downloading tensorboard-2.13.0-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting tensorflow-estimator<2.14,>=2.13.0 (from tensorflow-macos==2.13.0)\n", "  Downloading tensorflow_estimator-2.13.0-py2.py3-none-any.whl.metadata (1.3 kB)\n", "Collecting keras<2.14,>=2.13.1 (from tensorflow-macos==2.13.0)\n", "  Downloading keras-2.13.1-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: google-auth<3,>=1.6.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (2.40.3)\n", "Collecting google-auth-oauthlib<1.1,>=0.5 (from tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0)\n", "  Downloading google_auth_oauthlib-1.0.0-py2.py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: markdown>=2.6.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (3.8.2)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (2.32.4)\n", "Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (0.7.2)\n", "Requirement already satisfied: werkzeug>=1.0.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (3.1.3)\n", "Requirement already satisfied: wheel>=0.26 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (0.45.1)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth<3,>=1.6.3->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (5.5.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth<3,>=1.6.3->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth<3,>=1.6.3->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (4.9.1)\n", "Requirement already satisfied: requests-oauthlib>=0.7.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth-oauthlib<1.1,>=0.5->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (2.0.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (2025.6.15)\n", "Requirement already satisfied: pyasn1>=0.1.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rsa<5,>=3.1.4->google-auth<3,>=1.6.3->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (0.6.1)\n", "Requirement already satisfied: oauthlib>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib<1.1,>=0.5->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (3.3.1)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from werkzeug>=1.0.1->tensorboard<2.14,>=2.13->tensorflow-macos==2.13.0) (3.0.2)\n", "Downloading tensorflow_macos-2.13.0-cp311-cp311-macosx_12_0_arm64.whl (189.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m189.3/189.3 MB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:02\u001b[0m\n", "\u001b[?25hDownloading gast-0.4.0-py3-none-any.whl (9.8 kB)\n", "Downloading keras-2.13.1-py3-none-any.whl (1.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m34.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tensorboard-2.13.0-py3-none-any.whl (5.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m38.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading google_auth_oauthlib-1.0.0-py2.py3-none-any.whl (18 kB)\n", "Downloading tensorflow_estimator-2.13.0-py2.py3-none-any.whl (440 kB)\n", "Downloading typing_extensions-4.5.0-py3-none-any.whl (27 kB)\n", "Installing collected packages: typing-extensions, tensorflow-estimator, keras, gast, google-auth-oauthlib, tensorboard, tensorflow-macos\n", "\u001b[2K  Attempting uninstall: typing-extensions\n", "\u001b[2K    Found existing installation: typing_extensions 4.14.0\n", "\u001b[2K    Uninstalling typing_extensions-4.14.0:\n", "\u001b[2K      Successfully uninstalled typing_extensions-4.14.032m0/7\u001b[0m [typing-extensions]\n", "\u001b[2K  Attempting uninstall: keras90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-estimator]\n", "\u001b[2K    Found existing installation: keras 2.15.0━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-estimator]\n", "\u001b[2K    Uninstalling keras-2.15.0:━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-estimator]\n", "\u001b[2K      Successfully uninstalled keras-2.15.0━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-estimator]\n", "\u001b[2K  Attempting uninstall: gast╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]-estimator]\n", "\u001b[2K    Found existing installation: gast 0.6.0━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K    Uninstalling gast-0.6.0:m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K      Successfully uninstalled gast-0.6.0━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K  Attempting uninstall: google-auth-oauthlib━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K    Found existing installation: google-auth-oauthlib 1.2.2━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K    Uninstalling google-auth-oauthlib-1.2.2:━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K      Successfully uninstalled google-auth-oauthlib-1.2.2━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K  Attempting uninstall: tensorboard━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K    Found existing installation: tensorboard 2.15.2━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K    Uninstalling tensorboard-2.15.2:━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K      Successfully uninstalled tensorboard-2.15.2━━━━━━━━━━━━━\u001b[0m \u001b[32m2/7\u001b[0m [keras]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7/7\u001b[0m [tensorflow-macos][tensorflow-macos]\n", "\u001b[1A\u001b[2K\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "pydantic-core 2.33.2 requires typing-extensions!=4.7.0,>=4.6.0, but you have typing-extensions 4.5.0 which is incompatible.\n", "typing-inspection 0.4.1 requires typing-extensions>=4.12.0, but you have typing-extensions 4.5.0 which is incompatible.\n", "onnxscript 0.1.0 requires typing_extensions>=4.10, but you have typing-extensions 4.5.0 which is incompatible.\n", "graphene 3.4.3 requires typing-extensions<5,>=4.7.1, but you have typing-extensions 4.5.0 which is incompatible.\n", "exceptiongroup 1.3.0 requires typing-extensions>=4.6.0; python_version < \"3.13\", but you have typing-extensions 4.5.0 which is incompatible.\n", "fastapi 0.115.14 requires typing-extensions>=4.8.0, but you have typing-extensions 4.5.0 which is incompatible.\n", "ezdxf 1.4.2 requires typing_extensions>=4.6.0, but you have typing-extensions 4.5.0 which is incompatible.\n", "alembic 1.16.2 requires typing-extensions>=4.12, but you have typing-extensions 4.5.0 which is incompatible.\n", "torch 2.5.1 requires typing-extensions>=4.8.0, but you have typing-extensions 4.5.0 which is incompatible.\n", "optree 0.16.0 requires typing-extensions>=4.6.0, but you have typing-extensions 4.5.0 which is incompatible.\n", "sqlalchemy 2.0.41 requires typing-extensions>=4.6.0, but you have typing-extensions 4.5.0 which is incompatible.\n", "pydantic 2.11.7 requires typing-extensions>=4.12.2, but you have typing-extensions 4.5.0 which is incompatible.\n", "ipython 9.3.0 requires typing_extensions>=4.6; python_version < \"3.12\", but you have typing-extensions 4.5.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed gast-0.4.0 google-auth-oauthlib-1.0.0 keras-2.13.1 tensorboard-2.13.0 tensorflow-estimator-2.13.0 tensorflow-macos-2.13.0 typing-extensions-4.5.0\n", "Collecting tensorflow-metal==1.0.1\n", "  Downloading tensorflow_metal-1.0.1-cp311-cp311-macosx_12_0_arm64.whl.metadata (1.2 kB)\n", "Requirement already satisfied: wheel~=0.35 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-metal==1.0.1) (0.45.1)\n", "Requirement already satisfied: six>=1.15.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow-metal==1.0.1) (1.17.0)\n", "Downloading tensorflow_metal-1.0.1-cp311-cp311-macosx_12_0_arm64.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0mta \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: tensorflow-metal\n", "Successfully installed tensorflow-metal-1.0.1\n", "Installation complete. Please restart your Jupyter kernel before importing TensorFlow.\n", "After restart, run:\n", "import tensorflow as tf\n", "print('TensorFlow version:', tf.__version__)\n", "print('GPU devices:', tf.config.list_physical_devices('GPU'))\n"]}], "source": ["# Comprehensive TensorFlow Installation Fix for macOS\n", "\n", "# 1. Upgrade pip and wheel\n", "!pip install --upgrade pip wheel setuptools\n", "\n", "# 2. Complete cleanup - remove ALL TensorFlow related packages\n", "!pip uninstall -y numpy scipy tensorflow tensorflow-macos tensorflow-metal tensorflow-estimator tensorflow-io-gcs-filesystem h5py protobuf\n", "\n", "# 3. Clear pip cache to avoid corrupted packages\n", "!pip cache purge\n", "\n", "# 4. Install dependencies in correct order\n", "!pip install numpy==1.24.3\n", "!pip install scipy==1.10.1\n", "!pip install h5py==3.9.0\n", "!pip install protobuf==3.20.3\n", "\n", "# 5. Install TensorFlow for macOS with specific compatible versions\n", "!pip install tensorflow-macos==2.13.0\n", "!pip install tensorflow-metal==1.0.1\n", "\n", "# 6. Restart kernel after installation\n", "print(\"Installation complete. Please restart your Jupyter kernel before importing TensorFlow.\")\n", "print(\"After restart, run:\")\n", "print(\"import tensorflow as tf\")\n", "print(\"print('TensorFlow version:', tf.__version__)\")\n", "print(\"print('GPU devices:', tf.config.list_physical_devices('GPU'))\")\n", "\n", "# Alternative: If above doesn't work, try this more conservative approach:\n", "# !pip install --upgrade pip\n", "# !pip install numpy==1.21.6\n", "# !pip install tensorflow-macos==2.12.0\n", "# !pip install tensorflow-metal==1.0.0"]}, {"cell_type": "code", "execution_count": 8, "id": "759f63e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found existing installation: tensorflow-macos 2.13.0\n", "Uninstalling tensorflow-macos-2.13.0:\n", "  Successfully uninstalled tensorflow-macos-2.13.0\n", "Found existing installation: tensorflow-metal 1.0.1\n", "Uninstalling tensorflow-metal-1.0.1:\n", "  Successfully uninstalled tensorflow-metal-1.0.1\n", "\u001b[33mWARNING: Skipping tensorflow as it is not installed.\u001b[0m\u001b[33m\n", "\u001b[0mFound existing installation: tensorflow-estimator 2.13.0\n", "Uninstalling tensorflow-estimator-2.13.0:\n", "  Successfully uninstalled tensorflow-estimator-2.13.0\n", "\u001b[33mWARNING: Skipping tensorflow-io-gcs-filesystem as it is not installed.\u001b[0m\u001b[33m\n", "\u001b[0mFiles removed: 72 (245.1 MB)\n", "Collecting tensorflow==2.16.1\n", "  Downloading tensorflow-2.16.1-cp311-cp311-macosx_12_0_arm64.whl.metadata (4.1 kB)\n", "Requirement already satisfied: absl-py>=1.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (2.3.1)\n", "Requirement already satisfied: astunparse>=1.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (1.6.3)\n", "Requirement already satisfied: flatbuffers>=23.5.26 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (25.2.10)\n", "Requirement already satisfied: gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (0.4.0)\n", "Requirement already satisfied: google-pasta>=0.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (0.2.0)\n", "Collecting h5py>=3.10.0 (from tensorflow==2.16.1)\n", "  Downloading h5py-3.14.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (2.7 kB)\n", "Requirement already satisfied: libclang>=13.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (18.1.1)\n", "Collecting ml-dtypes~=0.3.1 (from tensorflow==2.16.1)\n", "  Downloading ml_dtypes-0.3.2-cp311-cp311-macosx_10_9_universal2.whl.metadata (20 kB)\n", "Requirement already satisfied: opt-einsum>=2.3.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (3.4.0)\n", "Requirement already satisfied: packaging in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.1) (25.0)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (3.20.3)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.1) (2.32.4)\n", "Requirement already satisfied: setuptools in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.1) (80.9.0)\n", "Requirement already satisfied: six>=1.12.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.1) (1.17.0)\n", "Requirement already satisfied: termcolor>=1.1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (3.1.0)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (4.5.0)\n", "Requirement already satisfied: wrapt>=1.11.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (1.14.1)\n", "Requirement already satisfied: grpcio<2.0,>=1.24.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (1.73.1)\n", "Collecting tensorboard<2.17,>=2.16 (from tensorflow==2.16.1)\n", "  Downloading tensorboard-2.16.2-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting keras>=3.0.0 (from tensorflow==2.16.1)\n", "  Downloading keras-3.10.0-py3-none-any.whl.metadata (6.0 kB)\n", "Collecting tensorflow-io-gcs-filesystem>=0.23.1 (from tensorflow==2.16.1)\n", "  Downloading tensorflow_io_gcs_filesystem-0.37.1-cp311-cp311-macosx_12_0_arm64.whl.metadata (14 kB)\n", "Requirement already satisfied: numpy<2.0.0,>=1.23.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.1) (1.24.3)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.1) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.1) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.1) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.1) (2025.6.15)\n", "Requirement already satisfied: markdown>=2.6.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.17,>=2.16->tensorflow==2.16.1) (3.8.2)\n", "Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.17,>=2.16->tensorflow==2.16.1) (0.7.2)\n", "Requirement already satisfied: werkzeug>=1.0.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorboard<2.17,>=2.16->tensorflow==2.16.1) (3.1.3)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from astunparse>=1.6.0->tensorflow==2.16.1) (0.45.1)\n", "Requirement already satisfied: rich in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from keras>=3.0.0->tensorflow==2.16.1) (14.0.0)\n", "Requirement already satisfied: namex in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from keras>=3.0.0->tensorflow==2.16.1) (0.1.0)\n", "Requirement already satisfied: optree in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from keras>=3.0.0->tensorflow==2.16.1) (0.16.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from werkzeug>=1.0.1->tensorboard<2.17,>=2.16->tensorflow==2.16.1) (3.0.2)\n", "Collecting typing-extensions>=3.6.6 (from tensorflow==2.16.1)\n", "  Downloading typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rich->keras>=3.0.0->tensorflow==2.16.1) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rich->keras>=3.0.0->tensorflow==2.16.1) (2.19.2)\n", "Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich->keras>=3.0.0->tensorflow==2.16.1) (0.1.2)\n", "Downloading tensorflow-2.16.1-cp311-cp311-macosx_12_0_arm64.whl (227.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m227.0/227.0 MB\u001b[0m \u001b[31m1.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:06\u001b[0mm\n", "\u001b[?25hDownloading ml_dtypes-0.3.2-cp311-cp311-macosx_10_9_universal2.whl (389 kB)\n", "Downloading tensorboard-2.16.2-py3-none-any.whl (5.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.5/5.5 MB\u001b[0m \u001b[31m16.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading h5py-3.14.0-cp311-cp311-macosx_11_0_arm64.whl (2.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m18.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading keras-3.10.0-py3-none-any.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m19.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tensorflow_io_gcs_filesystem-0.37.1-cp311-cp311-macosx_12_0_arm64.whl (3.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.5/3.5 MB\u001b[0m \u001b[31m20.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading typing_extensions-4.14.1-py3-none-any.whl (43 kB)\n", "Installing collected packages: typing-extensions, tensorflow-io-gcs-filesystem, ml-dtypes, h5py, tensorboard, keras, tensorflow\n", "\u001b[2K  Attempting uninstall: typing-extensions\n", "\u001b[2K    Found existing installation: typing_extensions 4.5.0\n", "\u001b[2K    Uninstalling typing_extensions-4.5.0:\n", "\u001b[2K      Successfully uninstalled typing_extensions-4.5.0[32m0/7\u001b[0m [typing-extensions]\n", "\u001b[2K  Attempting uninstall: ml-dtypes━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K    Found existing installation: ml-dtypes 0.2.0━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K    Uninstalling ml-dtypes-0.2.0:━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K      Successfully uninstalled ml-dtypes-0.2.0━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K  Attempting uninstall: h5py━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K    Found existing installation: h5py 3.9.0━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K    Uninstalling h5py-3.9.0:━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K      Successfully uninstalled h5py-3.9.0━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K  Attempting uninstall: tensorboard━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K    Found existing installation: tensorboard 2.13.0━━━━━━━━━━━\u001b[0m \u001b[32m1/7\u001b[0m [tensorflow-io-gcs-filesystem]\n", "\u001b[2K    Uninstalling tensorboard-2.13.0:91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4/7\u001b[0m [tensorboard]s-filesystem]\n", "\u001b[2K      Successfully uninstalled tensorboard-2.13.0━━━━━━━━━━━━━\u001b[0m \u001b[32m4/7\u001b[0m [tensorboard]\n", "\u001b[2K  Attempting uninstall: keras━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4/7\u001b[0m [tensorboard]\n", "\u001b[2K    Found existing installation: keras 2.13.1━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4/7\u001b[0m [tensorboard]\n", "\u001b[2K    Uninstalling keras-2.13.1:\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4/7\u001b[0m [tensorboard]\n", "\u001b[2K      Successfully uninstalled keras-2.13.10m━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4/7\u001b[0m [tensorboard]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7/7\u001b[0m [tensorflow]7\u001b[0m [tensorflow]\n", "\u001b[1A\u001b[2KSuccessfully installed h5py-3.14.0 keras-3.10.0 ml-dtypes-0.3.2 tensorboard-2.16.2 tensorflow-2.16.1 tensorflow-io-gcs-filesystem-0.37.1 typing-extensions-4.14.0\n"]}, {"ename": "AttributeError", "evalue": "`np.complex_` was removed in the NumPy 2.0 release. Use `np.complex128` instead.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 14\u001b[39m\n\u001b[32m     11\u001b[39m get_ipython().system(\u001b[33m'\u001b[39m\u001b[33mpip install tensorflow==2.16.1\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# Verify installation\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON>orflow\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtf\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m     16\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mTensorFlow version:\u001b[39m\u001b[33m\"\u001b[39m, tf.__version__)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/__init__.py:45\u001b[39m\n\u001b[32m     42\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tf2 \u001b[38;5;28;01mas\u001b[39;00m _tf2\n\u001b[32m     43\u001b[39m _tf2.enable()\n\u001b[32m---> \u001b[39m\u001b[32m45\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>low\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m __internal__\n\u001b[32m     46\u001b[39m \u001b[38;5;28;01mf<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m __operators__\n\u001b[32m     47\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m audio\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/_api/v2/__internal__/__init__.py:8\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[33;03m\"\"\"Public API for tf._api.v2.__internal__ namespace\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msys\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m_sys\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m__internal__\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m autograph\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m__internal__\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m decorator\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m__internal__\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dispatch\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/_api/v2/__internal__/autograph/__init__.py:8\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[33;03m\"\"\"Public API for tf._api.v2.__internal__.autograph namespace\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msys\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m_sys\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mag_ctx\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m control_status_ctx \u001b[38;5;66;03m# line: 34\u001b[39;00m\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mimpl\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tf_convert \u001b[38;5;66;03m# line: 493\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/autograph/core/ag_ctx.py:21\u001b[39m\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01minspect\u001b[39;00m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mthreading\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ag_logging\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtf_export\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tf_export\n\u001b[32m     25\u001b[39m stacks = threading.local()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/autograph/utils/__init__.py:17\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Copyright 2016 The TensorFlow Authors. All Rights Reserved.\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# Licensed under the Apache License, Version 2.0 (the \"License\");\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# limitations under the License.\u001b[39;00m\n\u001b[32m     14\u001b[39m \u001b[38;5;66;03m# ==============================================================================\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[33;03m\"\"\"Utility module that contains APIs usable in the generated code.\"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcontext_managers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m control_dependency_on_returns\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmisc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m alias_tensors\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtensor_list\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dynamic_list_append\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/autograph/utils/context_managers.py:20\u001b[39m\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcontextlib\u001b[39;00m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON>orflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ops\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyt<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_array_ops\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcontrol_dependency_on_returns\u001b[39m(return_value):\n\u001b[32m     24\u001b[39m \u001b[38;5;250m  \u001b[39m\u001b[33;03m\"\"\"Create a TF control dependency on the return values of a function.\u001b[39;00m\n\u001b[32m     25\u001b[39m \n\u001b[32m     26\u001b[39m \u001b[33;03m  If the function had no return value, a no-op context is returned.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     32\u001b[39m \u001b[33;03m    A context manager.\u001b[39;00m\n\u001b[32m     33\u001b[39m \u001b[33;03m  \"\"\"\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/ops/tensor_array_ops.py:36\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m type_spec\n\u001b[32m     35\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m type_spec_registry\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m array_ops\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m array_ops_stack\n\u001b[32m     38\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m control_flow_util\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/ops/array_ops.py:22\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m     21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>low\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mconfig\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m flags\n\u001b[32m---> \u001b[39m\u001b[32m22\u001b[39m \u001b[38;5;28;01mf<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m<PERSON><PERSON>or\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m api \u001b[38;5;28;01mas\u001b[39;00m d_api\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01meager\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m context\n\u001b[32m     24\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01meager\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m record\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/dtensor/python/api.py:21\u001b[39m\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mthreading\u001b[39;00m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Any, Callable, Optional, Sequence\n\u001b[32m---> \u001b[39m\u001b[32m21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>low\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdtensor\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dtensor_device\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdtensor\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m gen_dtensor_ops\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdtensor\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m layout \u001b[38;5;28;01mas\u001b[39;00m layout_lib\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/dtensor/python/dtensor_device.py:33\u001b[39m\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dtypes\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON>orflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ops\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m sparse_tensor\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_util\n\u001b[32m     35\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m _pywrap_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/framework/sparse_tensor.py:28\u001b[39m\n\u001b[32m     26\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dtypes\n\u001b[32m     27\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ops\n\u001b[32m---> \u001b[39m\u001b[32m28\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyt<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m override_binary_operator\n\u001b[32m     29\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor\n\u001b[32m     30\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_shape\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/framework/override_binary_operator.py:24\u001b[39m\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_shape\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m gen_math_ops\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mnumpy_ops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m np_dtypes\n\u001b[32m     25\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m nest\n\u001b[32m     26\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m traceback_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/ops/numpy_ops/np_dtypes.py:30\u001b[39m\n\u001b[32m     26\u001b[39m bool_ = np.bool_\n\u001b[32m     27\u001b[39m tf_export.tf_export(\u001b[33m'\u001b[39m\u001b[33mexperimental.numpy.bool_\u001b[39m\u001b[33m'\u001b[39m, v1=[]).export_constant(\n\u001b[32m     28\u001b[39m     \u001b[34m__name__\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mbool_\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     29\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m30\u001b[39m complex_ = \u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcomplex_\u001b[49m\n\u001b[32m     31\u001b[39m tf_export.tf_export(\u001b[33m'\u001b[39m\u001b[33mexperimental.numpy.complex_\u001b[39m\u001b[33m'\u001b[39m, v1=[]).export_constant(\n\u001b[32m     32\u001b[39m     \u001b[34m__name__\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mcomplex_\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     33\u001b[39m )\n\u001b[32m     34\u001b[39m complex128 = np.complex128\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/numpy/__init__.py:400\u001b[39m, in \u001b[36m__getattr__\u001b[39m\u001b[34m(attr)\u001b[39m\n\u001b[32m    397\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(__former_attrs__[attr], name=\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    399\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr \u001b[38;5;129;01min\u001b[39;00m __expired_attributes__:\n\u001b[32m--> \u001b[39m\u001b[32m400\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\n\u001b[32m    401\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m`np.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattr\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m` was removed in the NumPy 2.0 release. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    402\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m__expired_attributes__[attr]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m    403\u001b[39m         name=\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    404\u001b[39m     )\n\u001b[32m    406\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr == \u001b[33m\"\u001b[39m\u001b[33mchararray\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    407\u001b[39m     warnings.warn(\n\u001b[32m    408\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m`np.chararray` is deprecated and will be removed from \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    409\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mthe main namespace in the future. Use an array with a string \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    410\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mor bytes dtype instead.\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m, stacklevel=\u001b[32m2\u001b[39m)\n", "\u001b[31mAttributeError\u001b[39m: `np.complex_` was removed in the NumPy 2.0 release. Use `np.complex128` instead."]}], "source": ["# Complete removal of TensorFlow-macos\n", "!pip uninstall -y tensorflow-macos tensorflow-metal\n", "\n", "# Also remove any other TensorFlow packages to avoid conflicts\n", "!pip uninstall -y tensorflow tensorflow-estimator tensorflow-io-gcs-filesystem\n", "\n", "# Clear pip cache\n", "!pip cache purge\n", "\n", "# Install regular TensorFlow (works with Python 3.11)\n", "!pip install tensorflow==2.16.1\n", "\n", "# Verify installation\n", "import tensorflow as tf\n", "import numpy as np\n", "print(\"TensorFlow version:\", tf.__version__)\n", "print(\"Python version:\", tf.version.VERSION)\n", "print(\"Available devices:\", tf.config.list_physical_devices())\n", "\n", "# Test basic functionality\n", "print(\"\\nTesting basic operations:\")\n", "a = tf.constant([1, 2, 3])\n", "b = tf.constant([4, 5, 6])\n", "c = tf.add(a, b)\n", "print(\"1 + 4 =\", c.numpy())\n", "print(\"<PERSON><PERSON><PERSON><PERSON> is working correctly!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}