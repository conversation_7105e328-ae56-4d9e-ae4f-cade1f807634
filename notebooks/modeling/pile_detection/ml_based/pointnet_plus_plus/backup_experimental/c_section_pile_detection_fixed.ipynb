{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# C-Section Pile Detection (PointNet++) \n", "\n", "This notebook implements working C-section pile detection using geometric rules, with PointNet++ architecture ready for future training.\n", "\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (C-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "**Method**: Geometric-based detection with PointNet++ for future training\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025 \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"csf\"  # Options: csf, pmf, ransac\n", "confidence_threshold = 0.6  # Lower threshold for C-sections (harder to detect)\n", "output_dir = \"../../../../../data/output_runs/pile_detection\"\n", "use_aligned_data = True  # Use ICP aligned point clouds if available\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch version: 2.5.1\n", "CUDA available: False\n", "Using device: cpu\n", "C-SECTION PILE DETECTION - CSF\n", "Site: trino_enel\n", "Output: ../../../../../data/output_runs/pile_detection/csf\n", "Use aligned data: Enabled\n", "Confidence threshold: 0.6\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import pandas as pd\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.decomposition import PCA\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Deep learning imports\n", "try:\n", "    import torch\n", "    import torch.nn as nn\n", "    import torch.nn.functional as F\n", "    from torch.utils.data import Dataset, DataLoader\n", "    TORCH_AVAILABLE = True\n", "    print(f\"PyTorch version: {torch.__version__}\")\n", "    print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "except ImportError:\n", "    TORCH_AVAILABLE = False\n", "    print(\"PyTorch not available - using geometric detection only\")\n", "    device = None\n", "\n", "# Setup\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"C-SECTION PILE DETECTION - {ground_method.upper()}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")\n", "print(f\"Use aligned data: {'Enabled' if use_aligned_data else '❌ Disabled'}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ls: -: No such file or directory\n", "ls: lh: No such file or directory\n", "../../../../../data:\n", "\u001b[34mENEL\u001b[m\u001b[m                         \u001b[34mmlruns\u001b[m\u001b[m\n", "\u001b[34mUsers\u001b[m\u001b[m                        \u001b[34mnotebooks\u001b[m\u001b[m\n", "\u001b[34manalysis_output\u001b[m\u001b[m              \u001b[34moutput_runs\u001b[m\u001b[m\n", "convert_ifc_to_pointcloud.py \u001b[34mprocessed\u001b[m\u001b[m\n", "\u001b[34mdata\u001b[m\u001b[m                         \u001b[34mraw\u001b[m\u001b[m\n", "ifc_metadata.csv             \u001b[34mreference\u001b[m\u001b[m\n"]}], "source": ["\n", "!ls - lh ../../../../../data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==> LOADING POINT CLOUD DATA\n", "Using aligned point cloud: ../../../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_aligned.ply\n", "\n", "DATA SOURCE: ICP aligned\n", "Points loaded: 508,032\n", "Bounds: X[435222.1, 436796.9] Y[5010809.4, 5012545.1] Z[-2.4, 13.3]\n"]}], "source": ["from pathlib import Path\n", "import numpy as np\n", "import open3d as o3d\n", "\n", "# Load point cloud data, preferring aligned ICP output if available\n", "print(\"==> LOADING POINT CLOUD DATA\")\n", "\n", "#aligned_file = Path(f\"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply\")\n", "aligned_file = Path(\"../../../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_aligned.ply\")\n", "\n", "if aligned_file.exists():\n", "    input_file = aligned_file\n", "    data_source = \"ICP aligned\"\n", "    print(f\"Using aligned point cloud: {input_file}\")\n", "else:\n", "    print(f\"Aligned file not found: {aligned_file}\")\n", "    print(\"Falling back to ground segmentation data\")\n", "    input_file = Path(f\"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply\")\n", "    data_source = \"Ground segmentation\"\n", "\n", "# Load point cloud\n", "pcd = o3d.io.read_point_cloud(str(input_file))\n", "points = np.asarray(pcd.points)\n", "\n", "# Display summary\n", "print(f\"\\nDATA SOURCE: {data_source}\")\n", "print(f\"Points loaded: {points.shape[0]:,}\")\n", "print(f\"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] \"\n", "      f\"Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] \"\n", "      f\"Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PointNet++ for C-Section Detection - Clean Implementation\n", "\n", "Let's implement a clean, trainable PointNet++ model for C-section pile detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Simple PointNet model implemented\n"]}], "source": ["if TORCH_AVAILABLE:\n", "    # Simple PointNet++ for C-section detection\n", "    class SimplePointNet(nn.Module):\n", "        \"\"\"Simplified PointNet for C-section pile detection\"\"\"\n", "        def __init__(self, num_points=1024, num_classes=2):\n", "            super(SimplePointNet, self).__init__()\n", "            self.num_points = num_points\n", "            \n", "            # Point feature extraction\n", "            self.conv1 = nn.Conv1d(3, 64, 1)\n", "            self.conv2 = nn.Conv1d(64, 128, 1)\n", "            self.conv3 = nn.Conv1d(128, 256, 1)\n", "            \n", "            self.bn1 = nn.BatchNorm1d(64)\n", "            self.bn2 = nn.BatchNorm1d(128)\n", "            self.bn3 = nn.BatchNorm1d(256)\n", "            \n", "            # Classification head\n", "            self.fc1 = nn.Linear(256, 128)\n", "            self.fc2 = nn.<PERSON>ar(128, 64)\n", "            self.fc3 = nn.Linear(64, num_classes)\n", "            \n", "            self.dropout = nn.Dropout(0.3)\n", "            \n", "        def forward(self, x):\n", "            # x: [batch_size, 3, num_points]\n", "            x = F.relu(self.bn1(self.conv1(x)))\n", "            x = F.relu(self.bn2(self.conv2(x)))\n", "            x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "            \n", "            # Global max pooling\n", "            x = torch.max(x, 2)[0]  # [batch_size, 256]\n", "            \n", "            # Classification\n", "            x = F.relu(self.fc1(x))\n", "            x = self.dropout(x)\n", "            x = <PERSON>.relu(self.fc2(x))\n", "            x = self.dropout(x)\n", "            x = self.fc3(x)\n", "            \n", "            return F.log_softmax(x, dim=1)\n", "    \n", "    print(\"Simple PointNet model implemented\")\n", "else:\n", "    print(\"Skipping PointNet implementation - PyTorch not available\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Real data utilities implemented\n"]}], "source": ["# Real data utilities\n", "if TORCH_AVAILABLE:\n", "    def load_pile_coordinates(site_name):\n", "        \"\"\"Load pile coordinates from IFC metadata\"\"\"\n", "        pile_file = Path(f\"../../../../../data/processed/{site_name}/advanced_ifc_metadata/advanced_pile_coordinates.csv\")\n", "        \n", "        if not pile_file.exists():\n", "            print(f\"Pile coordinates not found: {pile_file}\")\n", "            return None\n", "        \n", "        pile_df = pd.read_csv(pile_file)\n", "        print(f\"Loaded {len(pile_df)} pile coordinates from IFC\")\n", "        \n", "        # Extract coordinates\n", "        pile_coords = pile_df[['X', 'Y', 'Z']].values\n", "        return pile_coords, pile_df\n", "    \n", "    def create_training_patches(points, pile_coords, patch_size=3.0, num_points=512):\n", "        \"\"\"Create training patches from real point cloud data\"\"\"\n", "        print(f\"Creating training patches from real data...\")\n", "        print(f\"Point cloud: {points.shape[0]:,} points\")\n", "        print(f\"Pile locations: {len(pile_coords)} piles\")\n", "        \n", "        X = []\n", "        y = []\n", "        \n", "        # Create positive samples (patches around pile locations)\n", "        for pile_coord in pile_coords:\n", "            # Extract points within patch_size radius of pile\n", "            distances = np.linalg.norm(points[:, :2] - pile_coord[:2], axis=1)\n", "            patch_mask = distances < patch_size / 2\n", "            \n", "            if np.sum(patch_mask) >= 50:  # Minimum points for a valid patch\n", "                patch_points = points[patch_mask]\n", "                \n", "                # Center the patch\n", "                patch_points = patch_points - patch_points.mean(axis=0)\n", "                \n", "                # Resample to fixed number of points\n", "                if len(patch_points) >= num_points:\n", "                    indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "                else:\n", "                    indices = np.random.choice(len(patch_points), num_points, replace=True)\n", "                \n", "                patch_points = patch_points[indices]\n", "                \n", "                X.append(patch_points)\n", "                y.append(1)  # Pile patch\n", "        \n", "        # Create negative samples (random patches away from piles)\n", "        num_negative = len(X)  # Same number as positive samples\n", "        \n", "        for _ in range(num_negative):\n", "            # Random center point\n", "            x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "            y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "            \n", "            center_x = np.random.uniform(x_min, x_max)\n", "            center_y = np.random.uniform(y_min, y_max)\n", "            \n", "            # Check if this center is far enough from any pile\n", "            distances_to_piles = np.linalg.norm(pile_coords[:, :2] - [center_x, center_y], axis=1)\n", "            \n", "            if np.min(distances_to_piles) > patch_size:  # Far from any pile\n", "                # Extract points around this center\n", "                distances = np.linalg.norm(points[:, :2] - [center_x, center_y], axis=1)\n", "                patch_mask = distances < patch_size / 2\n", "                \n", "                if np.sum(patch_mask) >= 50:\n", "                    patch_points = points[patch_mask]\n", "                    \n", "                    # Center the patch\n", "                    patch_points = patch_points - patch_points.mean(axis=0)\n", "                    \n", "                    # Resample to fixed number of points\n", "                    if len(patch_points) >= num_points:\n", "                        indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "                    else:\n", "                        indices = np.random.choice(len(patch_points), num_points, replace=True)\n", "                    \n", "                    patch_points = patch_points[indices]\n", "                    \n", "                    X.append(patch_points)\n", "                    y.append(0)  # Background patch\n", "        \n", "        X = np.array(X)\n", "        y = np.array(y)\n", "        \n", "        print(f\"Created {len(X)} training patches:\")\n", "        print(f\"  Pile patches: {np.sum(y == 1)}\")\n", "        print(f\"  Background patches: {np.sum(y == 0)}\")\n", "        \n", "        return X, y\n", "    \n", "    print(\"Real data utilities implemented\")\n", "else:\n", "    print(\"Skipping real data utilities - PyTorch not available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model Training\n", "\n", "Let's train the PointNet model on synthetic data to demonstrate the training process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training utilities ready\n"]}], "source": ["if TORCH_AVAILABLE:\n", "    # Simple dataset class\n", "    class CSectionDataset(Dataset):\n", "        def __init__(self, X, y):\n", "            self.X = torch.FloatTensor(X)\n", "            self.y = torch.LongTensor(y)\n", "        \n", "        def __len__(self):\n", "            return len(self.X)\n", "        \n", "        def __getitem__(self, idx):\n", "            # Return [3, N] format for PointNet\n", "            return self.X[idx].transpose(0, 1), self.y[idx]\n", "    \n", "    # Training function\n", "    def train_model(model, train_loader, num_epochs=10):\n", "        \"\"\"Train the PointNet model\"\"\"\n", "        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)\n", "        criterion = nn.NLLLoss()\n", "        \n", "        model.train()\n", "        for epoch in range(num_epochs):\n", "            total_loss = 0\n", "            correct = 0\n", "            total = 0\n", "            \n", "            for batch_idx, (data, target) in enumerate(train_loader):\n", "                data, target = data.to(device), target.to(device)\n", "                \n", "                optimizer.zero_grad()\n", "                output = model(data)\n", "                loss = criterion(output, target)\n", "                loss.backward()\n", "                optimizer.step()\n", "                \n", "                total_loss += loss.item()\n", "                pred = output.argmax(dim=1)\n", "                correct += pred.eq(target).sum().item()\n", "                total += target.size(0)\n", "            \n", "            accuracy = 100. * correct / total\n", "            avg_loss = total_loss / len(train_loader)\n", "            print(f'Epoch {epoch+1}/{num_epochs}: Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%')\n", "        \n", "        return model\n", "    \n", "    print(\"Training utilities ready\")\n", "else:\n", "    print(\"Skipping training setup - PyTorch not available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Geometric-Based C-Section Pile Detection (Baseline)\n", "\n", "Let's keep the geometric approach as our baseline and comparison method."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Geometric C-section detector initialized\n"]}], "source": ["class GeometricCSectionDetector:\n", "    \"\"\"Geometric-based C-section pile detection using point cloud analysis.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # C-section pile characteristics\n", "        self.min_height = 1.5  # Minimum pile height above ground\n", "        self.max_height = 10.0  # Maximum reasonable pile height\n", "        self.typical_width = 0.15  # Typical C-section width (~15cm)\n", "        self.typical_depth = 0.08  # Typical C-section depth (~8cm)\n", "        self.width_tolerance = 0.08  # ±8cm tolerance\n", "        self.min_points_per_pile = 40  # Minimum points (C-sections have fewer points than I-sections)\n", "        \n", "    def detect_vertical_structures(self, points):\n", "        \"\"\"Detect vertical structures that could be C-section piles.\"\"\"\n", "        print(\"Detecting vertical structures...\")\n", "        \n", "        # Filter by height\n", "        z_min = points[:, 2].min()\n", "        height_mask = (points[:, 2] - z_min) >= self.min_height\n", "        elevated_points = points[height_mask]\n", "        \n", "        print(f\"Points above {self.min_height}m: {len(elevated_points):,}\")\n", "        \n", "        if len(elevated_points) < self.min_points_per_pile:\n", "            return []\n", "        \n", "        # Use smaller clustering radius for C-sections (they're thinner)\n", "        clustering = DBSCAN(eps=0.3, min_samples=self.min_points_per_pile)\n", "        cluster_labels = clustering.fit_predict(elevated_points[:, :2])\n", "        \n", "        # Extract clusters\n", "        unique_labels = set(cluster_labels)\n", "        unique_labels.discard(-1)  # Remove noise\n", "        \n", "        vertical_structures = []\n", "        for label in unique_labels:\n", "            cluster_mask = cluster_labels == label\n", "            cluster_points = elevated_points[cluster_mask]\n", "            \n", "            if len(cluster_points) >= self.min_points_per_pile:\n", "                vertical_structures.append(cluster_points)\n", "        \n", "        print(f\"Found {len(vertical_structures)} vertical structure candidates\")\n", "        return vertical_structures\n", "    \n", "    def analyze_c_section_characteristics(self, structure_points):\n", "        \"\"\"Analyze if structure has C-section characteristics.\"\"\"\n", "        # Calculate structure dimensions\n", "        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()\n", "        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()\n", "        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()\n", "        \n", "        # C-section characteristics:\n", "        # 1. Significant height (vertical structure)\n", "        # 2. Moderate width (C-section opening)\n", "        # 3. Smaller depth (C-section profile)\n", "        # 4. Asymmetric cross-section (C-shape)\n", "        \n", "        height_score = min(z_span / self.min_height, 1.0)\n", "        \n", "        # Check dimensions for C-section profile\n", "        max_horizontal = max(x_span, y_span)\n", "        min_horizontal = min(x_span, y_span)\n", "        \n", "        # C-sections are typically smaller than I-sections\n", "        width_score = 0.0\n", "        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):\n", "            width_score = 0.7\n", "        \n", "        # C-sections have moderate aspect ratio (not as elongated as I-sections)\n", "        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)\n", "        aspect_score = 0.0\n", "        if 1.5 <= aspect_ratio <= 4.0:  # C-sections less elongated than I-sections\n", "            aspect_score = 0.8\n", "        \n", "        # Analyze point distribution for C-shape characteristics\n", "        distribution_score = self.analyze_point_distribution(structure_points)\n", "        \n", "        # Combine scores (weight distribution more heavily for C-sections)\n", "        confidence = (height_score * 0.3 + width_score * 0.3 + \n", "                     aspect_score * 0.2 + distribution_score * 0.2)\n", "        \n", "        return {\n", "            'confidence': confidence,\n", "            'height': z_span,\n", "            'width': max_horizontal,\n", "            'depth': min_horizontal,\n", "            'aspect_ratio': aspect_ratio,\n", "            'point_count': len(structure_points),\n", "            'distribution_score': distribution_score\n", "        }\n", "    \n", "    def analyze_point_distribution(self, structure_points):\n", "        \"\"\"Analyze point distribution to detect C-shape characteristics.\"\"\"\n", "        if len(structure_points) < 20:\n", "            return 0.0\n", "        \n", "        # Project points to horizontal plane and analyze distribution\n", "        xy_points = structure_points[:, :2]\n", "        \n", "        # Center the points\n", "        centroid = xy_points.mean(axis=0)\n", "        centered_points = xy_points - centroid\n", "        \n", "        # Use PCA to find principal directions\n", "        pca = PCA(n_components=2)\n", "        pca.fit(centered_points)\n", "        \n", "        # Transform to principal component space\n", "        transformed_points = pca.transform(centered_points)\n", "        \n", "        # Analyze distribution along principal axes\n", "        pc1_std = np.std(transformed_points[:, 0])\n", "        pc2_std = np.std(transformed_points[:, 1])\n", "        \n", "        # C-sections should have asymmetric distribution\n", "        asymmetry_ratio = max(pc1_std, pc2_std) / (min(pc1_std, pc2_std) + 1e-6)\n", "        \n", "        # Score based on asymmetry (C-sections are more asymmetric than cylinders)\n", "        if asymmetry_ratio > 2.0:\n", "            return 0.8\n", "        elif asymmetry_ratio > 1.5:\n", "            return 0.6\n", "        else:\n", "            return 0.3\n", "    \n", "    def extract_pile_center(self, structure_points):\n", "        \"\"\"Extract pile center coordinates.\"\"\"\n", "        # Use centroid of bottom 20% of points\n", "        z_min = structure_points[:, 2].min()\n", "        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)\n", "        \n", "        base_mask = structure_points[:, 2] <= z_threshold\n", "        base_points = structure_points[base_mask]\n", "        \n", "        if len(base_points) > 0:\n", "            center = base_points.mean(axis=0)\n", "        else:\n", "            center = structure_points.mean(axis=0)\n", "        \n", "        return center\n", "    \n", "    def detect_c_section_piles(self, points):\n", "        \"\"\"Main detection pipeline.\"\"\"\n", "        print(\"Starting C-section pile detection...\")\n", "        \n", "        # Step 1: Find vertical structures\n", "        vertical_structures = self.detect_vertical_structures(points)\n", "        \n", "        if not vertical_structures:\n", "            print(\"No vertical structures found\")\n", "            return []\n", "        \n", "        # Step 2: Analyze each structure for C-section characteristics\n", "        detections = []\n", "        for i, structure in enumerate(vertical_structures):\n", "            analysis = self.analyze_c_section_characteristics(structure)\n", "            \n", "            if analysis['confidence'] >= confidence_threshold:\n", "                center = self.extract_pile_center(structure)\n", "                \n", "                detection = {\n", "                    'x': float(center[0]),\n", "                    'y': float(center[1]),\n", "                    'z': float(center[2]),\n", "                    'confidence': float(analysis['confidence']),\n", "                    'width': float(analysis['width']),\n", "                    'height': float(analysis['height']),\n", "                    'depth': float(analysis['depth']),\n", "                    'c_section_score': float(analysis['confidence']),\n", "                    'point_count': int(analysis['point_count']),\n", "                    'distribution_score': float(analysis['distribution_score']),\n", "                    'detection_method': 'geometric'\n", "                }\n", "                \n", "                detections.append(detection)\n", "        \n", "        print(f\"Detected {len(detections)} C-section pile candidates\")\n", "        return detections\n", "\n", "# Initialize detector\n", "detector = GeometricCSectionDetector()\n", "print(\"Geometric C-section detector initialized\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train PointNet Model\n", "\n", "Let's train a PointNet model on synthetic C-section data to demonstrate the complete pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==> TRAINING POINTNET MODEL ON REAL DATA\n", "Loaded 14460 pile coordinates from IFC\n", "\n", "Pile coordinate statistics:\n", "  X range: [435267.2, 436719.9]\n", "  Y range: [5010900.7, 5012462.4]\n", "  Z range: [157.1, 161.7]\n", "Creating training patches from real data...\n", "Point cloud: 1,359,240 points\n", "Pile locations: 14460 piles\n", "Created 4 training patches:\n", "  Pile patches: 4\n", "  Background patches: 0\n", "\n", "Training data shape: (4, 512, 3)\n", "Labels shape: (4,)\n", "Pile samples: 4\n", "Background samples: 0\n", "\n", "Model initialized on cpu\n", "\n", "Starting training on real data...\n", "Epoch 1/10: Loss: 0.4941, Accuracy: 100.00%\n", "Epoch 2/10: Loss: 0.2002, Accuracy: 100.00%\n", "Epoch 3/10: Loss: 0.0917, Accuracy: 100.00%\n", "Epoch 4/10: Loss: 0.0352, Accuracy: 100.00%\n", "Epoch 5/10: Loss: 0.0724, Accuracy: 100.00%\n", "Epoch 6/10: Loss: 0.0079, Accuracy: 100.00%\n", "Epoch 7/10: Loss: 0.0037, Accuracy: 100.00%\n", "Epoch 8/10: Loss: 0.0041, Accuracy: 100.00%\n", "Epoch 9/10: Loss: 0.0139, Accuracy: 100.00%\n", "Epoch 10/10: Loss: 0.0019, Accuracy: 100.00%\n", "\n", "Training completed!\n", "\n", "Test Results on Real Data:\n", "  Sample 1: True=<PERSON><PERSON>, Predicted=<PERSON><PERSON> (conf: 0.719)\n", "  Sample 2: True=<PERSON><PERSON>, Predicted=<PERSON><PERSON> (conf: 0.724)\n", "  Sample 3: True=<PERSON><PERSON>, Predicted=<PERSON><PERSON> (conf: 0.722)\n", "  Sample 4: True=<PERSON><PERSON>, Predicted=<PERSON><PERSON> (conf: 0.718)\n"]}], "source": ["if TORCH_AVAILABLE:\n", "    print(\"==> TRAINING POINTNET MODEL ON REAL DATA\")\n", "    \n", "    # Load pile coordinates from IFC metadata\n", "    pile_coords, pile_df = load_pile_coordinates(site_name)\n", "    \n", "    if pile_coords is not None:\n", "        print(f\"\\nPile coordinate statistics:\")\n", "        print(f\"  X range: [{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}]\")\n", "        print(f\"  Y range: [{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]\")\n", "        print(f\"  Z range: [{pile_coords[:, 2].min():.1f}, {pile_coords[:, 2].max():.1f}]\")\n", "        \n", "        # Create training patches from real data\n", "        X_train, y_train = create_training_patches(points, pile_coords, patch_size=3.0, num_points=512)\n", "        \n", "        if len(X_train) > 0:\n", "            print(f\"\\nTraining data shape: {X_train.shape}\")\n", "            print(f\"Labels shape: {y_train.shape}\")\n", "            print(f\"Pile samples: {np.sum(y_train == 1)}\")\n", "            print(f\"Background samples: {np.sum(y_train == 0)}\")\n", "            \n", "            # Create dataset and dataloader\n", "            train_dataset = CSectionDataset(X_train, y_train)\n", "            train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)\n", "            \n", "            # Initialize model\n", "            model = SimplePointNet(num_points=512, num_classes=2)\n", "            model.to(device)\n", "            print(f\"\\nModel initialized on {device}\")\n", "            \n", "            # Train the model\n", "            print(\"\\nStarting training on real data...\")\n", "            trained_model = train_model(model, train_loader, num_epochs=10)\n", "            \n", "            print(\"\\nTraining completed!\")\n", "            \n", "            # Test on a few samples\n", "            model.eval()\n", "            with torch.no_grad():\n", "                test_samples = min(5, len(X_train))\n", "                test_data = torch.FloatTensor(X_train[:test_samples]).transpose(1, 2).to(device)\n", "                test_labels = y_train[:test_samples]\n", "                \n", "                outputs = model(test_data)\n", "                predictions = outputs.argmax(dim=1).cpu().numpy()\n", "                probabilities = torch.exp(outputs).cpu().numpy()\n", "                \n", "                print(f\"\\nTest Results on Real Data:\")\n", "                for i in range(test_samples):\n", "                    label_name = '<PERSON><PERSON>' if test_labels[i] == 1 else 'Background'\n", "                    pred_name = '<PERSON><PERSON>' if predictions[i] == 1 else 'Background'\n", "                    confidence = probabilities[i][predictions[i]]\n", "                    print(f\"  Sample {i+1}: True={label_name}, Predicted={pred_name} (conf: {confidence:.3f})\")\n", "            \n", "            # Store results for later use\n", "            pointnet_results = {\n", "                'model': trained_model,\n", "                'training_data_size': len(X_train),\n", "                'pile_coordinates': pile_coords,\n", "                'detections': []  # Would be filled during inference\n", "            }\n", "            \n", "        else:\n", "            print(\"No training patches could be created\")\n", "            pointnet_results = {'detections': [], 'model': None}\n", "    else:\n", "        print(\"No pile coordinates available for training\")\n", "        pointnet_results = {'detections': [], 'model': None}\n", "    \n", "else:\n", "    print(\"Skipping PointNet training - PyTorch not available\")\n", "    pointnet_results = {'detections': [], 'model': None}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PointNet Inference on Real Data\n", "\n", "Now let's use the trained model to detect piles in the point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==> POINTNET INFERENCE ON REAL DATA\n", "Point cloud bounds: X[435307.2, 436760.1] Y[5010836.8, 5012398.5]\n", "Area: 2268883.2 m²\n", "Estimated patches: 565500 (725 x 780)\n", "WARNING: Too many patches! Using reduced settings...\n", "Reduced to: 141180 patches\n", "Actually generated 15494 patches for inference\n", "  Processed 0/15494 patches (0.0%)\n", "  Processed 32/15494 patches (0.2%)\n", "  Processed 64/15494 patches (0.4%)\n", "  Processed 96/15494 patches (0.6%)\n", "  Processed 128/15494 patches (0.8%)\n", "  Processed 160/15494 patches (1.0%)\n", "  Processed 192/15494 patches (1.2%)\n", "  Processed 224/15494 patches (1.4%)\n", "  Processed 256/15494 patches (1.7%)\n", "  Processed 288/15494 patches (1.9%)\n", "  Processed 320/15494 patches (2.1%)\n", "  Processed 352/15494 patches (2.3%)\n", "  Processed 384/15494 patches (2.5%)\n", "  Processed 416/15494 patches (2.7%)\n", "  Processed 448/15494 patches (2.9%)\n", "  Processed 480/15494 patches (3.1%)\n", "  Processed 512/15494 patches (3.3%)\n", "  Processed 544/15494 patches (3.5%)\n", "  Processed 576/15494 patches (3.7%)\n", "  Processed 608/15494 patches (3.9%)\n", "  Processed 640/15494 patches (4.1%)\n", "  Processed 672/15494 patches (4.3%)\n", "  Processed 704/15494 patches (4.5%)\n", "  Processed 736/15494 patches (4.8%)\n", "  Processed 768/15494 patches (5.0%)\n", "  Processed 800/15494 patches (5.2%)\n", "  Processed 832/15494 patches (5.4%)\n", "  Processed 864/15494 patches (5.6%)\n", "  Processed 896/15494 patches (5.8%)\n", "  Processed 928/15494 patches (6.0%)\n", "  Processed 960/15494 patches (6.2%)\n", "  Processed 992/15494 patches (6.4%)\n", "  Processed 1024/15494 patches (6.6%)\n", "  Processed 1056/15494 patches (6.8%)\n", "  Processed 1088/15494 patches (7.0%)\n", "  Processed 1120/15494 patches (7.2%)\n", "  Processed 1152/15494 patches (7.4%)\n", "  Processed 1184/15494 patches (7.6%)\n", "  Processed 1216/15494 patches (7.8%)\n", "  Processed 1248/15494 patches (8.1%)\n", "  Processed 1280/15494 patches (8.3%)\n", "  Processed 1312/15494 patches (8.5%)\n", "  Processed 1344/15494 patches (8.7%)\n", "  Processed 1376/15494 patches (8.9%)\n", "  Processed 1408/15494 patches (9.1%)\n", "  Processed 1440/15494 patches (9.3%)\n", "  Processed 1472/15494 patches (9.5%)\n", "  Processed 1504/15494 patches (9.7%)\n", "  Processed 1536/15494 patches (9.9%)\n", "  Processed 1568/15494 patches (10.1%)\n", "  Processed 1600/15494 patches (10.3%)\n", "  Processed 1632/15494 patches (10.5%)\n", "  Processed 1664/15494 patches (10.7%)\n", "  Processed 1696/15494 patches (10.9%)\n", "  Processed 1728/15494 patches (11.2%)\n", "  Processed 1760/15494 patches (11.4%)\n", "  Processed 1792/15494 patches (11.6%)\n", "  Processed 1824/15494 patches (11.8%)\n", "  Processed 1856/15494 patches (12.0%)\n", "  Processed 1888/15494 patches (12.2%)\n", "  Processed 1920/15494 patches (12.4%)\n", "  Processed 1952/15494 patches (12.6%)\n", "  Processed 1984/15494 patches (12.8%)\n", "  Processed 2016/15494 patches (13.0%)\n", "  Processed 2048/15494 patches (13.2%)\n", "  Processed 2080/15494 patches (13.4%)\n", "  Processed 2112/15494 patches (13.6%)\n", "  Processed 2144/15494 patches (13.8%)\n", "  Processed 2176/15494 patches (14.0%)\n", "  Processed 2208/15494 patches (14.3%)\n", "  Processed 2240/15494 patches (14.5%)\n", "  Processed 2272/15494 patches (14.7%)\n", "  Processed 2304/15494 patches (14.9%)\n", "  Processed 2336/15494 patches (15.1%)\n", "  Processed 2368/15494 patches (15.3%)\n", "  Processed 2400/15494 patches (15.5%)\n", "  Processed 2432/15494 patches (15.7%)\n", "  Processed 2464/15494 patches (15.9%)\n", "  Processed 2496/15494 patches (16.1%)\n", "  Processed 2528/15494 patches (16.3%)\n", "  Processed 2560/15494 patches (16.5%)\n", "  Processed 2592/15494 patches (16.7%)\n", "  Processed 2624/15494 patches (16.9%)\n", "  Processed 2656/15494 patches (17.1%)\n", "  Processed 2688/15494 patches (17.3%)\n", "  Processed 2720/15494 patches (17.6%)\n", "  Processed 2752/15494 patches (17.8%)\n", "  Processed 2784/15494 patches (18.0%)\n", "  Processed 2816/15494 patches (18.2%)\n", "  Processed 2848/15494 patches (18.4%)\n", "  Processed 2880/15494 patches (18.6%)\n", "  Processed 2912/15494 patches (18.8%)\n", "  Processed 2944/15494 patches (19.0%)\n", "  Processed 2976/15494 patches (19.2%)\n", "  Processed 3008/15494 patches (19.4%)\n", "  Processed 3040/15494 patches (19.6%)\n", "  Processed 3072/15494 patches (19.8%)\n", "  Processed 3104/15494 patches (20.0%)\n", "  Processed 3136/15494 patches (20.2%)\n", "  Processed 3168/15494 patches (20.4%)\n", "  Processed 3200/15494 patches (20.7%)\n", "  Processed 3232/15494 patches (20.9%)\n", "  Processed 3264/15494 patches (21.1%)\n", "  Processed 3296/15494 patches (21.3%)\n", "  Processed 3328/15494 patches (21.5%)\n", "  Processed 3360/15494 patches (21.7%)\n", "  Processed 3392/15494 patches (21.9%)\n", "  Processed 3424/15494 patches (22.1%)\n", "  Processed 3456/15494 patches (22.3%)\n", "  Processed 3488/15494 patches (22.5%)\n", "  Processed 3520/15494 patches (22.7%)\n", "  Processed 3552/15494 patches (22.9%)\n", "  Processed 3584/15494 patches (23.1%)\n", "  Processed 3616/15494 patches (23.3%)\n", "  Processed 3648/15494 patches (23.5%)\n", "  Processed 3680/15494 patches (23.8%)\n", "  Processed 3712/15494 patches (24.0%)\n", "  Processed 3744/15494 patches (24.2%)\n", "  Processed 3776/15494 patches (24.4%)\n", "  Processed 3808/15494 patches (24.6%)\n", "  Processed 3840/15494 patches (24.8%)\n", "  Processed 3872/15494 patches (25.0%)\n", "  Processed 3904/15494 patches (25.2%)\n", "  Processed 3936/15494 patches (25.4%)\n", "  Processed 3968/15494 patches (25.6%)\n", "  Processed 4000/15494 patches (25.8%)\n", "  Processed 4032/15494 patches (26.0%)\n", "  Processed 4064/15494 patches (26.2%)\n", "  Processed 4096/15494 patches (26.4%)\n", "  Processed 4128/15494 patches (26.6%)\n", "  Processed 4160/15494 patches (26.8%)\n", "  Processed 4192/15494 patches (27.1%)\n", "  Processed 4224/15494 patches (27.3%)\n", "  Processed 4256/15494 patches (27.5%)\n", "  Processed 4288/15494 patches (27.7%)\n", "  Processed 4320/15494 patches (27.9%)\n", "  Processed 4352/15494 patches (28.1%)\n", "  Processed 4384/15494 patches (28.3%)\n", "  Processed 4416/15494 patches (28.5%)\n", "  Processed 4448/15494 patches (28.7%)\n", "  Processed 4480/15494 patches (28.9%)\n", "  Processed 4512/15494 patches (29.1%)\n", "  Processed 4544/15494 patches (29.3%)\n", "  Processed 4576/15494 patches (29.5%)\n", "  Processed 4608/15494 patches (29.7%)\n", "  Processed 4640/15494 patches (29.9%)\n", "  Processed 4672/15494 patches (30.2%)\n", "  Processed 4704/15494 patches (30.4%)\n", "  Processed 4736/15494 patches (30.6%)\n", "  Processed 4768/15494 patches (30.8%)\n", "  Processed 4800/15494 patches (31.0%)\n", "  Processed 4832/15494 patches (31.2%)\n", "  Processed 4864/15494 patches (31.4%)\n", "  Processed 4896/15494 patches (31.6%)\n", "  Processed 4928/15494 patches (31.8%)\n", "  Processed 4960/15494 patches (32.0%)\n", "  Processed 4992/15494 patches (32.2%)\n", "  Processed 5024/15494 patches (32.4%)\n", "  Processed 5056/15494 patches (32.6%)\n", "  Processed 5088/15494 patches (32.8%)\n", "  Processed 5120/15494 patches (33.0%)\n", "  Processed 5152/15494 patches (33.3%)\n", "  Processed 5184/15494 patches (33.5%)\n", "  Processed 5216/15494 patches (33.7%)\n", "  Processed 5248/15494 patches (33.9%)\n", "  Processed 5280/15494 patches (34.1%)\n", "  Processed 5312/15494 patches (34.3%)\n", "  Processed 5344/15494 patches (34.5%)\n", "  Processed 5376/15494 patches (34.7%)\n", "  Processed 5408/15494 patches (34.9%)\n", "  Processed 5440/15494 patches (35.1%)\n", "  Processed 5472/15494 patches (35.3%)\n", "  Processed 5504/15494 patches (35.5%)\n", "  Processed 5536/15494 patches (35.7%)\n", "  Processed 5568/15494 patches (35.9%)\n", "  Processed 5600/15494 patches (36.1%)\n", "  Processed 5632/15494 patches (36.3%)\n", "  Processed 5664/15494 patches (36.6%)\n", "  Processed 5696/15494 patches (36.8%)\n", "  Processed 5728/15494 patches (37.0%)\n", "  Processed 5760/15494 patches (37.2%)\n", "  Processed 5792/15494 patches (37.4%)\n", "  Processed 5824/15494 patches (37.6%)\n", "  Processed 5856/15494 patches (37.8%)\n", "  Processed 5888/15494 patches (38.0%)\n", "  Processed 5920/15494 patches (38.2%)\n", "  Processed 5952/15494 patches (38.4%)\n", "  Processed 5984/15494 patches (38.6%)\n", "  Processed 6016/15494 patches (38.8%)\n", "  Processed 6048/15494 patches (39.0%)\n", "  Processed 6080/15494 patches (39.2%)\n", "  Processed 6112/15494 patches (39.4%)\n", "  Processed 6144/15494 patches (39.7%)\n", "  Processed 6176/15494 patches (39.9%)\n", "  Processed 6208/15494 patches (40.1%)\n", "  Processed 6240/15494 patches (40.3%)\n", "  Processed 6272/15494 patches (40.5%)\n", "  Processed 6304/15494 patches (40.7%)\n", "  Processed 6336/15494 patches (40.9%)\n", "  Processed 6368/15494 patches (41.1%)\n", "  Processed 6400/15494 patches (41.3%)\n", "  Processed 6432/15494 patches (41.5%)\n", "  Processed 6464/15494 patches (41.7%)\n", "  Processed 6496/15494 patches (41.9%)\n", "  Processed 6528/15494 patches (42.1%)\n", "  Processed 6560/15494 patches (42.3%)\n", "  Processed 6592/15494 patches (42.5%)\n", "  Processed 6624/15494 patches (42.8%)\n", "  Processed 6656/15494 patches (43.0%)\n", "  Processed 6688/15494 patches (43.2%)\n", "  Processed 6720/15494 patches (43.4%)\n", "  Processed 6752/15494 patches (43.6%)\n", "  Processed 6784/15494 patches (43.8%)\n", "  Processed 6816/15494 patches (44.0%)\n", "  Processed 6848/15494 patches (44.2%)\n", "  Processed 6880/15494 patches (44.4%)\n", "  Processed 6912/15494 patches (44.6%)\n", "  Processed 6944/15494 patches (44.8%)\n", "  Processed 6976/15494 patches (45.0%)\n", "  Processed 7008/15494 patches (45.2%)\n", "  Processed 7040/15494 patches (45.4%)\n", "  Processed 7072/15494 patches (45.6%)\n", "  Processed 7104/15494 patches (45.9%)\n", "  Processed 7136/15494 patches (46.1%)\n", "  Processed 7168/15494 patches (46.3%)\n", "  Processed 7200/15494 patches (46.5%)\n", "  Processed 7232/15494 patches (46.7%)\n", "  Processed 7264/15494 patches (46.9%)\n", "  Processed 7296/15494 patches (47.1%)\n", "  Processed 7328/15494 patches (47.3%)\n", "  Processed 7360/15494 patches (47.5%)\n", "  Processed 7392/15494 patches (47.7%)\n", "  Processed 7424/15494 patches (47.9%)\n", "  Processed 7456/15494 patches (48.1%)\n", "  Processed 7488/15494 patches (48.3%)\n", "  Processed 7520/15494 patches (48.5%)\n", "  Processed 7552/15494 patches (48.7%)\n", "  Processed 7584/15494 patches (48.9%)\n", "  Processed 7616/15494 patches (49.2%)\n", "  Processed 7648/15494 patches (49.4%)\n", "  Processed 7680/15494 patches (49.6%)\n", "  Processed 7712/15494 patches (49.8%)\n", "  Processed 7744/15494 patches (50.0%)\n", "  Processed 7776/15494 patches (50.2%)\n", "  Processed 7808/15494 patches (50.4%)\n", "  Processed 7840/15494 patches (50.6%)\n", "  Processed 7872/15494 patches (50.8%)\n", "  Processed 7904/15494 patches (51.0%)\n", "  Processed 7936/15494 patches (51.2%)\n", "  Processed 7968/15494 patches (51.4%)\n", "  Processed 8000/15494 patches (51.6%)\n", "  Processed 8032/15494 patches (51.8%)\n", "  Processed 8064/15494 patches (52.0%)\n", "  Processed 8096/15494 patches (52.3%)\n", "  Processed 8128/15494 patches (52.5%)\n", "  Processed 8160/15494 patches (52.7%)\n", "  Processed 8192/15494 patches (52.9%)\n", "  Processed 8224/15494 patches (53.1%)\n", "  Processed 8256/15494 patches (53.3%)\n", "  Processed 8288/15494 patches (53.5%)\n", "  Processed 8320/15494 patches (53.7%)\n", "  Processed 8352/15494 patches (53.9%)\n", "  Processed 8384/15494 patches (54.1%)\n", "  Processed 8416/15494 patches (54.3%)\n", "  Processed 8448/15494 patches (54.5%)\n", "  Processed 8480/15494 patches (54.7%)\n", "  Processed 8512/15494 patches (54.9%)\n", "  Processed 8544/15494 patches (55.1%)\n", "  Processed 8576/15494 patches (55.4%)\n", "  Processed 8608/15494 patches (55.6%)\n", "  Processed 8640/15494 patches (55.8%)\n", "  Processed 8672/15494 patches (56.0%)\n", "  Processed 8704/15494 patches (56.2%)\n", "  Processed 8736/15494 patches (56.4%)\n", "  Processed 8768/15494 patches (56.6%)\n", "  Processed 8800/15494 patches (56.8%)\n", "  Processed 8832/15494 patches (57.0%)\n", "  Processed 8864/15494 patches (57.2%)\n", "  Processed 8896/15494 patches (57.4%)\n", "  Processed 8928/15494 patches (57.6%)\n", "  Processed 8960/15494 patches (57.8%)\n", "  Processed 8992/15494 patches (58.0%)\n", "  Processed 9024/15494 patches (58.2%)\n", "  Processed 9056/15494 patches (58.4%)\n", "  Processed 9088/15494 patches (58.7%)\n", "  Processed 9120/15494 patches (58.9%)\n", "  Processed 9152/15494 patches (59.1%)\n", "  Processed 9184/15494 patches (59.3%)\n", "  Processed 9216/15494 patches (59.5%)\n", "  Processed 9248/15494 patches (59.7%)\n", "  Processed 9280/15494 patches (59.9%)\n", "  Processed 9312/15494 patches (60.1%)\n", "  Processed 9344/15494 patches (60.3%)\n", "  Processed 9376/15494 patches (60.5%)\n", "  Processed 9408/15494 patches (60.7%)\n", "  Processed 9440/15494 patches (60.9%)\n", "  Processed 9472/15494 patches (61.1%)\n", "  Processed 9504/15494 patches (61.3%)\n", "  Processed 9536/15494 patches (61.5%)\n", "  Processed 9568/15494 patches (61.8%)\n", "  Processed 9600/15494 patches (62.0%)\n", "  Processed 9632/15494 patches (62.2%)\n", "  Processed 9664/15494 patches (62.4%)\n", "  Processed 9696/15494 patches (62.6%)\n", "  Processed 9728/15494 patches (62.8%)\n", "  Processed 9760/15494 patches (63.0%)\n", "  Processed 9792/15494 patches (63.2%)\n", "  Processed 9824/15494 patches (63.4%)\n", "  Processed 9856/15494 patches (63.6%)\n", "  Processed 9888/15494 patches (63.8%)\n", "  Processed 9920/15494 patches (64.0%)\n", "  Processed 9952/15494 patches (64.2%)\n", "  Processed 9984/15494 patches (64.4%)\n", "  Processed 10016/15494 patches (64.6%)\n", "  Processed 10048/15494 patches (64.9%)\n", "  Processed 10080/15494 patches (65.1%)\n", "  Processed 10112/15494 patches (65.3%)\n", "  Processed 10144/15494 patches (65.5%)\n", "  Processed 10176/15494 patches (65.7%)\n", "  Processed 10208/15494 patches (65.9%)\n", "  Processed 10240/15494 patches (66.1%)\n", "  Processed 10272/15494 patches (66.3%)\n", "  Processed 10304/15494 patches (66.5%)\n", "  Processed 10336/15494 patches (66.7%)\n", "  Processed 10368/15494 patches (66.9%)\n", "  Processed 10400/15494 patches (67.1%)\n", "  Processed 10432/15494 patches (67.3%)\n", "  Processed 10464/15494 patches (67.5%)\n", "  Processed 10496/15494 patches (67.7%)\n", "  Processed 10528/15494 patches (67.9%)\n", "  Processed 10560/15494 patches (68.2%)\n", "  Processed 10592/15494 patches (68.4%)\n", "  Processed 10624/15494 patches (68.6%)\n", "  Processed 10656/15494 patches (68.8%)\n", "  Processed 10688/15494 patches (69.0%)\n", "  Processed 10720/15494 patches (69.2%)\n", "  Processed 10752/15494 patches (69.4%)\n", "  Processed 10784/15494 patches (69.6%)\n", "  Processed 10816/15494 patches (69.8%)\n", "  Processed 10848/15494 patches (70.0%)\n", "  Processed 10880/15494 patches (70.2%)\n", "  Processed 10912/15494 patches (70.4%)\n", "  Processed 10944/15494 patches (70.6%)\n", "  Processed 10976/15494 patches (70.8%)\n", "  Processed 11008/15494 patches (71.0%)\n", "  Processed 11040/15494 patches (71.3%)\n", "  Processed 11072/15494 patches (71.5%)\n", "  Processed 11104/15494 patches (71.7%)\n", "  Processed 11136/15494 patches (71.9%)\n", "  Processed 11168/15494 patches (72.1%)\n", "  Processed 11200/15494 patches (72.3%)\n", "  Processed 11232/15494 patches (72.5%)\n", "  Processed 11264/15494 patches (72.7%)\n", "  Processed 11296/15494 patches (72.9%)\n", "  Processed 11328/15494 patches (73.1%)\n", "  Processed 11360/15494 patches (73.3%)\n", "  Processed 11392/15494 patches (73.5%)\n", "  Processed 11424/15494 patches (73.7%)\n", "  Processed 11456/15494 patches (73.9%)\n", "  Processed 11488/15494 patches (74.1%)\n", "  Processed 11520/15494 patches (74.4%)\n", "  Processed 11552/15494 patches (74.6%)\n", "  Processed 11584/15494 patches (74.8%)\n", "  Processed 11616/15494 patches (75.0%)\n", "  Processed 11648/15494 patches (75.2%)\n", "  Processed 11680/15494 patches (75.4%)\n", "  Processed 11712/15494 patches (75.6%)\n", "  Processed 11744/15494 patches (75.8%)\n", "  Processed 11776/15494 patches (76.0%)\n", "  Processed 11808/15494 patches (76.2%)\n", "  Processed 11840/15494 patches (76.4%)\n", "  Processed 11872/15494 patches (76.6%)\n", "  Processed 11904/15494 patches (76.8%)\n", "  Processed 11936/15494 patches (77.0%)\n", "  Processed 11968/15494 patches (77.2%)\n", "  Processed 12000/15494 patches (77.4%)\n", "  Processed 12032/15494 patches (77.7%)\n", "  Processed 12064/15494 patches (77.9%)\n", "  Processed 12096/15494 patches (78.1%)\n", "  Processed 12128/15494 patches (78.3%)\n", "  Processed 12160/15494 patches (78.5%)\n", "  Processed 12192/15494 patches (78.7%)\n", "  Processed 12224/15494 patches (78.9%)\n", "  Processed 12256/15494 patches (79.1%)\n", "  Processed 12288/15494 patches (79.3%)\n", "  Processed 12320/15494 patches (79.5%)\n", "  Processed 12352/15494 patches (79.7%)\n", "  Processed 12384/15494 patches (79.9%)\n", "  Processed 12416/15494 patches (80.1%)\n", "  Processed 12448/15494 patches (80.3%)\n", "  Processed 12480/15494 patches (80.5%)\n", "  Processed 12512/15494 patches (80.8%)\n", "  Processed 12544/15494 patches (81.0%)\n", "  Processed 12576/15494 patches (81.2%)\n", "  Processed 12608/15494 patches (81.4%)\n", "  Processed 12640/15494 patches (81.6%)\n", "  Processed 12672/15494 patches (81.8%)\n", "  Processed 12704/15494 patches (82.0%)\n", "  Processed 12736/15494 patches (82.2%)\n", "  Processed 12768/15494 patches (82.4%)\n", "  Processed 12800/15494 patches (82.6%)\n", "  Processed 12832/15494 patches (82.8%)\n", "  Processed 12864/15494 patches (83.0%)\n", "  Processed 12896/15494 patches (83.2%)\n", "  Processed 12928/15494 patches (83.4%)\n", "  Processed 12960/15494 patches (83.6%)\n", "  Processed 12992/15494 patches (83.9%)\n", "  Processed 13024/15494 patches (84.1%)\n", "  Processed 13056/15494 patches (84.3%)\n", "  Processed 13088/15494 patches (84.5%)\n", "  Processed 13120/15494 patches (84.7%)\n", "  Processed 13152/15494 patches (84.9%)\n", "  Processed 13184/15494 patches (85.1%)\n", "  Processed 13216/15494 patches (85.3%)\n", "  Processed 13248/15494 patches (85.5%)\n", "  Processed 13280/15494 patches (85.7%)\n", "  Processed 13312/15494 patches (85.9%)\n", "  Processed 13344/15494 patches (86.1%)\n", "  Processed 13376/15494 patches (86.3%)\n", "  Processed 13408/15494 patches (86.5%)\n", "  Processed 13440/15494 patches (86.7%)\n", "  Processed 13472/15494 patches (86.9%)\n", "  Processed 13504/15494 patches (87.2%)\n", "  Processed 13536/15494 patches (87.4%)\n", "  Processed 13568/15494 patches (87.6%)\n", "  Processed 13600/15494 patches (87.8%)\n", "  Processed 13632/15494 patches (88.0%)\n", "  Processed 13664/15494 patches (88.2%)\n", "  Processed 13696/15494 patches (88.4%)\n", "  Processed 13728/15494 patches (88.6%)\n", "  Processed 13760/15494 patches (88.8%)\n", "  Processed 13792/15494 patches (89.0%)\n", "  Processed 13824/15494 patches (89.2%)\n", "  Processed 13856/15494 patches (89.4%)\n", "  Processed 13888/15494 patches (89.6%)\n", "  Processed 13920/15494 patches (89.8%)\n", "  Processed 13952/15494 patches (90.0%)\n", "  Processed 13984/15494 patches (90.3%)\n", "  Processed 14016/15494 patches (90.5%)\n", "  Processed 14048/15494 patches (90.7%)\n", "  Processed 14080/15494 patches (90.9%)\n", "  Processed 14112/15494 patches (91.1%)\n", "  Processed 14144/15494 patches (91.3%)\n", "  Processed 14176/15494 patches (91.5%)\n", "  Processed 14208/15494 patches (91.7%)\n", "  Processed 14240/15494 patches (91.9%)\n", "  Processed 14272/15494 patches (92.1%)\n", "  Processed 14304/15494 patches (92.3%)\n", "  Processed 14336/15494 patches (92.5%)\n", "  Processed 14368/15494 patches (92.7%)\n", "  Processed 14400/15494 patches (92.9%)\n", "  Processed 14432/15494 patches (93.1%)\n", "  Processed 14464/15494 patches (93.4%)\n", "  Processed 14496/15494 patches (93.6%)\n", "  Processed 14528/15494 patches (93.8%)\n", "  Processed 14560/15494 patches (94.0%)\n", "  Processed 14592/15494 patches (94.2%)\n", "  Processed 14624/15494 patches (94.4%)\n", "  Processed 14656/15494 patches (94.6%)\n", "  Processed 14688/15494 patches (94.8%)\n", "  Processed 14720/15494 patches (95.0%)\n", "  Processed 14752/15494 patches (95.2%)\n", "  Processed 14784/15494 patches (95.4%)\n", "  Processed 14816/15494 patches (95.6%)\n", "  Processed 14848/15494 patches (95.8%)\n", "  Processed 14880/15494 patches (96.0%)\n", "  Processed 14912/15494 patches (96.2%)\n", "  Processed 14944/15494 patches (96.5%)\n", "  Processed 14976/15494 patches (96.7%)\n", "  Processed 15008/15494 patches (96.9%)\n", "  Processed 15040/15494 patches (97.1%)\n", "  Processed 15072/15494 patches (97.3%)\n", "  Processed 15104/15494 patches (97.5%)\n", "  Processed 15136/15494 patches (97.7%)\n", "  Processed 15168/15494 patches (97.9%)\n", "  Processed 15200/15494 patches (98.1%)\n", "  Processed 15232/15494 patches (98.3%)\n", "  Processed 15264/15494 patches (98.5%)\n", "  Processed 15296/15494 patches (98.7%)\n", "  Processed 15328/15494 patches (98.9%)\n", "  Processed 15360/15494 patches (99.1%)\n", "  Processed 15392/15494 patches (99.3%)\n", "  Processed 15424/15494 patches (99.5%)\n", "  Processed 15456/15494 patches (99.8%)\n", "  Processed 15488/15494 patches (100.0%)\n", "\n", "PointNet Inference Results:\n", "  Total patches: 15494\n", "  High confidence patches: 15493\n", "  Mean pile score: 0.728\n", "  Max pile score: 0.809\n", "  Final PointNet detections: 15493\n"]}], "source": ["if TORCH_AVAILABLE and pointnet_results.get('model') is not None:\n", "    print(\"==> POINTNET INFERENCE ON REAL DATA\")\n", "    \n", "    model = pointnet_results['model']\n", "    model.eval()\n", "    \n", "    # Generate patches for inference across the entire point cloud\n", "    def generate_inference_patches(points, patch_size=3.0, stride=2.0, num_points=512):\n", "        \"\"\"Generate patches for inference across the point cloud\"\"\"\n", "        x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "        y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "        \n", "        patches = []\n", "        centers = []\n", "        \n", "        x = x_min\n", "        while x < x_max - patch_size:\n", "            y = y_min\n", "            while y < y_max - patch_size:\n", "                center_x = x + patch_size / 2\n", "                center_y = y + patch_size / 2\n", "                \n", "                # Extract points in this patch\n", "                distances = np.linalg.norm(points[:, :2] - [center_x, center_y], axis=1)\n", "                patch_mask = distances < patch_size / 2\n", "                \n", "                if np.sum(patch_mask) >= 50:  # Minimum points\n", "                    patch_points = points[patch_mask]\n", "                    \n", "                    # Center the patch\n", "                    patch_points = patch_points - patch_points.mean(axis=0)\n", "                    \n", "                    # Resample to fixed number of points\n", "                    if len(patch_points) >= num_points:\n", "                        indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "                    else:\n", "                        indices = np.random.choice(len(patch_points), num_points, replace=True)\n", "                    \n", "                    patch_points = patch_points[indices]\n", "                    patches.append(patch_points)\n", "                    centers.append([center_x, center_y])\n", "                \n", "                y += stride\n", "            x += stride\n", "        \n", "        return np.array(patches), np.array(centers)\n", "    \n", "    # DEBUGGING: Check point cloud size first\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    area = (x_max - x_min) * (y_max - y_min)\n", "    \n", "    print(f\"Point cloud bounds: X[{x_min:.1f}, {x_max:.1f}] Y[{y_min:.1f}, {y_max:.1f}]\")\n", "    print(f\"Area: {area:.1f} m²\")\n", "    \n", "    # Estimate patches with current settings\n", "    patch_size, stride = 3.0, 2.0\n", "    x_patches = int((x_max - x_min - patch_size) / stride) + 1\n", "    y_patches = int((y_max - y_min - patch_size) / stride) + 1\n", "    estimated_patches = x_patches * y_patches\n", "    \n", "    print(f\"Estimated patches: {estimated_patches} ({x_patches} x {y_patches})\")\n", "    \n", "    if estimated_patches > 500:\n", "        print(\"WARNING: Too many patches! Using reduced settings...\")\n", "        patch_size, stride = 5.0, 4.0  # Larger patches, bigger stride\n", "        x_patches = int((x_max - x_min - patch_size) / stride) + 1\n", "        y_patches = int((y_max - y_min - patch_size) / stride) + 1\n", "        estimated_patches = x_patches * y_patches\n", "        print(f\"Reduced to: {estimated_patches} patches\")\n", "    \n", "    # Generate inference patches with optimized settings\n", "    inference_patches, patch_centers = generate_inference_patches(points, patch_size=patch_size, stride=stride)\n", "    \n", "    print(f\"Actually generated {len(inference_patches)} patches for inference\")\n", "    \n", "    if len(inference_patches) > 0:\n", "        # Run inference\n", "        pile_scores = []\n", "        batch_size = 16\n", "        \n", "        with torch.no_grad():\n", "            for i in range(0, len(inference_patches), batch_size):\n", "                batch_patches = inference_patches[i:i+batch_size]\n", "                batch_tensor = torch.FloatTensor(batch_patches).transpose(1, 2).to(device)\n", "                \n", "                outputs = model(batch_tensor)\n", "                probabilities = torch.exp(outputs).cpu().numpy()\n", "                \n", "                # Get pile probabilities (class 1)\n", "                batch_pile_scores = probabilities[:, 1]\n", "                pile_scores.extend(batch_pile_scores)\n", "                \n", "                # More frequent logging\n", "                if i % (batch_size * 2) == 0:  # Log every 32 patches instead of 160\n", "                    print(f\"  Processed {i}/{len(inference_patches)} patches ({100*i/len(inference_patches):.1f}%)\")\n", "        \n", "        pile_scores = np.array(pile_scores)\n", "        \n", "        # Filter high-confidence detections\n", "        confidence_threshold = 0.7\n", "        high_conf_mask = pile_scores > confidence_threshold\n", "        \n", "        print(f\"\\nPointNet Inference Results:\")\n", "        print(f\"  Total patches: {len(inference_patches)}\")\n", "        print(f\"  High confidence patches: {np.sum(high_conf_mask)}\")\n", "        print(f\"  Mean pile score: {np.mean(pile_scores):.3f}\")\n", "        print(f\"  Max pile score: {np.max(pile_scores):.3f}\")\n", "        \n", "        # Convert to pile detections\n", "        pointnet_detections = []\n", "        if np.sum(high_conf_mask) > 0:\n", "            high_conf_centers = patch_centers[high_conf_mask]\n", "            high_conf_scores = pile_scores[high_conf_mask]\n", "            \n", "            for center, score in zip(high_conf_centers, high_conf_scores):\n", "                # Estimate Z coordinate\n", "                nearby_mask = np.linalg.norm(points[:, :2] - center, axis=1) < 1.0\n", "                if np.sum(nearby_mask) > 0:\n", "                    z_coord = points[nearby_mask, 2].mean()\n", "                else:\n", "                    z_coord = points[:, 2].mean()\n", "                \n", "                detection = {\n", "                    'x': float(center[0]),\n", "                    'y': float(center[1]),\n", "                    'z': float(z_coord),\n", "                    'confidence': float(score),\n", "                    'detection_method': 'pointnet_trained'\n", "                }\n", "                pointnet_detections.append(detection)\n", "        \n", "        pointnet_results['detections'] = pointnet_detections\n", "        print(f\"  Final PointNet detections: {len(pointnet_detections)}\")\n", "    \n", "else:\n", "    print(\"Skipping PointNet inference - no trained model available\")\n", "    pointnet_detections = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Geometric Detection (Baseline Comparison)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting C-section pile detection...\n", "Detecting vertical structures...\n", "Points above 1.5m: 1,200,073\n", "Found 14460 vertical structure candidates\n", "Detected 0 C-section pile candidates\n", "\n", "==> DETECTION RESULTS COMPARISON\n", "\n", "Geometric Method:\n", "  Total detections: 0\n", "  Confidence threshold: 0.7\n", "\n", "PointNet Method:\n", "  Model trained: Yes\n", "  Ready for inference: Yes\n", "\n", "No detections found\n", "Note: Trained PointNet model is available for inference on real data\n", "\n", "No C-section piles detected with either method\n", "Consider:\n", "  - Lowering confidence threshold\n", "  - Adjusting patch size for PointNet++\n", "  - Training PointNet++ model on labeled data\n"]}], "source": ["# Run geometric detection\n", "geometric_detections = detector.detect_c_section_piles(points)\n", "\n", "print(f\"\\n==> DETECTION RESULTS COMPARISON\")\n", "print(f\"\\nGeometric Method:\")\n", "print(f\"  Total detections: {len(geometric_detections)}\")\n", "print(f\"  Confidence threshold: {confidence_threshold}\")\n", "\n", "if TORCH_AVAILABLE:\n", "    print(f\"\\nPointNet Method:\")\n", "    print(f\"  Model trained: {'Yes' if pointnet_results.get('model') else 'No'}\")\n", "    print(f\"  Ready for inference: {'Yes' if pointnet_results.get('model') else 'No'}\")\n", "\n", "# For this demo, use geometric detections as the primary method\n", "# In practice, you would use the trained PointNet model for inference\n", "if len(geometric_detections) > 0:\n", "    final_detections = geometric_detections\n", "    detection_method_used = 'geometric'\n", "    print(f\"\\nUsing geometric detections for final results\")\n", "    if TORCH_AVAILABLE and pointnet_results.get('model'):\n", "        print(f\"Note: Trained PointNet model is available for future inference\")\n", "else:\n", "    final_detections = []\n", "    detection_method_used = 'none'\n", "    print(f\"\\nNo detections found\")\n", "    if TORCH_AVAILABLE and pointnet_results.get('model'):\n", "        print(f\"Note: Trained PointNet model is available for inference on real data\")\n", "\n", "if final_detections:\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    heights = [d.get('height', 2.0) for d in final_detections]\n", "    widths = [d.get('width', 0.15) for d in final_detections]\n", "    \n", "    print(f\"\\nFinal Detection Statistics ({detection_method_used}):\")\n", "    print(f\"  Count: {len(final_detections)}\")\n", "    print(f\"  Mean confidence: {np.mean(confidences):.3f}\")\n", "    print(f\"  Confidence range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]\")\n", "    print(f\"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m\")\n", "    print(f\"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m\")\n", "    \n", "    # Display individual detections\n", "    print(f\"\\nDetailed Results:\")\n", "    for i, det in enumerate(final_detections[:10]):  # Show first 10\n", "        print(f\"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) \"\n", "              f\"conf={det['confidence']:.3f} method={det.get('detection_method', 'unknown')}\")\n", "    if len(final_detections) > 10:\n", "        print(f\"  ... and {len(final_detections) - 10} more\")\n", "else:\n", "    print(\"\\nNo C-section piles detected with either method\")\n", "    print(\"Consider:\")\n", "    print(\"  - Lowering confidence threshold\")\n", "    print(\"  - Adjusting patch size for PointNet++\")\n", "    print(\"  - Training PointNet++ model on labeled data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No detections to visualize\n"]}], "source": ["enable_visualization = True\n", "\n", "if enable_visualization and final_detections:\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Top view with detections\n", "    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')\n", "    \n", "    # Overlay detections (use squares for C-sections to distinguish from I-sections)\n", "    for det in final_detections:\n", "        square = plt.Rectangle((det['x'] - det['width']/2, det['y'] - det['width']/2), \n", "                             det['width'], det['width'], \n", "                             fill=False, color='orange', linewidth=2)\n", "        ax1.add_patch(square)\n", "        ax1.text(det['x'], det['y'], f\"{det['confidence']:.2f}\", \n", "                ha='center', va='center', color='orange', fontweight='bold')\n", "    \n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title(f'C-Section Pile Detection - Top View\\n{len(final_detections)} detections')\n", "    ax1.axis('equal')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Plot 2: Side view\n", "    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)\n", "    \n", "    # Overlay detections\n", "    for det in final_detections:\n", "        ax2.scatter(det['x'], det['z'], c='orange', s=100, marker='s', linewidth=3)\n", "        ax2.text(det['x'], det['z'] + 0.5, f\"{det['confidence']:.2f}\", \n", "                ha='center', va='bottom', color='orange', fontweight='bold')\n", "    \n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Z (m)')\n", "    ax2.set_title('C-Section Pile Detection - Side View')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_results:\n", "        plt.savefig(output_path / f'c_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')\n", "        print(f\"Saved visualization: {output_path / f'c_section_detection_{ground_method}.png'}\")\n", "    \n", "    plt.show()\n", "else:\n", "    print(\"No detections to visualize\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "SAVING RESULTS\n", "Saved metrics: ../../../../../data/output_runs/pile_detection/csf/c_section_metrics_csf.json\n", "\n", "All results saved to: ../../../../../data/output_runs/pile_detection/csf\n"]}], "source": ["if save_results:\n", "    print(\"\\nSAVING RESULTS\")\n", "    \n", "    # Save detections CSV\n", "    if final_detections:\n", "        df = pd.DataFrame(final_detections)\n", "        csv_file = output_path / f'c_section_detections_{ground_method}.csv'\n", "        df.to_csv(csv_file, index=False)\n", "        print(f\"Saved detections: {csv_file}\")\n", "    \n", "    # Save metrics\n", "    metrics = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'detection_method_used': detection_method_used,\n", "        'pytorch_available': TORCH_AVAILABLE,\n", "        'pile_type': 'c_section',\n", "        'confidence_threshold': confidence_threshold,\n", "        'total_detections': len(final_detections),\n", "        'input_points': int(len(points)),\n", "        'data_source': data_source,\n", "        'use_aligned_data': use_aligned_data,\n", "        'input_file': str(input_file),\n", "        'detection_statistics': {\n", "            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,\n", "            'mean_height': float(np.mean([d.get('height', 2.0) for d in final_detections])) if final_detections else 0.0,\n", "            'mean_width': float(np.mean([d.get('width', 0.15) for d in final_detections])) if final_detections else 0.0,\n", "            'mean_distribution_score': float(np.mean([d.get('distribution_score', 0.0) for d in final_detections])) if final_detections else 0.0\n", "        }\n", "    }\n", "    \n", "    metrics_file = output_path / f'c_section_metrics_{ground_method}.json'\n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(metrics, f, indent=2)\n", "    print(f\"Saved metrics: {metrics_file}\")\n", "    \n", "    print(f\"\\nAll results saved to: {output_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "C-SECTION PILE DETECTION SUMMARY\n", "============================================================\n", "Site: trino_enel\n", "Ground method: csf\n", "Detection method: none\n", "PyTorch available: True\n", "Input points: 1,359,240\n", "Geometric detections: 0\n", "PointNet model trained: Yes\n", "Final detections: 0\n", "Success rate: NO DETECTIONS\n", "============================================================\n", "\n", "Implementation Summary:\n", "- Clean PointNet implementation for C-section detection\n", "- Synthetic data generation for training\n", "- Complete training pipeline demonstrated\n", "- Geometric baseline for comparison\n", "- Ready for real data training\n", "\n", "Next steps:\n", "- Replace synthetic data with real labeled data\n", "- Implement inference on point cloud patches\n", "- Add data augmentation\n", "- Optimize hyperparameters\n"]}], "source": ["# Summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"C-SECTION PILE DETECTION SUMMARY\")\n", "print(\"=\"*60)\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Detection method: {detection_method_used}\")\n", "print(f\"PyTorch available: {TORCH_AVAILABLE}\")\n", "print(f\"Input points: {len(points):,}\")\n", "print(f\"Geometric detections: {len(geometric_detections)}\")\n", "if TORCH_AVAILABLE:\n", "    print(f\"PointNet model trained: {'Yes' if pointnet_results.get('model') else 'No'}\")\n", "print(f\"Final detections: {len(final_detections)}\")\n", "print(f\"Success rate: {'GOOD' if len(final_detections) > 0 else 'NO DETECTIONS'}\")\n", "print(\"=\"*60)\n", "print(\"\\nImplementation Summary:\")\n", "print(\"- Clean PointNet implementation for C-section detection\")\n", "print(\"- Synthetic data generation for training\")\n", "print(\"- Complete training pipeline demonstrated\")\n", "print(\"- Geometric baseline for comparison\")\n", "print(\"- Ready for real data training\")\n", "print(\"\\nNext steps:\")\n", "print(\"- Replace synthetic data with real labeled data\")\n", "print(\"- Implement inference on point cloud patches\")\n", "print(\"- Add data augmentation\")\n", "print(\"- Optimize hyperparameters\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}