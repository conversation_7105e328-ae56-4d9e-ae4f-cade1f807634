{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# I-Section Pile Detection (PointNet++)\n", "\n", "This notebook implements deep learning-based I-section pile detection using PointNet++ architecture as part of the pile detection stage. It processes ground-filtered or aligned point clouds and detects I-section pile structures using hierarchical feature learning.\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (I-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "**Model**: PointNet++ for hierarchical point-wise classification  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Ground-Filtered Data**: Import processed point cloud from ground segmentation or alignment\n", "2. **Patch Generation**: Create overlapping 3D patches for PointNet++ processing\n", "3. **I-Section Detection**: Apply PointNet++ model for hierarchical point-wise classification\n", "4. **Post-Processing**: Cluster detected points and extract pile centers\n", "5. **Export Results**: Save detected pile data in .csv format"]}, {"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["## Papermill Parameters\n", "\n", "These parameters can be overridden when running with Papermill for batch processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - can be overridden during execution\n", "site_name = \"site_001\"\n", "ground_method = \"csf\"  # Options: csf, pmf, ransac\n", "confidence_threshold = 0.7\n", "model_path = \"../../models/pointnet_plus_plus_isection_pile.pth\"\n", "input_data_dir = \"../../data/processed/ground_segmentation\"\n", "output_dir = \"../../output_runs/pile_detection\"\n", "mlflow_experiment_name = \"pile_detection_pointnet_plus_plus\"\n", "mlflow_run_name = f\"i_section_{site_name}_{ground_method}\"\n", "enable_cross_validation = True\n", "cv_folds = 5\n", "enable_enhanced_analysis = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install torch torchvision torch-geometric torch-points3d open3d matplotlib numpy scipy pandas\n", "!pip install scikit-learn plotly mlflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull\n", "from scipy.spatial.distance import cdist\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch_geometric.nn import PointConv, global_max_pool, global_mean_pool\n", "from torch_geometric.data import Data, Batch\n", "from torch_geometric.nn import knn_graph\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced analysis and tracking imports\n", "import pandas as pd\n", "import json\n", "import time\n", "from datetime import datetime\n", "from sklearn.model_selection import KFold, cross_val_score\n", "from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, roc_curve, auc\n", "from sklearn.metrics import precision_recall_curve, average_precision_score\n", "import seaborn as sns\n", "from scipy import stats\n", "\n", "# MLflow tracking\n", "try:\n", "    import mlflow\n", "    import mlflow.pytorch\n", "    import mlflow.sklearn\n", "    MLFLOW_AVAILABLE = True\n", "    print(\"MLflow available for experiment tracking\")\n", "except ImportError:\n", "    MLFLOW_AVAILABLE = False\n", "    print(\"MLflow not available - install with: pip install mlflow\")\n", "\n", "# Create output directories\n", "output_dir = Path(output_dir)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Initialize MLflow if available\n", "if MLFLOW_AVAILABLE:\n", "    mlflow.set_experiment(mlflow_experiment_name)\n", "    mlflow.start_run(run_name=mlflow_run_name)\n", "    \n", "    # Log parameters\n", "    mlflow.log_param(\"site_name\", site_name)\n", "    mlflow.log_param(\"ground_method\", ground_method)\n", "    mlflow.log_param(\"confidence_threshold\", confidence_threshold)\n", "    mlflow.log_param(\"model_architecture\", \"PointNet++\")\n", "    mlflow.log_param(\"pile_type\", \"i_section\")\n", "    mlflow.log_param(\"enable_cross_validation\", enable_cross_validation)\n", "    mlflow.log_param(\"cv_folds\", cv_folds)\n", "\n", "print(f\"Processing site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")\n", "print(f\"Cross-validation enabled: {enable_cross_validation}\")\n", "print(f\"Enhanced analysis enabled: {enable_enhanced_analysis}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. I-<PERSON> Characteristics\n", "\n", "Define the geometric characteristics of I-section piles for detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ISectionPileConfig:\n", "    \"\"\"\n", "    Configuration for I-section pile detection using PointNet++.\n", "    \"\"\"\n", "    def __init__(self):\n", "        # I-section dimensions (typical ranges)\n", "        #self.flange_width_range = (0.1, 0.5)  # meters\n", "        #self.web_height_range = (0.2, 1.2)  # meters\n", "        #self.flange_thickness_range = (0.01, 0.05)  # meters\n", "        #self.web_thickness_range = (0.008, 0.03)  # meters\n", "        self.height_range = (0.2, 1.5)  # meters\n", "\n", "        self.flange_width_range = (0.08, 0.6)  # Slightly expanded\n", "        self.web_height_range = (0.15, 1.5)   # Extended for larger sections\n", "        self.flange_thickness_range = (0.008, 0.06)  # More comprehensive\n", "        self.web_thickness_range = (0.006, 0.04)     # Include thinner webs\n", "\n", "        # Detection parameters\n", "        #self.patch_size = 2.0  # meters\n", "        #self.min_points_per_patch = 100\n", "        self.overlap_ratio = 0.5\n", "\n", "        # Detection parameters - optimized for structural scale\n", "        self.patch_size = 3.0  # Larger patches for building-scale detection\n", "        self.min_points_per_patch = 150  # More points for better geometry\n", "\n", "        # Add geometric validation\n", "        self.min_aspect_ratio = 2.0  # web_height/flange_width\n", "        self.max_aspect_ratio = 15.0\n", "\n", "        # PointNet++ parameters\n", "        self.num_points = 1024\n", "        self.num_classes = 2  # pile vs non-pile\n", "    \n", "        # Context-aware detection\n", "        self.connection_detection = True  # Detect beam-column connections\n", "        self.orientation_tolerance = 15.0  # degrees\n", "\n", "        # Set abstraction parameters\n", "        self.sa_npoints = [512, 128, None]  # Number of points in each SA layer\n", "        self.sa_radius = [0.2, 0.4, None]   # Radius for each SA layer\n", "        self.sa_nsample = [64, 64, None]    # Number of samples in each SA layer\n", "        self.sa_mlps = [[64, 64, 128], [128, 128, 256], [256, 512, 1024]]  # MLP dimensions\n", "        \n", "        # Feature propagation parameters\n", "        self.fp_mlps = [[256, 256], [256, 128], [128, 128, 128]]  # FP MLP dimensions\n", "\n", "config = ISectionPileConfig()\n", "print(\"I-Section Pile Detection Configuration (PointNet++):\")\n", "print(f\"Flange width range: {config.flange_width_range} m\")\n", "print(f\"Web height range: {config.web_height_range} m\")\n", "print(f\"Flange thickness range: {config.flange_thickness_range} m\")\n", "print(f\"Web thickness range: {config.web_thickness_range} m\")\n", "print(f\"Patch size: {config.patch_size} m\")\n", "print(f\"Set abstraction layers: {len(config.sa_npoints)}\")\n", "print(f\"Feature propagation layers: {len(config.fp_mlps)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Patch Generation\n", "\n", "Generate patches from point clouds for training and inference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):\n", "    \"\"\"\n", "    Generate overlapping patches from a point cloud.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray, optional\n", "        RGB colors (N, 3)\n", "    patch_size : float\n", "        Size of each patch in meters\n", "    overlap_ratio : float\n", "        Overlap ratio between adjacent patches\n", "    min_points : int\n", "        Minimum number of points per patch\n", "        \n", "    Returns:\n", "    --------\n", "    patches : list\n", "        List of patch dictionaries containing points, colors, and metadata\n", "    \"\"\"\n", "    # Calculate bounds\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    \n", "    # Calculate step size\n", "    step_size = patch_size * (1 - overlap_ratio)\n", "    \n", "    patches = []\n", "    patch_id = 0\n", "    \n", "    # Generate grid of patch centers\n", "    x_centers = np.arange(min_coords[0], max_coords[0], step_size)\n", "    y_centers = np.arange(min_coords[1], max_coords[1], step_size)\n", "    \n", "    for x_center in x_centers:\n", "        for y_center in y_centers:\n", "            # Define patch bounds\n", "            x_min = x_center - patch_size / 2\n", "            x_max = x_center + patch_size / 2\n", "            y_min = y_center - patch_size / 2\n", "            y_max = y_center + patch_size / 2\n", "            \n", "            # Find points within patch\n", "            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &\n", "                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))\n", "            \n", "            patch_points = points[mask]\n", "            \n", "            if len(patch_points) >= min_points:\n", "                # Center the patch points\n", "                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])\n", "                centered_points = patch_points - patch_center\n", "                \n", "                patch_data = {\n", "                    'id': patch_id,\n", "                    'points': centered_points,\n", "                    'original_points': patch_points,\n", "                    'center': patch_center,\n", "                    'bounds': (x_min, y_min, x_max, y_max),\n", "                    'num_points': len(patch_points)\n", "                }\n", "                \n", "                if colors is not None:\n", "                    patch_data['colors'] = colors[mask]\n", "                \n", "                patches.append(patch_data)\n", "                patch_id += 1\n", "    \n", "    print(f\"Generated {len(patches)} patches from point cloud\")\n", "    return patches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. I-Section Pile Geometric Analysis\n", "\n", "Analyze point cloud patches for I-section pile characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_i_section_geometry(points, tolerance=0.05):\n", "    \"\"\"\n", "    Analyze point cloud patch for I-section pile geometry.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud patch (N, 3)\n", "    tolerance : float\n", "        Tolerance for geometric analysis\n", "        \n", "    Returns:\n", "    --------\n", "    features : dict\n", "        Geometric features indicating I-section characteristics\n", "    \"\"\"\n", "    if len(points) < 10:\n", "        return None\n", "    \n", "    # Project points to XY plane for cross-section analysis\n", "    xy_points = points[:, :2]\n", "    \n", "    # Find convex hull\n", "    try:\n", "        hull = ConvexHull(xy_points)\n", "        hull_points = xy_points[hull.vertices]\n", "    except:\n", "        return None\n", "    \n", "    # Calculate bounding box\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    dimensions = max_coords - min_coords\n", "    \n", "    # Analyze cross-sectional shape for I-section\n", "    # For I-section, we expect:\n", "    # 1. Two horizontal flanges connected by a vertical web\n", "    # 2. Symmetric about both horizontal and vertical axes\n", "    # 3. Specific width-to-height ratios\n", "    # 4. Hollow regions between flanges\n", "    \n", "    # Calculate aspect ratios\n", "    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0\n", "    if xy_aspect < 1:\n", "        xy_aspect = 1 / xy_aspect\n", "    \n", "    # Analyze point distribution for I-section pattern\n", "    # Cluster points in cross-section\n", "    clustering = DBSCAN(eps=tolerance, min_samples=5)\n", "    cluster_labels = clustering.fit_predict(xy_points)\n", "    \n", "    unique_labels = np.unique(cluster_labels)\n", "    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)\n", "    \n", "    # Calculate symmetry measures\n", "    center_x, center_y = xy_points.mean(axis=0)\n", "    \n", "    # Check for horizontal symmetry (top and bottom flanges)\n", "    upper_points = xy_points[xy_points[:, 1] > center_y]\n", "    lower_points = xy_points[xy_points[:, 1] < center_y]\n", "    \n", "    # Check for vertical symmetry (left and right sides)\n", "    left_points = xy_points[xy_points[:, 0] < center_x]\n", "    right_points = xy_points[xy_points[:, 0] > center_x]\n", "    \n", "    # Calculate symmetry scores\n", "    h_symmetry = min(len(upper_points), len(lower_points)) / max(len(upper_points), len(lower_points), 1)\n", "    v_symmetry = min(len(left_points), len(right_points)) / max(len(left_points), len(right_points), 1)\n", "    \n", "    # Analyze density distribution for I-shape detection\n", "    # Divide into grid to detect hollow center characteristic of I-sections\n", "    grid_size = 5\n", "    x_bins = np.linspace(xy_points[:, 0].min(), xy_points[:, 0].max(), grid_size)\n", "    y_bins = np.linspace(xy_points[:, 1].min(), xy_points[:, 1].max(), grid_size)\n", "    \n", "    density_grid = np.zeros((grid_size-1, grid_size-1))\n", "    for i in range(grid_size-1):\n", "        for j in range(grid_size-1):\n", "            mask = ((xy_points[:, 0] >= x_bins[i]) & (xy_points[:, 0] < x_bins[i+1]) &\n", "                   (xy_points[:, 1] >= y_bins[j]) & (xy_points[:, 1] < y_bins[j+1]))\n", "            density_grid[i, j] = mask.sum()\n", "    \n", "    # I-section should have high density at edges (flanges) and low density in center\n", "    edge_density = (density_grid[0, :].mean() + density_grid[-1, :].mean() + \n", "                   density_grid[:, 0].mean() + density_grid[:, -1].mean()) / 4\n", "    center_density = density_grid[grid_size//2-1:grid_size//2+1, grid_size//2-1:grid_size//2+1].mean()\n", "    \n", "    hollow_ratio = 1 - (center_density / (edge_density + 1e-6))\n", "    \n", "    # Calculate features\n", "    features = {\n", "        'num_points': len(points),\n", "        'dimensions': dimensions,\n", "        'xy_aspect_ratio': xy_aspect,\n", "        'height': dimensions[2],\n", "        'width': max(dimensions[0], dimensions[1]),\n", "        'thickness': min(dimensions[0], dimensions[1]),\n", "        'num_clusters': num_clusters,\n", "        'hull_area': hull.volume if hasattr(hull, 'volume') else 0,\n", "        'h_symmetry': h_symmetry,\n", "        'v_symmetry': v_symmetry,\n", "        'hollow_ratio': hollow_ratio,\n", "        'edge_density': edge_density,\n", "        'center_density': center_density,\n", "        'compactness': len(points) / (hull.volume + 1e-6) if hasattr(hull, 'volume') else 0,\n", "        'i_section_score': h_symmetry * v_symmetry * hollow_ratio\n", "    }\n", "    \n", "    return features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. PointNet++ Model for I-Section Pile Detection\n", "\n", "Implement PointNet++ architecture for hierarchical point cloud feature learning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SetAbstractionLayer(nn.Module):\n", "    \"\"\"\n", "    Set Abstraction layer for PointNet++.\n", "    \"\"\"\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all=False):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.group_all = group_all\n", "        \n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        \n", "        last_channel = in_channel + 3  # +3 for xyz coordinates\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz, points):\n", "        \"\"\"\n", "        Parameters:\n", "        -----------\n", "        xyz : torch.Tensor\n", "            Point coordinates (B, N, 3)\n", "        points : torch.Tensor\n", "            Point features (B, N, C) or None\n", "            \n", "        Returns:\n", "        --------\n", "        new_xyz : torch.Tensor\n", "            Sampled point coordinates (B, npoint, 3)\n", "        new_points : torch.Tensor\n", "            Aggregated point features (B, npoint, mlp[-1])\n", "        \"\"\"\n", "        B, N, C = xyz.shape\n", "        \n", "        if self.group_all:\n", "            # Global pooling\n", "            new_xyz = xyz.mean(dim=1, keepdim=True)  # (B, 1, 3)\n", "            if points is not None:\n", "                new_points = torch.cat([xyz, points], dim=-1)  # (B, N, 3+C)\n", "            else:\n", "                new_points = xyz  # (B, N, 3)\n", "            new_points = new_points.unsqueeze(2)  # (B, N, 1, 3+C)\n", "        else:\n", "            # Farthest point sampling\n", "            fps_idx = self.farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = self.index_points(xyz, fps_idx)  # (B, npoint, 3)\n", "            \n", "            # Ball query\n", "            idx = self.ball_query(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = self.index_points(xyz, idx)  # (B, npoint, nsample, 3)\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.unsqueeze(2)  # Normalize\n", "            \n", "            if points is not None:\n", "                grouped_points = self.index_points(points, idx)  # (B, npoint, nsample, C)\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # (B, npoint, nsample, 3+C)\n", "            else:\n", "                new_points = grouped_xyz_norm  # (B, npoint, nsample, 3)\n", "        \n", "        # Apply MLPs\n", "        new_points = new_points.permute(0, 3, 2, 1)  # (B, 3+C, nsample, npoint)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "        \n", "        # Max pooling\n", "        new_points = torch.max(new_points, 2)[0]  # (B, mlp[-1], npoint)\n", "        new_points = new_points.permute(0, 2, 1)  # (B, npoint, mlp[-1])\n", "        \n", "        return new_xyz, new_points\n", "    \n", "    def farthest_point_sample(self, xyz, npoint):\n", "        \"\"\"\n", "        Farthest point sampling.\n", "        \"\"\"\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "        distance = torch.ones(B, N).to(device) * 1e10\n", "        farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "        batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "        \n", "        for i in range(npoint):\n", "            centroids[:, i] = farthest\n", "            centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "            dist = torch.sum((xyz - centroid) ** 2, -1)\n", "            mask = dist < distance\n", "            distance[mask] = dist[mask]\n", "            farthest = torch.max(distance, -1)[1]\n", "        \n", "        return centroids\n", "    \n", "    def ball_query(self, radius, nsample, xyz, new_xyz):\n", "        \"\"\"\n", "        Ball query for grouping points.\n", "        \"\"\"\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        _, S, _ = new_xyz.shape\n", "        group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "        \n", "        sqrdists = self.square_distance(new_xyz, xyz)\n", "        group_idx[sqrdists > radius ** 2] = N\n", "        group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "        group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "        mask = group_idx == N\n", "        group_idx[mask] = group_first[mask]\n", "        \n", "        return group_idx\n", "    \n", "    def square_distance(self, src, dst):\n", "        \"\"\"\n", "        Calculate squared distance between points.\n", "        \"\"\"\n", "        B, N, _ = src.shape\n", "        _, M, _ = dst.shape\n", "        dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "        dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "        dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "        return dist\n", "    \n", "    def index_points(self, points, idx):\n", "        \"\"\"\n", "        Index points using indices.\n", "        \"\"\"\n", "        device = points.device\n", "        B = points.shape[0]\n", "        view_shape = list(idx.shape)\n", "        view_shape[1:] = [1] * (len(view_shape) - 1)\n", "        repeat_shape = list(idx.shape)\n", "        repeat_shape[0] = 1\n", "        batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)\n", "        new_points = points[batch_indices, idx, :]\n", "        return new_points"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class FeaturePropagationLayer(nn.Module):\n", "    \"\"\"\n", "    Feature Propagation layer for PointNet++.\n", "    \"\"\"\n", "    def __init__(self, in_channel, mlp):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        \n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm1d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz1, xyz2, points1, points2):\n", "        \"\"\"\n", "        Parameters:\n", "        -----------\n", "        xyz1 : torch.Tensor\n", "            Coordinates of points to interpolate to (B, N, 3)\n", "        xyz2 : torch.Tensor\n", "            Coordinates of points with features (B, M, 3)\n", "        points1 : torch.Tensor\n", "            Features of points1 (B, N, C1) or None\n", "        points2 : torch.Tensor\n", "            Features of points2 (B, M, C2)\n", "            \n", "        Returns:\n", "        --------\n", "        new_points : torch.Tensor\n", "            Interpolated features (B, N, mlp[-1])\n", "        \"\"\"\n", "        B, N, C = xyz1.shape\n", "        _, M, _ = xyz2.shape\n", "        \n", "        if M == 1:\n", "            # Global feature, repeat for all points\n", "            interpolated_points = points2.repeat(1, N, 1)\n", "        else:\n", "            # Interpolate using inverse distance weighting\n", "            dists = self.square_distance(xyz1, xyz2)\n", "            dists, idx = dists.sort(dim=-1)\n", "            dists, idx = dists[:, :, :3], idx[:, :, :3]  # Use 3 nearest neighbors\n", "            \n", "            dist_recip = 1.0 / (dists + 1e-8)\n", "            norm = torch.sum(dist_recip, dim=2, keepdim=True)\n", "            weight = dist_recip / norm\n", "            \n", "            interpolated_points = torch.sum(self.index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)\n", "        \n", "        if points1 is not None:\n", "            new_points = torch.cat([points1, interpolated_points], dim=-1)\n", "        else:\n", "            new_points = interpolated_points\n", "        \n", "        # Apply MLPs\n", "        new_points = new_points.permute(0, 2, 1)  # (B, C, N)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "        \n", "        new_points = new_points.permute(0, 2, 1)  # (B, N, C)\n", "        return new_points\n", "    \n", "    def square_distance(self, src, dst):\n", "        \"\"\"\n", "        Calculate squared distance between points.\n", "        \"\"\"\n", "        B, N, _ = src.shape\n", "        _, M, _ = dst.shape\n", "        dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "        dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "        dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "        return dist\n", "    \n", "    def index_points(self, points, idx):\n", "        \"\"\"\n", "        Index points using indices.\n", "        \"\"\"\n", "        device = points.device\n", "        B = points.shape[0]\n", "        view_shape = list(idx.shape)\n", "        view_shape[1:] = [1] * (len(view_shape) - 1)\n", "        repeat_shape = list(idx.shape)\n", "        repeat_shape[0] = 1\n", "        batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)\n", "        new_points = points[batch_indices, idx, :]\n", "        return new_points"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PointNetPlusPlusISectionPile(nn.Module):\n", "    \"\"\"\n", "    PointNet++ model for I-section pile detection.\n", "    \"\"\"\n", "    def __init__(self, num_classes=2, dropout=0.5):\n", "        super(PointNetPlusPlusISectionPile, self).__init__()\n", "        self.num_classes = num_classes\n", "        \n", "        # Set Abstraction layers\n", "        self.sa1 = SetAbstractionLayer(\n", "            npoint=config.sa_npoints[0], \n", "            radius=config.sa_radius[0], \n", "            nsample=config.sa_nsample[0],\n", "            in_channel=0,  # No input features, only xyz\n", "            mlp=config.sa_mlps[0]\n", "        )\n", "        \n", "        self.sa2 = SetAbstractionLayer(\n", "            npoint=config.sa_npoints[1], \n", "            radius=config.sa_radius[1], \n", "            nsample=config.sa_nsample[1],\n", "            in_channel=config.sa_mlps[0][-1],\n", "            mlp=config.sa_mlps[1]\n", "        )\n", "        \n", "        self.sa3 = SetAbstractionLayer(\n", "            npoint=config.sa_npoints[2], \n", "            radius=config.sa_radius[2], \n", "            nsample=config.sa_nsample[2],\n", "            in_channel=config.sa_mlps[1][-1],\n", "            mlp=config.sa_mlps[2],\n", "            group_all=True\n", "        )\n", "        \n", "        # Feature Propagation layers\n", "        self.fp3 = FeaturePropagationLayer(\n", "            in_channel=config.sa_mlps[2][-1] + config.sa_mlps[1][-1],\n", "            mlp=config.fp_mlps[0]\n", "        )\n", "        \n", "        self.fp2 = FeaturePropagationLayer(\n", "            in_channel=config.fp_mlps[0][-1] + config.sa_mlps[0][-1],\n", "            mlp=config.fp_mlps[1]\n", "        )\n", "        \n", "        self.fp1 = FeaturePropagationLayer(\n", "            in_channel=config.fp_mlps[1][-1],\n", "            mlp=config.fp_mlps[2]\n", "        )\n", "        \n", "        # Classification heads\n", "        # Global classification (patch-level)\n", "        self.global_classifier = nn.Sequential(\n", "            nn.Linear(config.sa_mlps[2][-1], 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "        \n", "        # Point-wise classification\n", "        self.point_classifier = nn.Sequential(\n", "            nn.Conv1d(config.fp_mlps[2][-1], 128, 1),\n", "            nn.<PERSON>chNorm1d(128),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Conv1d(128, num_classes, 1)\n", "        )\n", "    \n", "    def forward(self, xyz):\n", "        \"\"\"\n", "        Parameters:\n", "        -----------\n", "        xyz : torch.Tensor\n", "            Point coordinates (B, N, 3)\n", "            \n", "        Returns:\n", "        --------\n", "        global_pred : torch.Tensor\n", "            Global classification logits (B, num_classes)\n", "        point_pred : torch.Tensor\n", "            Point-wise classification logits (B, N, num_classes)\n", "        \"\"\"\n", "        # Set Abstraction\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "        \n", "        # Global classification\n", "        global_features = l3_points.squeeze(1)  # (B, C)\n", "        global_pred = self.global_classifier(global_features)\n", "        \n", "        # Feature Propagation\n", "        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)\n", "        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)\n", "        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)\n", "        \n", "        # Point-wise classification\n", "        point_features = l0_points.permute(0, 2, 1)  # (B, C, N)\n", "        point_pred = self.point_classifier(point_features)  # (B, num_classes, N)\n", "        point_pred = point_pred.permute(0, 2, 1)  # (B, N, num_classes)\n", "        \n", "        return global_pred, point_pred"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Ground-Filtered Point Cloud Loading\n", "\n", "Load processed point cloud data from ground segmentation or alignment stage."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration for data paths\n", "import pandas as pd\n", "import laspy\n", "\n", "# Define input and output paths\n", "input_data_dir = Path(\"../../data/processed/ground_segmentation\")\n", "output_dir = Path(\"../../output_runs/pile_detection\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Parameters for Papermill execution\n", "site_name = \"site_001\"  # Will be parameterized\n", "ground_method = \"csf\"   # Options: csf, pmf, ransac\n", "confidence_threshold = 0.7\n", "model_path = \"../../models/pointnet_plus_plus_isection_pile.pth\"\n", "\n", "print(f\"Processing site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_ground_filtered_point_cloud(site_name, method=\"csf\"):\n", "    \"\"\"\n", "    Load ground-filtered point cloud from previous processing stage.\n", "    \n", "    Parameters:\n", "    -----------\n", "    site_name : str\n", "        Name of the site to process\n", "    method : str\n", "        Ground segmentation method used (csf, pmf, ransac)\n", "        \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray\n", "        RGB colors if available (N, 3)\n", "    metadata : dict\n", "        Additional metadata about the point cloud\n", "    \"\"\"\n", "    # Look for ground-filtered files\n", "    las_file = input_data_dir / f\"{site_name}_ground_filtered_{method}.las\"\n", "    \n", "    if not las_file.exists():\n", "        # Try alternative naming conventions\n", "        alternative_files = list(input_data_dir.glob(f\"{site_name}*{method}*.las\"))\n", "        if alternative_files:\n", "            las_file = alternative_files[0]\n", "        else:\n", "            raise FileNotFoundError(f\"No ground-filtered LAS file found for {site_name} with method {method}\")\n", "    \n", "    print(f\"Loading point cloud from: {las_file}\")\n", "    \n", "    # Load LAS file\n", "    las = laspy.read(las_file)\n", "    \n", "    # Extract coordinates\n", "    points = np.vstack([las.x, las.y, las.z]).transpose()\n", "    \n", "    # Extract colors if available\n", "    colors = None\n", "    if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):\n", "        colors = np.vstack([las.red, las.green, las.blue]).transpose()\n", "        colors = colors / 65535.0  # Normalize to [0, 1]\n", "    \n", "    # Extract metadata\n", "    metadata = {\n", "        'num_points': len(points),\n", "        'bounds': {\n", "            'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),\n", "            'y_min': points[:, 1].min(), 'y_max': points[:, 1].max(),\n", "            'z_min': points[:, 2].min(), 'z_max': points[:, 2].max()\n", "        },\n", "        'file_path': str(las_file),\n", "        'ground_method': method\n", "    }\n", "    \n", "    print(f\"Loaded {len(points):,} points\")\n", "    print(f\"Bounds: X[{metadata['bounds']['x_min']:.2f}, {metadata['bounds']['x_max']:.2f}] \")\n", "    print(f\"        Y[{metadata['bounds']['y_min']:.2f}, {metadata['bounds']['y_max']:.2f}] \")\n", "    print(f\"        Z[{metadata['bounds']['z_min']:.2f}, {metadata['bounds']['z_max']:.2f}]\")\n", "    \n", "    return points, colors, metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the ground-filtered point cloud\n", "points, colors, metadata = load_ground_filtered_point_cloud(site_name, ground_method)\n", "\n", "# Display basic statistics\n", "print(f\"Point cloud statistics:\")\n", "print(f\"Total points: {len(points):,}\")\n", "print(f\"Point density: {len(points) / ((metadata['bounds']['x_max'] - metadata['bounds']['x_min']) * (metadata['bounds']['y_max'] - metadata['bounds']['y_min'])):.2f} points/m²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Model Inference and Detection\n", "\n", "Apply PointNet++ model for pile detection (placeholder for actual implementation)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder for model inference\n", "# In a complete implementation, this would:\n", "# 1. Load the trained PointNet++ model\n", "# 2. Generate patches from the point cloud\n", "# 3. Run inference on each patch\n", "# 4. Post-process results to extract pile detections\n", "\n", "# For demonstration, create mock detection results\n", "final_detections = []\n", "\n", "# Simulate some detections for testing\n", "if len(points) > 1000:  # Only if we have sufficient points\n", "    # Create mock detections\n", "    num_detections = np.random.randint(0, 5)\n", "    for i in range(num_detections):\n", "        # Random location within point cloud bounds\n", "        x = np.random.uniform(metadata['bounds']['x_min'], metadata['bounds']['x_max'])\n", "        y = np.random.uniform(metadata['bounds']['y_min'], metadata['bounds']['y_max'])\n", "        z = np.random.uniform(metadata['bounds']['z_min'], metadata['bounds']['z_max'])\n", "        \n", "        detection = {\n", "            'x': x,\n", "            'y': y,\n", "            'z': z,\n", "            'confidence': np.random.beta(2, 2),  # Beta distribution for realistic confidence\n", "            'width': np.random.uniform(0.1, 0.4),\n", "            'height': np.random.uniform(0.2, 1.0),\n", "            'thickness': np.random.uniform(0.01, 0.05),\n", "            'i_section_score': np.random.beta(3, 2)\n", "        }\n", "        \n", "        # Only include detections above confidence threshold\n", "        if detection['confidence'] >= confidence_threshold:\n", "            final_detections.append(detection)\n", "\n", "print(f\"\\nPointNet++ I-Section Pile Detection Results:\")\n", "print(f\"Total detections: {len(final_detections)}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")\n", "\n", "if final_detections:\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    print(f\"Mean confidence: {np.mean(confidences):.3f}\")\n", "    print(f\"Confidence range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]\")\n", "else:\n", "    print(\"No detections above confidence threshold.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Standardized Output Format\n", "\n", "Export results in standardized format for architecture comparison."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_pointnet_plus_plus_results(final_detections, site_name, ground_method, output_dir):\n", "    \"\"\"\n", "    Export PointNet++ results in standardized format for architecture comparison.\n", "    \n", "    Parameters:\n", "    -----------\n", "    final_detections : list\n", "        List of final detection results\n", "    site_name : str\n", "        Name of the site\n", "    ground_method : str\n", "        Ground segmentation method used\n", "    output_dir : Path\n", "        Output directory\n", "        \n", "    Returns:\n", "    --------\n", "    output_files : dict\n", "        Dictionary of output file paths\n", "    \"\"\"\n", "    output_files = {}\n", "    \n", "    # 1. Detection results CSV (standardized format)\n", "    detection_filename = f\"{site_name}_pile_detections_pointnet_plus_plus_i_section_{ground_method}.csv\"\n", "    detection_file = output_dir / detection_filename\n", "    \n", "    if final_detections:\n", "        # Convert to DataFrame with standardized columns\n", "        detection_data = []\n", "        for i, detection in enumerate(final_detections):\n", "            row = {\n", "                'detection_id': i,\n", "                'x': detection['x'],\n", "                'y': detection['y'],\n", "                'z': detection['z'],\n", "                'confidence': detection['confidence'],\n", "                'pile_type': 'i_section',\n", "                'architecture': 'pointnet_plus_plus',\n", "                'site_name': site_name,\n", "                'ground_method': ground_method,\n", "                'width': detection.get('width', None),\n", "                'height': detection.get('height', None),\n", "                'thickness': detection.get('thickness', None),\n", "                'i_section_score': detection.get('i_section_score', None),\n", "                'timestamp': datetime.now().isoformat()\n", "            }\n", "            detection_data.append(row)\n", "        \n", "        detection_df = pd.DataFrame(detection_data)\n", "        detection_df.to_csv(detection_file, index=False)\n", "        output_files['detections_csv'] = detection_file\n", "        print(f\"Detection results saved to: {detection_file}\")\n", "    else:\n", "        # Create empty DataFrame with standard columns\n", "        empty_df = pd.DataFrame(columns=[\n", "            'detection_id', 'x', 'y', 'z', 'confidence', 'pile_type', 'architecture',\n", "            'site_name', 'ground_method', 'width', 'height', 'thickness', \n", "            'i_section_score', 'timestamp'\n", "        ])\n", "        empty_df.to_csv(detection_file, index=False)\n", "        output_files['detections_csv'] = detection_file\n", "        print(f\"Empty detection results saved to: {detection_file}\")\n", "    \n", "    # 2. Performance metrics JSON\n", "    metrics_filename = f\"{site_name}_performance_metrics_pointnet_plus_plus_i_section_{ground_method}.json\"\n", "    metrics_file = output_dir / metrics_filename\n", "    \n", "    performance_metrics = {\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'architecture': 'pointnet_plus_plus',\n", "        'pile_type': 'i_section',\n", "        'timestamp': datetime.now().isoformat(),\n", "        'total_detections': len(final_detections),\n", "        'confidence_threshold': confidence_threshold,\n", "        'model_path': model_path,\n", "        'processing_time_ms': None,  # Would be measured during actual processing\n", "        'memory_usage_mb': None,     # Would be measured during actual processing\n", "        'model_size_mb': None        # Would be calculated from model file\n", "    }\n", "    \n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(performance_metrics, f, indent=2)\n", "    \n", "    output_files['metrics_json'] = metrics_file\n", "    print(f\"Performance metrics saved to: {metrics_file}\")\n", "    \n", "    return output_files\n", "\n", "# Export results\n", "print(\"\\nExporting standardized results...\")\n", "output_files = export_pointnet_plus_plus_results(\n", "    final_detections, \n", "    site_name, \n", "    ground_method, \n", "    output_dir\n", ")\n", "\n", "# MLflow logging\n", "if MLFLOW_AVAILABLE:\n", "    print(\"\\nLogging results to MLflow...\")\n", "    \n", "    try:\n", "        # Log metrics\n", "        mlflow.log_metric(\"total_detections\", len(final_detections))\n", "        \n", "        if final_detections:\n", "            confidences = [d['confidence'] for d in final_detections]\n", "            mlflow.log_metric(\"mean_confidence\", np.mean(confidences))\n", "            mlflow.log_metric(\"std_confidence\", np.std(confidences))\n", "            mlflow.log_metric(\"min_confidence\", np.min(confidences))\n", "            mlflow.log_metric(\"max_confidence\", np.max(confidences))\n", "            mlflow.log_metric(\"median_confidence\", np.median(confidences))\n", "        \n", "        # Log artifacts\n", "        if 'detections_csv' in output_files:\n", "            mlflow.log_artifact(str(output_files['detections_csv']))\n", "        \n", "        if 'metrics_json' in output_files:\n", "            mlflow.log_artifact(str(output_files['metrics_json']))\n", "        \n", "        # Add tags\n", "        mlflow.set_tag(\"architecture\", \"PointNet++\")\n", "        mlflow.set_tag(\"pile_type\", \"i_section\")\n", "        mlflow.set_tag(\"stage\", \"pile_detection\")\n", "        \n", "        print(\"MLflow logging completed successfully.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"MLflow logging failed: {e}\")\n", "    \n", "    finally:\n", "        mlflow.end_run()\n", "        print(\"MLflow run ended.\")\n", "else:\n", "    print(\"MLflow not available - skipping experiment tracking.\")\n", "\n", "print(f\"\\nAll outputs saved to: {output_dir}\")\n", "print(\"Files created:\")\n", "for file_type, file_path in output_files.items():\n", "    print(f\"  {file_type}: {file_path.name}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}