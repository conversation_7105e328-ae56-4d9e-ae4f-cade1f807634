import sys
from pathlib import Path

# Add notebooks to path
notebooks_root = Path.cwd().parents[4]
if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Parameters
training_data_path = "../../rule_based/data/output_runs"  # Path to IFC training data
batch_size = 16
num_epochs = 100
learning_rate = 0.001
num_points = 1024  # Points per patch
save_model = True

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")
print("=== POINTNET++ PILE DETECTION ===")

def load_training_data(data_dir):
    """Load training data from IFC rule-based detection"""
    data_dir = Path(data_dir)
    
    # Find latest training data
    training_files = list(data_dir.glob("*/training_data.json"))
    if not training_files:
        raise FileNotFoundError(f"No training data found in {data_dir}")
    
    latest_file = max(training_files, key=lambda x: x.parent.name)
    print(f"Loading training data: {latest_file}")
    
    with open(latest_file, 'r') as f:
        data = json.load(f)
    
    return data

def preprocess_patches(patches, num_points=1024):
    """Preprocess point patches for PointNet++"""
    processed = []
    
    for patch in patches:
        patch_array = np.array(patch)
        
        if len(patch_array) == 0:
            continue
            
        # Sample or pad to fixed number of points
        if len(patch_array) >= num_points:
            # Random sampling
            indices = np.random.choice(len(patch_array), num_points, replace=False)
            sampled_patch = patch_array[indices]
        else:
            # Pad with repetition
            repetitions = num_points // len(patch_array) + 1
            repeated = np.tile(patch_array, (repetitions, 1))
            sampled_patch = repeated[:num_points]
        
        # Normalize coordinates
        centroid = np.mean(sampled_patch, axis=0)
        sampled_patch = sampled_patch - centroid
        
        # Scale to unit sphere
        max_dist = np.max(np.linalg.norm(sampled_patch, axis=1))
        if max_dist > 0:
            sampled_patch = sampled_patch / max_dist
        
        processed.append(sampled_patch)
    
    return np.array(processed)

# Load and preprocess data
training_data = load_training_data(training_data_path)

positive_patches = preprocess_patches(training_data['positive_patches'], num_points)
negative_patches = preprocess_patches(training_data['negative_patches'], num_points)

print(f"Positive patches: {len(positive_patches)}")
print(f"Negative patches: {len(negative_patches)}")

# Combine data
X = np.concatenate([positive_patches, negative_patches], axis=0)
y = np.concatenate([np.ones(len(positive_patches)), np.zeros(len(negative_patches))], axis=0)

print(f"Total samples: {len(X)}")
print(f"Input shape: {X.shape}")

class PileDataset(Dataset):
    def __init__(self, points, labels):
        self.points = torch.FloatTensor(points)
        self.labels = torch.LongTensor(labels)
    
    def __len__(self):
        return len(self.points)
    
    def __getitem__(self, idx):
        return self.points[idx], self.labels[idx]

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

X_train, X_val, y_train, y_val = train_test_split(
    X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
)

print(f"Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")

# Create datasets
train_dataset = PileDataset(X_train, y_train)
val_dataset = PileDataset(X_val, y_val)
test_dataset = PileDataset(X_test, y_test)

# Create dataloaders
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

def square_distance(src, dst):
    """Calculate squared distance between points"""
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist

def farthest_point_sample(xyz, npoint):
    """Farthest point sampling"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)
    
    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]
    
    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """Ball query"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

class PointNetSetAbstraction(nn.Module):
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        self.group_all = group_all
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        B, N, C = xyz.shape
        
        if self.group_all:
            new_xyz = torch.zeros(B, 1, C).to(xyz.device)
            new_points = points.view(B, 1, N, -1)
        else:
            fps_idx = farthest_point_sample(xyz, self.npoint)
            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]
            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]
            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)
            
            if points is not None:
                grouped_points = points[torch.arange(B)[:, None, None], idx]
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)
            else:
                new_points = grouped_xyz_norm
        
        new_points = new_points.permute(0, 3, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = torch.relu(bn(conv(new_points)))
        
        new_points = torch.max(new_points, 2)[0]
        new_points = new_points.permute(0, 2, 1)
        
        return new_xyz, new_points

class PointNetPlusPlus(nn.Module):
    def __init__(self, num_classes=2):
        super(PointNetPlusPlus, self).__init__()
        
        # Set abstraction layers
        self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)
        self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)
        self.sa3 = PointNetSetAbstraction(None, None, None, 256 + 3, [256, 512, 1024], True)
        
        # Classification head
        self.fc1 = nn.Linear(1024, 512)
        self.bn1 = nn.BatchNorm1d(512)
        self.drop1 = nn.Dropout(0.4)
        self.fc2 = nn.Linear(512, 256)
        self.bn2 = nn.BatchNorm1d(256)
        self.drop2 = nn.Dropout(0.4)
        self.fc3 = nn.Linear(256, num_classes)
    
    def forward(self, xyz):
        B, _, _ = xyz.shape
        
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        
        x = l3_points.view(B, 1024)
        x = self.drop1(torch.relu(self.bn1(self.fc1(x))))
        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))
        x = self.fc3(x)
        
        return x

# Initialize model
model = PointNetPlusPlus(num_classes=2).to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=learning_rate)

print(f"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters")

def train_epoch(model, loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (data, target) in enumerate(loader):
        data, target = data.to(device), target.to(device)
        
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        pred = output.argmax(dim=1)
        correct += pred.eq(target).sum().item()
        total += target.size(0)
    
    return total_loss / len(loader), correct / total

def validate_epoch(model, loader, criterion, device):
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            
            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)
    
    return total_loss / len(loader), correct / total

# Training loop
print("Starting training...")
train_losses = []
val_losses = []
train_accs = []
val_accs = []

best_val_acc = 0
patience = 10
patience_counter = 0

for epoch in range(num_epochs):
    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)
    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)
    
    train_losses.append(train_loss)
    val_losses.append(val_loss)
    train_accs.append(train_acc)
    val_accs.append(val_acc)
    
    if (epoch + 1) % 10 == 0:
        print(f"Epoch {epoch+1}/{num_epochs}:")
        print(f"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
        print(f"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
    
    # Early stopping
    if val_acc > best_val_acc:
        best_val_acc = val_acc
        patience_counter = 0
        if save_model:
            torch.save(model.state_dict(), 'best_pointnet_plus_plus.pth')
    else:
        patience_counter += 1
        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch+1}")
            break

print(f"Training completed. Best validation accuracy: {best_val_acc:.4f}")

# Load best model for evaluation
if save_model:
    model.load_state_dict(torch.load('best_pointnet_plus_plus.pth'))

# Test evaluation
model.eval()
all_preds = []
all_targets = []

with torch.no_grad():
    for data, target in test_loader:
        data, target = data.to(device), target.to(device)
        output = model(data)
        pred = output.argmax(dim=1)
        
        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target.cpu().numpy())

# Calculate metrics
test_accuracy = accuracy_score(all_targets, all_preds)
test_precision = precision_score(all_targets, all_preds)
test_recall = recall_score(all_targets, all_preds)
test_f1 = f1_score(all_targets, all_preds)

print("=== POINTNET++ TEST RESULTS ===")
print(f"Accuracy: {test_accuracy:.4f}")
print(f"Precision: {test_precision:.4f}")
print(f"Recall: {test_recall:.4f}")
print(f"F1-Score: {test_f1:.4f}")

# Compare with rule-based baseline
rule_based_performance = training_data['performance']
print("\n=== COMPARISON WITH RULE-BASED BASELINE ===")
print(f"Rule-based F1: {rule_based_performance['f1_score']:.4f}")
print(f"PointNet++ F1: {test_f1:.4f}")
improvement = ((test_f1 - rule_based_performance['f1_score']) / rule_based_performance['f1_score']) * 100
print(f"Improvement: {improvement:.1f}%")

# Save results
results = {
    'model': 'PointNet++',
    'test_metrics': {
        'accuracy': float(test_accuracy),
        'precision': float(test_precision),
        'recall': float(test_recall),
        'f1_score': float(test_f1)
    },
    'training_info': {
        'num_epochs': len(train_losses),
        'best_val_acc': float(best_val_acc),
        'final_train_acc': float(train_accs[-1]),
        'final_val_acc': float(val_accs[-1])
    },
    'comparison': {
        'rule_based_f1': rule_based_performance['f1_score'],
        'pointnet_plus_plus_f1': float(test_f1),
        'improvement_percent': float(improvement)
    }
}

with open('pointnet_plus_plus_results.json', 'w') as f:
    json.dump(results, f, indent=2)

print(f"\nResults saved to pointnet_plus_plus_results.json")
print("Ready for DGCNN comparison.")