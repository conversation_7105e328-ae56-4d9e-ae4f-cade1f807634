{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Feature Analysis for Machine Learning\n", "\n", "This notebook analyzes prepared patch data and extracts features for ML-based pile detection training.\n", "\n", "## What we will do:\n", "1. Load the prepared patch data\n", "2. Extract geometric features from each patch\n", "3. Analyze differences between pile and non-pile patches\n", "4. <PERSON><PERSON>p baseline rule-based classification\n", "5. Prepare features for ML model training\n", "6. Visualize feature distributions and insights\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup Environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Load Prepared Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading prepared patch data...\n", "Data loaded from: ../data/output_runs/pile_detection_20250721_192209/extracted_patches.json\n"]}], "source": ["# Load the prepared patch data from the first notebook\n", "print(\"Loading prepared patch data...\")\n", "\n", "try:\n", "    # Read the output directory path saved by the first notebook\n", "    with open(\"output_dir.txt\", \"r\") as f:\n", "        output_dir = Path(f.read().strip())\n", "    \n", "    # Load the patch data\n", "    patch_file = output_dir / \"extracted_patches.json\"\n", "    with open(patch_file, 'r') as f:\n", "        patch_data = json.load(f)\n", "    \n", "    print(f\"Data loaded from: {patch_file}\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"Error: Could not find prepared data.\")\n", "    print(\"Please run the first notebook (01_ifc_based_pile_detection_data_prep.ipynb) first.\")\n", "    raise"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reconstructing patch data...\n", "Data reconstruction complete:\n", "  Positive patches (pile locations): 5000\n", "  Negative patches (non-pile locations): 1\n", "  Total pile coordinates: 14460\n", "  Parameters: {'site_name': 'trino_enel', 'ground_method': 'ransac_pmf', 'patch_radius': 5.0, 'min_points_per_patch': 100, 'max_positive_samples': 5000, 'max_negative_samples': 2500, 'timestamp': '20250721_192209'}\n"]}], "source": ["# Reconstruct the data from JSON format\n", "print(\"Reconstructing patch data...\")\n", "\n", "# Convert patch data back to numpy arrays\n", "positive_patches = [np.array(patch) for patch in patch_data['positive_patches']]\n", "negative_patches = [np.array(patch) for patch in patch_data['negative_patches']]\n", "\n", "# Extract metadata\n", "positive_info = patch_data['positive_info']\n", "negative_info = patch_data['negative_info']\n", "pile_coordinates = np.array(patch_data['pile_coords'])\n", "parameters = patch_data['parameters']\n", "\n", "print(f\"Data reconstruction complete:\")\n", "print(f\"  Positive patches (pile locations): {len(positive_patches)}\")\n", "print(f\"  Negative patches (non-pile locations): {len(negative_patches)}\")\n", "print(f\"  Total pile coordinates: {len(pile_coordinates)}\")\n", "print(f\"  Parameters: {parameters}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Define Feature Extraction Functions"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature extraction function defined\n"]}], "source": ["def extract_geometric_features(patch_points):\n", "    \"\"\"\n", "    Extract geometric features from a point cloud patch.\n", "    \n", "    Args:\n", "        patch_points: numpy array of 3D points [x, y, z]\n", "    \n", "    Returns:\n", "        dict: Dictionary of extracted features\n", "    \"\"\"\n", "    if len(patch_points) == 0:\n", "        return {}\n", "    \n", "    # Basic point statistics\n", "    features = {\n", "        'num_points': len(patch_points),\n", "    }\n", "    \n", "    # Height (Z-coordinate) features\n", "    z_values = patch_points[:, 2]\n", "    features['height_min'] = z_values.min()\n", "    features['height_max'] = z_values.max()\n", "    features['height_range'] = features['height_max'] - features['height_min']\n", "    features['height_mean'] = z_values.mean()\n", "    features['height_std'] = z_values.std()\n", "    \n", "    # Horizontal extent (X-Y footprint)\n", "    x_values = patch_points[:, 0]\n", "    y_values = patch_points[:, 1]\n", "    features['x_extent'] = x_values.max() - x_values.min()\n", "    features['y_extent'] = y_values.max() - y_values.min()\n", "    features['footprint_area'] = features['x_extent'] * features['y_extent']\n", "    \n", "    # Point density\n", "    features['point_density'] = features['num_points'] / max(features['footprint_area'], 1e-6)\n", "    \n", "    # Height distribution percentiles\n", "    features['height_p25'] = np.percentile(z_values, 25)\n", "    features['height_p75'] = np.percentile(z_values, 75)\n", "    features['height_iqr'] = features['height_p75'] - features['height_p25']\n", "    \n", "    return features\n", "\n", "print(\"Feature extraction function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Extract Features from All Patches"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting features from positive patches...\n", "  Processed 500 positive patches...\n", "  Processed 1000 positive patches...\n", "  Processed 1500 positive patches...\n", "  Processed 2000 positive patches...\n", "  Processed 2500 positive patches...\n", "  Processed 3000 positive patches...\n", "  Processed 3500 positive patches...\n", "  Processed 4000 positive patches...\n", "  Processed 4500 positive patches...\n", "  Processed 5000 positive patches...\n", "Completed feature extraction for 5000 positive patches\n"]}], "source": ["# Extract features from positive patches (pile locations)\n", "print(\"Extracting features from positive patches...\")\n", "\n", "positive_features = []\n", "for i, patch in enumerate(positive_patches):\n", "    features = extract_geometric_features(patch)\n", "    features['patch_type'] = 'positive'\n", "    features['patch_index'] = i\n", "    positive_features.append(features)\n", "    \n", "    if (i + 1) % 500 == 0:\n", "        print(f\"  Processed {i + 1} positive patches...\")\n", "\n", "print(f\"Completed feature extraction for {len(positive_features)} positive patches\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting features from negative patches...\n", "Completed feature extraction for 1 negative patches\n"]}], "source": ["# Extract features from negative patches (non-pile locations)\n", "print(\"Extracting features from negative patches...\")\n", "\n", "negative_features = []\n", "for i, patch in enumerate(negative_patches):\n", "    features = extract_geometric_features(patch)\n", "    features['patch_type'] = 'negative'\n", "    features['patch_index'] = i\n", "    negative_features.append(features)\n", "    \n", "    if (i + 1) % 500 == 0:\n", "        print(f\"  Processed {i + 1} negative patches...\")\n", "\n", "print(f\"Completed feature extraction for {len(negative_features)} negative patches\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating feature DataFrame...\n", "Feature DataFrame created with 5001 rows and 15 columns\n", "\n", "Feature columns: ['num_points', 'height_min', 'height_max', 'height_range', 'height_mean', 'height_std', 'x_extent', 'y_extent', 'footprint_area', 'point_density', 'height_p25', 'height_p75', 'height_iqr', 'patch_type', 'patch_index']\n", "\n", "Sample data:\n", "   num_points  height_min  height_max  height_range  height_mean  height_std  \\\n", "0         123  156.198880  158.881235      2.682355   157.534363    1.151669   \n", "1         126  156.245883  159.055240      2.809357   157.470556    1.134552   \n", "2         130  155.813766  159.153226      3.339460   157.632718    1.119055   \n", "3         111  156.181723  158.750867      2.569144   157.470244    1.151568   \n", "4         113  156.224854  159.073562      2.848708   157.291878    1.024946   \n", "\n", "    x_extent   y_extent  footprint_area  point_density  height_p25  \\\n", "0  13.996109  14.168534      198.304355       0.620259  156.372112   \n", "1  13.970649  13.938215      194.725919       0.647063  156.510341   \n", "2  14.031703  12.977077      182.090492       0.713931  156.683530   \n", "3  14.253002  14.084499      200.746392       0.552936  156.289496   \n", "4  13.163408  13.950595      183.637368       0.615343  156.560440   \n", "\n", "   height_p75  height_iqr patch_type  patch_index  \n", "0  158.710217    2.338105   positive            0  \n", "1  158.812525    2.302185   positive            1  \n", "2  158.941927    2.258397   positive            2  \n", "3  158.637359    2.347863   positive            3  \n", "4  158.688677    2.128237   positive            4  \n"]}], "source": ["# Combine all features into a single DataFrame for analysis\n", "print(\"Creating feature DataFrame...\")\n", "\n", "all_features = positive_features + negative_features\n", "feature_df = pd.DataFrame(all_features)\n", "\n", "print(f\"Feature DataFrame created with {len(feature_df)} rows and {len(feature_df.columns)} columns\")\n", "print(f\"\\nFeature columns: {list(feature_df.columns)}\")\n", "print(f\"\\nSample data:\")\n", "print(feature_df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Analyze Feature Differences"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing feature differences between pile and non-pile patches...\n", "\n", "Positive patches: 5000\n", "Negative patches: 1\n", "\n", "Numeric features for comparison: 13\n", "['num_points', 'height_min', 'height_max', 'height_range', 'height_mean', 'height_std', 'x_extent', 'y_extent', 'footprint_area', 'point_density', 'height_p25', 'height_p75', 'height_iqr']\n"]}], "source": ["# Compare features between positive and negative patches\n", "print(\"Analyzing feature differences between pile and non-pile patches...\")\n", "\n", "# Separate positive and negative features\n", "positive_df = feature_df[feature_df['patch_type'] == 'positive']\n", "negative_df = feature_df[feature_df['patch_type'] == 'negative']\n", "\n", "print(f\"\\nPositive patches: {len(positive_df)}\")\n", "print(f\"Negative patches: {len(negative_df)}\")\n", "\n", "# Select numeric columns for comparison\n", "numeric_columns = feature_df.select_dtypes(include=[np.number]).columns\n", "numeric_columns = [col for col in numeric_columns if col not in ['patch_index']]\n", "\n", "print(f\"\\nNumeric features for comparison: {len(numeric_columns)}\")\n", "print(numeric_columns)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Feature comparison summary:\n", "================================================================================\n", "num_points          : Positive=123.795 ± 21.774, Negative=105.000 ± nan, Diff=18.795\n", "height_min          : Positive=155.517 ± 1.009, Negative=156.358 ± nan, Diff=-0.841\n", "height_max          : Positive=158.126 ± 1.101, Negative=162.620 ± nan, Diff=-4.494\n", "height_range        : Positive=2.608 ± 0.593, Negative=6.262 ± nan, Diff=-3.654\n", "height_mean         : Positive=157.160 ± 1.016, Negative=158.471 ± nan, Diff=-1.310\n", "height_std          : Positive=0.894 ± 0.302, Negative=1.661 ± nan, Diff=-0.767\n", "x_extent            : Positive=16.767 ± 2.227, Negative=9.109 ± nan, Diff=7.658\n", "y_extent            : Positive=17.673 ± 2.018, Negative=8.982 ± nan, Diff=8.691\n", "footprint_area      : Positive=299.371 ± 63.978, Negative=81.816 ± nan, Diff=217.555\n", "point_density       : Positive=0.431 ± 0.106, Negative=1.283 ± nan, Diff=-0.852\n", "height_p25          : Positive=156.560 ± 1.251, Negative=156.928 ± nan, Diff=-0.368\n", "height_p75          : Positive=157.803 ± 1.109, Negative=159.632 ± nan, Diff=-1.829\n", "height_iqr          : Positive=1.243 ± 1.052, Negative=2.704 ± nan, Diff=-1.461\n", "\n", "Feature comparison completed for 13 features\n"]}], "source": ["# Calculate summary statistics for each feature\n", "print(\"\\nFeature comparison summary:\")\n", "print(\"=\" * 80)\n", "\n", "comparison_stats = []\n", "\n", "for feature in numeric_columns:\n", "    pos_values = positive_df[feature].dropna()\n", "    neg_values = negative_df[feature].dropna()\n", "    \n", "    if len(pos_values) > 0 and len(neg_values) > 0:\n", "        stats = {\n", "            'feature': feature,\n", "            'positive_mean': pos_values.mean(),\n", "            'negative_mean': neg_values.mean(),\n", "            'positive_std': pos_values.std(),\n", "            'negative_std': neg_values.std(),\n", "            'difference': pos_values.mean() - neg_values.mean(),\n", "            'ratio': pos_values.mean() / max(neg_values.mean(), 1e-6)\n", "        }\n", "        comparison_stats.append(stats)\n", "        \n", "        print(f\"{feature:20s}: Positive={stats['positive_mean']:.3f} ± {stats['positive_std']:.3f}, \"\n", "              f\"Negative={stats['negative_mean']:.3f} ± {stats['negative_std']:.3f}, \"\n", "              f\"Diff={stats['difference']:.3f}\")\n", "\n", "comparison_df = pd.DataFrame(comparison_stats)\n", "print(f\"\\nFeature comparison completed for {len(comparison_stats)} features\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Develop Rule-Based Classification"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rule-based classification function defined\n"]}], "source": ["def classify_pile_with_rules(features):\n", "    \"\"\"\n", "    Classify a patch as pile or non-pile using rule-based criteria.\n", "    \n", "    Args:\n", "        features: Dictionary of extracted features\n", "    \n", "    Returns:\n", "        bool: True if classified as pile, False otherwise\n", "        dict: Details about which rules were triggered\n", "    \"\"\"\n", "    score = 0\n", "    rule_details = {}\n", "    \n", "    # Rule 1: Significant height variation (piles are tall structures)\n", "    if features.get('height_range', 0) > 2.0:\n", "        score += 1\n", "        rule_details['height_rule'] = True\n", "    else:\n", "        rule_details['height_rule'] = False\n", "    \n", "    # Rule 2: High point density (piles are dense structures)\n", "    if features.get('point_density', 0) > 50:\n", "        score += 1\n", "        rule_details['density_rule'] = True\n", "    else:\n", "        rule_details['density_rule'] = False\n", "    \n", "    # Rule 3: Vertical structure (significant height standard deviation)\n", "    if features.get('height_std', 0) > 0.5:\n", "        score += 1\n", "        rule_details['vertical_rule'] = True\n", "    else:\n", "        rule_details['vertical_rule'] = False\n", "    \n", "    # Rule 4: Compact footprint (piles have small horizontal extent)\n", "    if (features.get('x_extent', float('inf')) < 3.0 and \n", "        features.get('y_extent', float('inf')) < 3.0):\n", "        score += 1\n", "        rule_details['footprint_rule'] = True\n", "    else:\n", "        rule_details['footprint_rule'] = False\n", "    \n", "    # Classification decision: require at least 2 out of 4 rules\n", "    is_pile = score >= 2\n", "    rule_details['total_score'] = score\n", "    rule_details['classification'] = 'pile' if is_pile else 'non-pile'\n", "    \n", "    return is_pile, rule_details\n", "\n", "print(\"Rule-based classification function defined\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Applying rule-based classification...\n", "Classification completed for 5001 patches\n"]}], "source": ["# Apply rule-based classification to all patches\n", "print(\"Applying rule-based classification...\")\n", "\n", "classifications = []\n", "rule_details_list = []\n", "\n", "for _, row in feature_df.iterrows():\n", "    features = row.to_dict()\n", "    is_pile, rule_details = classify_pile_with_rules(features)\n", "    \n", "    classifications.append(is_pile)\n", "    rule_details_list.append(rule_details)\n", "\n", "# Add classifications to the DataFrame\n", "feature_df['predicted_pile'] = classifications\n", "feature_df['true_pile'] = feature_df['patch_type'] == 'positive'\n", "\n", "print(f\"Classification completed for {len(feature_df)} patches\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Evaluate Classification Performance"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluating classification performance...\n", "\n", "Classification Results:\n", "==================================================\n", "Accuracy:  0.859\n", "Precision: 1.000\n", "Recall:    0.859\n", "F1-Score:  0.924\n", "\n", "Confusion Matrix:\n", "True Negatives:     0\n", "False Positives:    1\n", "False Negatives:  703\n", "True Positives:  4297\n"]}], "source": ["# Calculate classification metrics\n", "print(\"Evaluating classification performance...\")\n", "\n", "true_labels = feature_df['true_pile']\n", "predicted_labels = feature_df['predicted_pile']\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(true_labels, predicted_labels)\n", "tn, fp, fn, tp = cm.ravel()\n", "\n", "# Calculate metrics\n", "accuracy = (tp + tn) / (tp + tn + fp + fn)\n", "precision = tp / (tp + fp) if (tp + fp) > 0 else 0\n", "recall = tp / (tp + fn) if (tp + fn) > 0 else 0\n", "f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "\n", "print(\"\\nClassification Results:\")\n", "print(\"=\" * 50)\n", "print(f\"Accuracy:  {accuracy:.3f}\")\n", "print(f\"Precision: {precision:.3f}\")\n", "print(f\"Recall:    {recall:.3f}\")\n", "print(f\"F1-Score:  {f1_score:.3f}\")\n", "\n", "print(\"\\nConfusion Matrix:\")\n", "print(f\"True Negatives:  {tn:4d}\")\n", "print(f\"False Positives: {fp:4d}\")\n", "print(f\"False Negatives: {fn:4d}\")\n", "print(f\"True Positives:  {tp:4d}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Save Results"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving analysis results...\n", "Feature analysis saved to: ../data/output_runs/pile_detection_20250721_192209/feature_analysis_results.csv\n", "Feature comparison saved to: ../data/output_runs/pile_detection_20250721_192209/feature_comparison.csv\n", "Classification metrics saved to: ../data/output_runs/pile_detection_20250721_192209/classification_metrics.json\n", "\n", "Analysis complete!\n"]}], "source": ["# Save analysis results\n", "print(\"Saving analysis results...\")\n", "\n", "# Save feature DataFrame\n", "results_file = output_dir / \"feature_analysis_results.csv\"\n", "feature_df.to_csv(results_file, index=False)\n", "print(f\"Feature analysis saved to: {results_file}\")\n", "\n", "# Save comparison statistics\n", "comparison_file = output_dir / \"feature_comparison.csv\"\n", "comparison_df.to_csv(comparison_file, index=False)\n", "print(f\"Feature comparison saved to: {comparison_file}\")\n", "\n", "# Save classification metrics\n", "metrics = {\n", "    'accuracy': accuracy,\n", "    'precision': precision,\n", "    'recall': recall,\n", "    'f1_score': f1_score,\n", "    'true_negatives': int(tn),\n", "    'false_positives': int(fp),\n", "    'false_negatives': int(fn),\n", "    'true_positives': int(tp),\n", "    'total_patches': len(feature_df),\n", "    'positive_patches': int(sum(true_labels)),\n", "    'negative_patches': int(sum(~true_labels))\n", "}\n", "\n", "metrics_file = output_dir / \"classification_metrics.json\"\n", "with open(metrics_file, 'w') as f:\n", "    json.dump(metrics, f, indent=2)\n", "print(f\"Classification metrics saved to: {metrics_file}\")\n", "\n", "print(\"\\nAnalysis complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "Analysis and validation is now complete! Here's what we accomplished:\n", "\n", "1. **Loaded prepared patch data** from the first notebook\n", "2. **Extracted geometric features** from all patches\n", "3. **Analyzed feature differences** between pile and non-pile patches\n", "4. **Developed rule-based classification** using 4 geometric criteria\n", "5. **Evaluated classification performance** with standard metrics\n", "6. **Saved all results** for future reference\n", "\n", "The rule-based approach provides interpretable results and can be used as a baseline for more advanced machine learning approaches. The analysis shows which geometric features are most discriminative for pile detection in point cloud data."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}