{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pile Detection Pipeline\n", "\n", "This notebook runs pile detection algorithms on point cloud data and saves detected pile coordinates for validation.\n", "\n", "**Purpose**: Generate detection results for validation against ground truth data\n", "\n", "**Input**: Point cloud data (ground-filtered or aligned)\n", "**Output**: Detected pile coordinates (CSV) for validation\n", "\n", "**Detection Methods**:\n", "1. **Geometric Rule-Based Detection** - DBSCAN clustering + geometric analysis\n", "2. **Enhanced Geometric Detection** - Multi-criteria scoring\n", "3. **Hybrid Detection** - Combination of methods\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"ransac_pmf\"  # Options: csf, pmf, ransac\n", "detection_methods = [\"geometric\", \"enhanced_geometric\"]  # Methods to run\n", "confidence_threshold = 0.5  # Minimum confidence for detection\n", "save_results = True\n", "create_visualizations = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Environment Setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Environment setup complete\n", "Site: trino_enel\n", "Ground method: ransac_pmf\n", "Detection methods: ['geometric', 'enhanced_geometric']\n", "Confidence threshold: 0.5\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Point cloud processing\n", "import open3d as o3d\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.neighbors import NearestNeighbors\n", "from scipy.spatial.distance import cdist\n", "\n", "print(\"Environment setup complete\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Detection methods: {detection_methods}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Configure Paths and Parameters"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory: ../../../../data/output_runs/pile_detection/comprehensive_detection/trino_enel_ransac_pmf_20250721_202034\n", "FIXED Detection Configuration:\n", "- Increased DBSCAN eps (1.2m, 1.0m) for sparse point clouds\n", "- Reduced min_points_per_cluster (15, 20) for better detection\n", "- More permissive height and width tolerances\n", "- Expanded height range for pile detection\n"]}], "source": ["# Configure paths\n", "base_path = Path(\"../../../..\")\n", "data_path = base_path / \"data\"\n", "\n", "# Input point cloud paths (try multiple sources)\n", "possible_point_clouds = [\n", "    data_path / \"processed\" / site_name / \"gcp_alignment_z_corrected\" / f\"{ground_method}\" / \"filtered\" / f\"{site_name}_gcp_aligned_z_corrected_filtered.ply\",\n", "    data_path / \"processed\" / site_name / \"ground_segmentation\" / ground_method / f\"{site_name}_{ground_method}_non_ground.ply\",\n", "    data_path / \"processed\" / site_name / \"alignment\" / f\"{site_name}_aligned_point_cloud.ply\",\n", "    data_path / \"raw\" / site_name / \"point_cloud\" / f\"{site_name}.ply\"\n", "]\n", "\n", "# Output paths\n", "output_base = data_path / \"output_runs\" / \"pile_detection\" / \"comprehensive_detection\"\n", "output_dir = output_base / f\"{site_name}_{ground_method}_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Output directory: {output_dir}\")\n", "\n", "# Detection parameters - FIXED for real pile detection\n", "detection_config = {\n", "    'geometric': {\n", "        'min_height_above_ground': 0.3,  # meters - more permissive\n", "        'max_height_above_ground': 4.0,  # meters - allow taller structures\n", "        'dbscan_eps': 1.2,  # clustering radius - INCREASED for sparse data\n", "        'min_points_per_cluster': 15,  # REDUCED for sparse point clouds\n", "        'max_cluster_size': 2000,  # allow larger clusters\n", "        'min_pile_height': 0.5,  # more permissive height\n", "        'max_pile_width': 2.5   # allow wider structures\n", "    },\n", "    'enhanced_geometric': {\n", "        'min_height_above_ground': 0.4,  # more permissive\n", "        'max_height_above_ground': 3.5,  # allow taller\n", "        'dbscan_eps': 1.0,  # INCREASED clustering radius\n", "        'min_points_per_cluster': 20,  # REDUCED minimum points\n", "        'expected_pile_height': 1.5,  # more realistic expectation\n", "        'expected_pile_width': 0.8,  # slightly wider expectation\n", "        'height_tolerance': 1.2,  # more tolerant\n", "        'width_tolerance': 0.6,  # more tolerant\n", "        'min_aspect_ratio': 1.2,  # less restrictive\n", "        'max_aspect_ratio': 8.0   # allow more elongated structures\n", "    }\n", "}\n", "\n", "print(\"FIXED Detection Configuration:\")\n", "print(\"- Increased DBSCAN eps (1.2m, 1.0m) for sparse point clouds\")\n", "print(\"- Reduced min_points_per_cluster (15, 20) for better detection\")\n", "print(\"- More permissive height and width tolerances\")\n", "print(\"- Expanded height range for pile detection\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud: ../../../../data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "Loaded point cloud: 517,002 points\n", "Point cloud bounds:\n", "  X: 435220.30 to 436796.35\n", "  Y: 5010812.68 to 5012553.07\n", "  Z: 152.43 to 182.42\n", "Estimated ground level: 154.23m\n"]}], "source": ["# Find and load point cloud\n", "point_cloud_file = None\n", "for pc_path in possible_point_clouds:\n", "    if pc_path.exists():\n", "        point_cloud_file = pc_path\n", "        break\n", "\n", "if point_cloud_file is None:\n", "    raise FileNotFoundError(f\"No point cloud found in any of the expected locations: {[str(p) for p in possible_point_clouds]}\")\n", "\n", "print(f\"Loading point cloud: {point_cloud_file}\")\n", "\n", "# Load point cloud\n", "pcd = o3d.io.read_point_cloud(str(point_cloud_file))\n", "points = np.asarray(pcd.points)\n", "\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "\n", "# Estimate ground level\n", "ground_z = np.percentile(points[:, 2], 5)  # Use 5th percentile as ground estimate\n", "print(f\"Estimated ground level: {ground_z:.2f}m\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 21 07:17 \u001b[34mcoordinate_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@ 17 <USER>  <GROUP>   544B Jul 20 17:12 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul 21 12:43 \u001b[34mgcp_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul 21 13:03 \u001b[34mgcp_alignment_z_corrected\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul 14 19:11 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@ 10 <USER>  <GROUP>   320B Jul 16 22:12 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul 16 13:20 \u001b[34mifc_pointclouds_corrected\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 14 15:40 \u001b[34mvalidation\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 18 13:00 \u001b[34mz_deviation\u001b[m\u001b[m\n"]}], "source": ["!ls -lh ../../../../data/processed/trino_enel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Geometric Pile Detection Algorithm"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def geometric_pile_detection(points, ground_z, config):\n", "    \"\"\"Basic geometric pile detection using DBSCAN clustering.\"\"\"\n", "    \n", "    print(\"\\n=== GEOMETRIC PILE DETECTION ===\")\n", "    \n", "    # Step 1: <PERSON>lter elevated points\n", "    min_z = ground_z + config['min_height_above_ground']\n", "    max_z = ground_z + config['max_height_above_ground']\n", "    \n", "    elevated_mask = (points[:, 2] >= min_z) & (points[:, 2] <= max_z)\n", "    elevated_points = points[elevated_mask]\n", "    \n", "    print(f\"Elevated points ({min_z:.1f}m - {max_z:.1f}m): {len(elevated_points):,}\")\n", "    \n", "    if len(elevated_points) < config['min_points_per_cluster']:\n", "        print(\"Insufficient elevated points for detection\")\n", "        return []\n", "    \n", "    # Step 2: Cluster in XY plane\n", "    clustering = DBSCAN(\n", "        eps=config['dbscan_eps'], \n", "        min_samples=config['min_points_per_cluster']\n", "    )\n", "    cluster_labels = clustering.fit_predict(elevated_points[:, :2])\n", "    \n", "    # Step 3: Analyze clusters\n", "    unique_labels = set(cluster_labels)\n", "    unique_labels.discard(-1)  # Remove noise\n", "    \n", "    print(f\"Found {len(unique_labels)} clusters\")\n", "    \n", "    detections = []\n", "    for label in unique_labels:\n", "        cluster_mask = cluster_labels == label\n", "        cluster_points = elevated_points[cluster_mask]\n", "        \n", "        # Filter by cluster size\n", "        if not (config['min_points_per_cluster'] <= len(cluster_points) <= config['max_cluster_size']):\n", "            continue\n", "        \n", "        # Calculate cluster properties\n", "        center = cluster_points.mean(axis=0)\n", "        height = cluster_points[:, 2].max() - cluster_points[:, 2].min()\n", "        width_x = cluster_points[:, 0].max() - cluster_points[:, 0].min()\n", "        width_y = cluster_points[:, 1].max() - cluster_points[:, 1].min()\n", "        max_width = max(width_x, width_y)\n", "        \n", "        # Basic pile criteria\n", "        if height >= config['min_pile_height'] and max_width <= config['max_pile_width']:\n", "            confidence = min(1.0, height / 2.0) * min(1.0, len(cluster_points) / 100)\n", "            \n", "            detection = {\n", "                'x': float(center[0]),\n", "                'y': float(center[1]),\n", "                'z': float(center[2]),\n", "                'confidence': float(confidence),\n", "                'height': float(height),\n", "                'width_x': float(width_x),\n", "                'width_y': float(width_y),\n", "                'point_count': int(len(cluster_points)),\n", "                'detection_method': 'geometric',\n", "                'cluster_id': int(label)\n", "            }\n", "            \n", "            detections.append(detection)\n", "    \n", "    print(f\"Detected {len(detections)} potential piles\")\n", "    return detections"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Enhanced Geometric Detection Algorithm"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def enhanced_geometric_detection(points, ground_z, config):\n", "    \"\"\"Enhanced geometric detection with multi-criteria scoring.\"\"\"\n", "    \n", "    print(\"\\n=== ENHANCED GEOMETRIC DETECTION ===\")\n", "    \n", "    # Step 1: <PERSON>lter elevated points\n", "    min_z = ground_z + config['min_height_above_ground']\n", "    max_z = ground_z + config['max_height_above_ground']\n", "    \n", "    elevated_mask = (points[:, 2] >= min_z) & (points[:, 2] <= max_z)\n", "    elevated_points = points[elevated_mask]\n", "    \n", "    print(f\"Elevated points ({min_z:.1f}m - {max_z:.1f}m): {len(elevated_points):,}\")\n", "    \n", "    if len(elevated_points) < config['min_points_per_cluster']:\n", "        return []\n", "    \n", "    # Step 2: Cluster with tighter parameters\n", "    clustering = DBSCAN(\n", "        eps=config['dbscan_eps'], \n", "        min_samples=config['min_points_per_cluster']\n", "    )\n", "    cluster_labels = clustering.fit_predict(elevated_points[:, :2])\n", "    \n", "    unique_labels = set(cluster_labels)\n", "    unique_labels.discard(-1)\n", "    \n", "    print(f\"Found {len(unique_labels)} clusters\")\n", "    \n", "    detections = []\n", "    for label in unique_labels:\n", "        cluster_mask = cluster_labels == label\n", "        cluster_points = elevated_points[cluster_mask]\n", "        \n", "        # Calculate dimensions\n", "        center = cluster_points.mean(axis=0)\n", "        height = cluster_points[:, 2].max() - cluster_points[:, 2].min()\n", "        width_x = cluster_points[:, 0].max() - cluster_points[:, 0].min()\n", "        width_y = cluster_points[:, 1].max() - cluster_points[:, 1].min()\n", "        max_width = max(width_x, width_y)\n", "        min_width = min(width_x, width_y)\n", "        aspect_ratio = max_width / (min_width + 1e-6)\n", "        \n", "        # Multi-criteria scoring\n", "        # Criterion 1: Height score\n", "        height_diff = abs(height - config['expected_pile_height'])\n", "        height_score = max(0, 1.0 - (height_diff / config['height_tolerance']))\n", "        \n", "        # Criterion 2: Width score\n", "        width_diff = abs(max_width - config['expected_pile_width'])\n", "        width_score = max(0, 1.0 - (width_diff / config['width_tolerance']))\n", "        \n", "        # Criterion 3: Aspect ratio score\n", "        if config['min_aspect_ratio'] <= aspect_ratio <= config['max_aspect_ratio']:\n", "            aspect_score = 0.8\n", "        else:\n", "            aspect_score = 0.3\n", "        \n", "        # Criterion 4: Point density score\n", "        area = width_x * width_y\n", "        density = len(cluster_points) / (area + 1e-6)\n", "        density_score = min(1.0, density / 50.0)  # Normalize to expected density\n", "        \n", "        # Combined confidence score\n", "        confidence = (height_score * 0.3 + width_score * 0.3 + \n", "                     aspect_score * 0.2 + density_score * 0.2)\n", "        \n", "        if confidence >= confidence_threshold:\n", "            detection = {\n", "                'x': float(center[0]),\n", "                'y': float(center[1]),\n", "                'z': float(center[2]),\n", "                'confidence': float(confidence),\n", "                'height': float(height),\n", "                'width_x': float(width_x),\n", "                'width_y': float(width_y),\n", "                'aspect_ratio': float(aspect_ratio),\n", "                'point_count': int(len(cluster_points)),\n", "                'height_score': float(height_score),\n", "                'width_score': float(width_score),\n", "                'aspect_score': float(aspect_score),\n", "                'density_score': float(density_score),\n", "                'detection_method': 'enhanced_geometric',\n", "                'cluster_id': int(label)\n", "            }\n", "            \n", "            detections.append(detection)\n", "    \n", "    print(f\"Detected {len(detections)} high-confidence piles\")\n", "    return detections"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Run Detection Algorithms"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "RUNNING PILE DETECTION ALGORITHMS\n", "============================================================\n", "\n", "Running geometric detection...\n", "\n", "=== GEOMETRIC PILE DETECTION ===\n", "Elevated points (154.5m - 158.2m): 363,421\n", "Found 9 clusters\n", "Detected 6 potential piles\n", "  Total detections: 6\n", "  High confidence (>=0.5): 0\n", "  Processing time: 0.91 seconds\n", "\n", "Running enhanced_geometric detection...\n", "\n", "=== ENHANCED GEOMETRIC DETECTION ===\n", "Elevated points (154.6m - 157.7m): 309,615\n", "Found 0 clusters\n", "Detected 0 high-confidence piles\n", "  Total detections: 0\n", "  High confidence (>=0.5): 0\n", "  Processing time: 0.79 seconds\n", "\n", "============================================================\n", "DETECTION SUMMARY\n", "============================================================\n", "GEOMETRIC: 0 piles detected\n", "ENHANCED_GEOMETRIC: 0 piles detected\n"]}], "source": ["# Run selected detection methods\n", "all_detections = {}\n", "detection_summary = {}\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"RUNNING PILE DETECTION ALGORITHMS\")\n", "print(\"=\" * 60)\n", "\n", "for method in detection_methods:\n", "    print(f\"\\nRunning {method} detection...\")\n", "    start_time = datetime.now()\n", "    \n", "    if method == \"geometric\":\n", "        detections = geometric_pile_detection(points, ground_z, detection_config['geometric'])\n", "    elif method == \"enhanced_geometric\":\n", "        detections = enhanced_geometric_detection(points, ground_z, detection_config['enhanced_geometric'])\n", "    else:\n", "        print(f\"Unknown detection method: {method}\")\n", "        continue\n", "    \n", "    end_time = datetime.now()\n", "    processing_time = (end_time - start_time).total_seconds()\n", "    \n", "    # Filter by confidence threshold\n", "    high_confidence_detections = [d for d in detections if d['confidence'] >= confidence_threshold]\n", "    \n", "    all_detections[method] = high_confidence_detections\n", "    detection_summary[method] = {\n", "        'total_detections': len(detections),\n", "        'high_confidence_detections': len(high_confidence_detections),\n", "        'processing_time_seconds': processing_time,\n", "        'confidence_threshold': confidence_threshold,\n", "        'mean_confidence': np.mean([d['confidence'] for d in high_confidence_detections]) if high_confidence_detections else 0.0,\n", "        'config_used': detection_config[method]\n", "    }\n", "    \n", "    print(f\"  Total detections: {len(detections)}\")\n", "    print(f\"  High confidence (>={confidence_threshold}): {len(high_confidence_detections)}\")\n", "    print(f\"  Processing time: {processing_time:.2f} seconds\")\n", "    if high_confidence_detections:\n", "        print(f\"  Mean confidence: {detection_summary[method]['mean_confidence']:.3f}\")\n", "\n", "print(f\"\\n\" + \"=\" * 60)\n", "print(\"DETECTION SUMMARY\")\n", "print(\"=\" * 60)\n", "for method, summary in detection_summary.items():\n", "    print(f\"{method.upper()}: {summary['high_confidence_detections']} piles detected\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Co<PERSON>ine and Deduplicate Detections"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== COMBINING DETECTIONS ===\n", "Merge distance threshold: 1.0m\n", "No detections to combine\n", "\n", "Final detection count: 0\n"]}], "source": ["def combine_detections(all_detections, merge_distance=1.0):\n", "    \"\"\"Combine detections from multiple methods and remove duplicates.\"\"\"\n", "    \n", "    print(f\"\\n=== COMBINING DETECTIONS ===\")\n", "    print(f\"Merge distance threshold: {merge_distance}m\")\n", "    \n", "    # Collect all detections\n", "    combined_detections = []\n", "    for method, detections in all_detections.items():\n", "        for detection in detections:\n", "            detection['source_method'] = method\n", "            combined_detections.append(detection)\n", "    \n", "    if not combined_detections:\n", "        print(\"No detections to combine\")\n", "        return []\n", "    \n", "    print(f\"Total detections before merging: {len(combined_detections)}\")\n", "    \n", "    # Extract coordinates for distance calculation\n", "    coords = np.array([[d['x'], d['y'], d['z']] for d in combined_detections])\n", "    \n", "    # Calculate pairwise distances\n", "    distances = cdist(coords, coords)\n", "    \n", "    # Group nearby detections\n", "    merged_detections = []\n", "    used_indices = set()\n", "    \n", "    for i, detection in enumerate(combined_detections):\n", "        if i in used_indices:\n", "            continue\n", "        \n", "        # Find all detections within merge distance\n", "        nearby_indices = np.where(distances[i] <= merge_distance)[0]\n", "        nearby_detections = [combined_detections[j] for j in nearby_indices if j not in used_indices]\n", "        \n", "        if len(nearby_detections) == 1:\n", "            # Single detection\n", "            merged_detections.append(nearby_detections[0])\n", "        else:\n", "            # Merge multiple detections\n", "            # Use highest confidence detection as base\n", "            best_detection = max(nearby_detections, key=lambda d: d['confidence'])\n", "            \n", "            # Average coordinates weighted by confidence\n", "            total_confidence = sum(d['confidence'] for d in nearby_detections)\n", "            avg_x = sum(d['x'] * d['confidence'] for d in nearby_detections) / total_confidence\n", "            avg_y = sum(d['y'] * d['confidence'] for d in nearby_detections) / total_confidence\n", "            avg_z = sum(d['z'] * d['confidence'] for d in nearby_detections) / total_confidence\n", "            \n", "            merged_detection = best_detection.copy()\n", "            merged_detection.update({\n", "                'x': float(avg_x),\n", "                'y': float(avg_y),\n", "                'z': float(avg_z),\n", "                'confidence': float(max(d['confidence'] for d in nearby_detections)),\n", "                'merged_from': [d['source_method'] for d in nearby_detections],\n", "                'merge_count': len(nearby_detections)\n", "            })\n", "            \n", "            merged_detections.append(merged_detection)\n", "        \n", "        # Mark indices as used\n", "        used_indices.update(nearby_indices)\n", "    \n", "    print(f\"Final detections after merging: {len(merged_detections)}\")\n", "    return merged_detections\n", "\n", "# Combine detections\n", "final_detections = combine_detections(all_detections, merge_distance=1.0)\n", "\n", "print(f\"\\nFinal detection count: {len(final_detections)}\")\n", "if final_detections:\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    print(f\"Confidence range: {min(confidences):.3f} - {max(confidences):.3f}\")\n", "    print(f\"Mean confidence: {np.mean(confidences):.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Save Detection Results"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "No detections to save\n"]}], "source": ["if save_results and final_detections:\n", "    print(\"\\n=== SAVING DETECTION RESULTS ===\")\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 1. Save detected pile coordinates (CSV)\n", "    detection_data = []\n", "    for i, detection in enumerate(final_detections):\n", "        detection_data.append({\n", "            'pile_id': f\"detected_{i+1:04d}\",\n", "            'x': detection['x'],\n", "            'y': detection['y'],\n", "            'z': detection['z'],\n", "            'confidence': detection['confidence'],\n", "            'height': detection.get('height', 0.0),\n", "            'width_x': detection.get('width_x', 0.0),\n", "            'width_y': detection.get('width_y', 0.0),\n", "            'point_count': detection.get('point_count', 0),\n", "            'detection_method': detection.get('source_method', 'unknown'),\n", "            'merge_count': detection.get('merge_count', 1)\n", "        })\n", "    \n", "    detections_df = pd.DataFrame(detection_data)\n", "    csv_file = output_dir / f\"{site_name}_detected_pile_coordinates_{timestamp}.csv\"\n", "    detections_df.to_csv(csv_file, index=False)\n", "    print(f\"Saved detected pile coordinates: {csv_file}\")\n", "    \n", "    # 2. Save detection metadata (JSON)\n", "    metadata = {\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'detection_timestamp': timestamp,\n", "        'point_cloud_file': str(point_cloud_file),\n", "        'total_input_points': len(points),\n", "        'estimated_ground_level': float(ground_z),\n", "        'confidence_threshold': confidence_threshold,\n", "        'final_detection_count': len(final_detections),\n", "        'detection_methods_used': detection_methods,\n", "        'detection_summary': detection_summary,\n", "        'detection_config': detection_config,\n", "        'output_files': {\n", "            'coordinates_csv': str(csv_file),\n", "            'metadata_json': str(output_dir / f\"{site_name}_detection_metadata_{timestamp}.json\")\n", "        }\n", "    }\n", "    \n", "    metadata_file = output_dir / f\"{site_name}_detection_metadata_{timestamp}.json\"\n", "    with open(metadata_file, 'w') as f:\n", "        json.dump(metadata, f, indent=2)\n", "    print(f\"Saved detection metadata: {metadata_file}\")\n", "    \n", "    # 3. Save individual method results\n", "    for method, detections in all_detections.items():\n", "        if detections:\n", "            method_data = []\n", "            for i, detection in enumerate(detections):\n", "                method_data.append({\n", "                    'pile_id': f\"{method}_{i+1:04d}\",\n", "                    **detection\n", "                })\n", "            \n", "            method_df = pd.DataFrame(method_data)\n", "            method_file = output_dir / f\"{site_name}_{method}_detections_{timestamp}.csv\"\n", "            method_df.to_csv(method_file, index=False)\n", "            print(f\"Saved {method} detections: {method_file}\")\n", "    \n", "    print(f\"\\nAll results saved to: {output_dir}\")\n", "    \n", "elif not final_detections:\n", "    print(\"\\nNo detections to save\")\n", "else:\n", "    print(\"\\nSaving disabled (save_results=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 9: Create Visualizations"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "No detections to visualize\n"]}], "source": ["if create_visualizations and final_detections:\n", "    print(\"\\n=== CREATING VISUALIZATIONS ===\")\n", "    \n", "    # Create comprehensive detection visualization\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Top view - all detections\n", "    detection_coords = np.array([[d['x'], d['y']] for d in final_detections])\n", "    confidences = np.array([d['confidence'] for d in final_detections])\n", "    \n", "    # Sample point cloud for background\n", "    sample_indices = np.random.choice(len(points), min(10000, len(points)), replace=False)\n", "    sample_points = points[sample_indices]\n", "    \n", "    ax1.scatter(sample_points[:, 0], sample_points[:, 1], c='lightgray', s=0.1, alpha=0.3, label='Point Cloud')\n", "    scatter = ax1.scatter(detection_coords[:, 0], detection_coords[:, 1], \n", "                         c=confidences, s=100, cmap='viridis', \n", "                         edgecolors='black', linewidth=1, label='Detected Piles')\n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title(f'Pile Detection Results - Top View\\n{len(final_detections)} piles detected')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.legend()\n", "    plt.colorbar(scatter, ax=ax1, label='Confidence')\n", "    \n", "    # 2. Confidence distribution\n", "    ax2.hist(confidences, bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "    ax2.axvline(confidence_threshold, color='red', linestyle='--', \n", "                label=f'Threshold ({confidence_threshold})')\n", "    ax2.set_xlabel('Confidence Score')\n", "    ax2.set_ylabel('Number of Detections')\n", "    ax2.set_title('Detection Confidence Distribution')\n", "    ax2.grid(True, alpha=0.3)\n", "    ax2.legend()\n", "    \n", "    # 3. Detection method comparison\n", "    method_counts = {}\n", "    for detection in final_detections:\n", "        method = detection.get('source_method', 'unknown')\n", "        method_counts[method] = method_counts.get(method, 0) + 1\n", "    \n", "    if method_counts:\n", "        methods = list(method_counts.keys())\n", "        counts = list(method_counts.values())\n", "        ax3.bar(methods, counts, alpha=0.7, color=['skyblue', 'lightcoral', 'lightgreen'][:len(methods)])\n", "        ax3.set_xlabel('Detection Method')\n", "        ax3.set_ylabel('Number of Detections')\n", "        ax3.set_title('Detections by Method')\n", "        ax3.grid(True, alpha=0.3)\n", "        \n", "        # Add count labels on bars\n", "        for i, count in enumerate(counts):\n", "            ax3.text(i, count + 0.5, str(count), ha='center', va='bottom')\n", "    \n", "    # 4. Height vs Confidence scatter\n", "    heights = [d.get('height', 0) for d in final_detections]\n", "    if any(h > 0 for h in heights):\n", "        ax4.scatter(heights, confidences, alpha=0.7, s=60, color='orange', edgecolors='black')\n", "        ax4.set_xlabel('Pile Height (m)')\n", "        ax4.set_ylabel('Confidence Score')\n", "        ax4.set_title('Height vs Confidence')\n", "        ax4.grid(True, alpha=0.3)\n", "    else:\n", "        ax4.text(0.5, 0.5, 'Height data not available', \n", "                ha='center', va='center', transform=ax4.transAxes, fontsize=12)\n", "        ax4.set_title('Height vs Confidence (No Data)')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_results:\n", "        viz_file = output_dir / f\"{site_name}_detection_visualization_{timestamp}.png\"\n", "        plt.savefig(viz_file, dpi=300, bbox_inches='tight')\n", "        print(f\"Saved visualization: {viz_file}\")\n", "    \n", "    plt.show()\n", "    \n", "elif not final_detections:\n", "    print(\"\\nNo detections to visualize\")\n", "else:\n", "    print(\"\\nVisualization disabled (create_visualizations=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 10: <PERSON><PERSON><PERSON> and Next Steps"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "COMPREHENSIVE PILE DETECTION PIPELINE - SUMMARY\n", "================================================================================\n", "\n", "📊 DETECTION RESULTS:\n", "  Site: trino_enel\n", "  Ground method: ransac_pmf\n", "  Point cloud: trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "  Total input points: 517,002\n", "  Estimated ground level: 154.23m\n", "  Confidence threshold: 0.5\n", "\n", "🔍 DETECTION METHODS USED:\n", "  GEOMETRIC:\n", "    - Total detections: 6\n", "    - High confidence: 0\n", "    - Processing time: 0.91s\n", "    - Mean confidence: 0.000\n", "  ENHANCED_GEOMETRIC:\n", "    - Total detections: 0\n", "    - High confidence: 0\n", "    - Processing time: 0.79s\n", "    - Mean confidence: 0.000\n", "\n", "🎯 FINAL RESULTS:\n", "  Final detected piles: 0\n", "\n", "🔄 NEXT STEPS:\n", "  1. Run validation notebook to compare detected piles against ground truth\n", "  2. Use detected coordinates for as-built vs as-planned analysis\n", "  3. Adjust detection parameters based on validation results\n", "  4. Consider training ML models using detected pile locations\n", "\n", "✅ PIPELINE COMPLETE\n", "================================================================================\n"]}], "source": ["print(\"\\n\" + \"=\" * 80)\n", "print(\"COMPREHENSIVE PILE DETECTION PIPELINE - SUMMARY\")\n", "print(\"=\" * 80)\n", "\n", "print(f\"\\n📊 DETECTION RESULTS:\")\n", "print(f\"  Site: {site_name}\")\n", "print(f\"  Ground method: {ground_method}\")\n", "print(f\"  Point cloud: {point_cloud_file.name}\")\n", "print(f\"  Total input points: {len(points):,}\")\n", "print(f\"  Estimated ground level: {ground_z:.2f}m\")\n", "print(f\"  Confidence threshold: {confidence_threshold}\")\n", "\n", "print(f\"\\n🔍 DETECTION METHODS USED:\")\n", "for method in detection_methods:\n", "    if method in detection_summary:\n", "        summary = detection_summary[method]\n", "        print(f\"  {method.upper()}:\")\n", "        print(f\"    - Total detections: {summary['total_detections']}\")\n", "        print(f\"    - High confidence: {summary['high_confidence_detections']}\")\n", "        print(f\"    - Processing time: {summary['processing_time_seconds']:.2f}s\")\n", "        print(f\"    - Mean confidence: {summary['mean_confidence']:.3f}\")\n", "\n", "print(f\"\\n🎯 FINAL RESULTS:\")\n", "print(f\"  Final detected piles: {len(final_detections)}\")\n", "if final_detections:\n", "    final_confidences = [d['confidence'] for d in final_detections]\n", "    print(f\"  Confidence range: {min(final_confidences):.3f} - {max(final_confidences):.3f}\")\n", "    print(f\"  Mean confidence: {np.mean(final_confidences):.3f}\")\n", "    \n", "    # Method distribution\n", "    method_dist = {}\n", "    for detection in final_detections:\n", "        method = detection.get('source_method', 'unknown')\n", "        method_dist[method] = method_dist.get(method, 0) + 1\n", "    \n", "    print(f\"  Method distribution:\")\n", "    for method, count in method_dist.items():\n", "        print(f\"    - {method}: {count} piles\")\n", "\n", "if save_results and final_detections:\n", "    print(f\"\\n💾 OUTPUT FILES:\")\n", "    print(f\"  Output directory: {output_dir}\")\n", "    print(f\"  Key files:\")\n", "    print(f\"    - Detected coordinates: {site_name}_detected_pile_coordinates_{timestamp}.csv\")\n", "    print(f\"    - Detection metadata: {site_name}_detection_metadata_{timestamp}.json\")\n", "    if create_visualizations:\n", "        print(f\"    - Visualization: {site_name}_detection_visualization_{timestamp}.png\")\n", "    \n", "    for method in detection_methods:\n", "        if method in all_detections and all_detections[method]:\n", "            print(f\"    - {method} results: {site_name}_{method}_detections_{timestamp}.csv\")\n", "\n", "print(f\"\\n🔄 NEXT STEPS:\")\n", "print(f\"  1. Run validation notebook to compare detected piles against ground truth\")\n", "print(f\"  2. Use detected coordinates for as-built vs as-planned analysis\")\n", "print(f\"  3. Adjust detection parameters based on validation results\")\n", "print(f\"  4. Consider training ML models using detected pile locations\")\n", "\n", "print(f\"\\n✅ PIPELINE COMPLETE\")\n", "print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook has successfully implemented a comprehensive pile detection pipeline that:\n", "\n", "### **Detection Capabilities:**\n", "1. **Geometric Detection** - Basic DBSCAN clustering with geometric filtering\n", "2. **Enhanced Geometric Detection** - Multi-criteria scoring with height, width, aspect ratio, and density analysis\n", "3. **Detection Combination** - Merges results from multiple methods and removes duplicates\n", "\n", "### **Key Features:**\n", "- **Configurable Parameters** - Adjustable detection thresholds and criteria\n", "- **Multiple Input Sources** - Supports various point cloud formats and locations\n", "- **Confidence Scoring** - Provides confidence metrics for each detection\n", "- **Comprehensive Output** - Saves coordinates, metadata, and individual method results\n", "- **Visualization** - Creates detailed plots showing detection results\n", "\n", "### **Output Files:**\n", "- **Detected Pile Coordinates** (CSV) - Ready for validation against ground truth\n", "- **Detection Metadata** (JSON) - Complete processing information\n", "- **Method-Specific Results** (CSV) - Individual algorithm outputs\n", "- **Visualizations** (PNG) - Detection analysis plots\n", "\n", "### **Validation Ready:**\n", "The detected pile coordinates are now ready for validation against:\n", "- Ground truth survey data (Trino_PIles.csv)\n", "- KML pile regions\n", "- IFC metadata (as secondary reference)\n", "\n", "This provides the **actual detected pile coordinates** needed for proper validation, rather than comparing metadata sources."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}