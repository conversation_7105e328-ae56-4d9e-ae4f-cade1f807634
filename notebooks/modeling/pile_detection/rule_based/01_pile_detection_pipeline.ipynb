# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "ransac_pmf"  # Options: csf, pmf, ransac
detection_methods = ["geometric", "enhanced_geometric"]  # Methods to run
confidence_threshold = 0.5  # Minimum confidence for detection
save_results = True
create_visualizations = True

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Point cloud processing
import open3d as o3d
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
from scipy.spatial.distance import cdist

print("Environment setup complete")
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Detection methods: {detection_methods}")
print(f"Confidence threshold: {confidence_threshold}")

# Configure paths
base_path = Path("../../../..")
data_path = base_path / "data"

# Input point cloud paths (try multiple sources)
possible_point_clouds = [
    data_path / "processed" / site_name / "gcp_alignment_z_corrected" / f"{ground_method}" / "filtered" / f"{site_name}_gcp_aligned_z_corrected_filtered.ply",
    data_path / "processed" / site_name / "ground_segmentation" / ground_method / f"{site_name}_{ground_method}_non_ground.ply",
    data_path / "processed" / site_name / "alignment" / f"{site_name}_aligned_point_cloud.ply",
    data_path / "raw" / site_name / "point_cloud" / f"{site_name}.ply"
]

# Output paths
output_base = data_path / "output_runs" / "pile_detection" / "comprehensive_detection"
output_dir = output_base / f"{site_name}_{ground_method}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
output_dir.mkdir(parents=True, exist_ok=True)

print(f"Output directory: {output_dir}")

# Detection parameters - FIXED for real pile detection
detection_config = {
    'geometric': {
        'min_height_above_ground': 0.3,  # meters - more permissive
        'max_height_above_ground': 4.0,  # meters - allow taller structures
        'dbscan_eps': 11.74,  # clustering radius - INCREASED for sparse data
        'min_points_per_cluster': 15,  # REDUCED for sparse point clouds
        'max_cluster_size': 2000,  # allow larger clusters
        'min_pile_height': 0.5,  # more permissive height
        'max_pile_width': 2.5   # allow wider structures
    },
    'enhanced_geometric': {
        'min_height_above_ground': 0.4,  # more permissive
        'max_height_above_ground': 3.5,  # allow taller
        'dbscan_eps': 1.0,  # INCREASED clustering radius
        'min_points_per_cluster': 20,  # REDUCED minimum points
        'expected_pile_height': 1.5,  # more realistic expectation
        'expected_pile_width': 0.8,  # slightly wider expectation
        'height_tolerance': 1.2,  # more tolerant
        'width_tolerance': 0.6,  # more tolerant
        'min_aspect_ratio': 1.2,  # less restrictive
        'max_aspect_ratio': 8.0   # allow more elongated structures
    }
}

print("FIXED Detection Configuration:")
print("- Increased DBSCAN eps (1.2m, 1.0m) for sparse point clouds")
print("- Reduced min_points_per_cluster (15, 20) for better detection")
print("- More permissive height and width tolerances")
print("- Expanded height range for pile detection")

# Find and load point cloud
point_cloud_file = None
for pc_path in possible_point_clouds:
    if pc_path.exists():
        point_cloud_file = pc_path
        break

if point_cloud_file is None:
    raise FileNotFoundError(f"No point cloud found in any of the expected locations: {[str(p) for p in possible_point_clouds]}")

print(f"Loading point cloud: {point_cloud_file}")

# Load point cloud
pcd = o3d.io.read_point_cloud(str(point_cloud_file))
points = np.asarray(pcd.points)

print(f"Loaded point cloud: {len(points):,} points")
print(f"Point cloud bounds:")
print(f"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}")
print(f"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}")
print(f"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}")

# Estimate ground level
ground_z = np.percentile(points[:, 2], 5)  # Use 5th percentile as ground estimate
print(f"Estimated ground level: {ground_z:.2f}m")

!ls -lh ../../../../data/processed/trino_enel

def geometric_pile_detection(points, ground_z, config):
    """Basic geometric pile detection using DBSCAN clustering."""
    
    print("\n=== GEOMETRIC PILE DETECTION ===")
    
    # Step 1: Filter elevated points
    min_z = ground_z + config['min_height_above_ground']
    max_z = ground_z + config['max_height_above_ground']
    
    elevated_mask = (points[:, 2] >= min_z) & (points[:, 2] <= max_z)
    elevated_points = points[elevated_mask]
    
    print(f"Elevated points ({min_z:.1f}m - {max_z:.1f}m): {len(elevated_points):,}")
    
    if len(elevated_points) < config['min_points_per_cluster']:
        print("Insufficient elevated points for detection")
        return []
    
    # Step 2: Cluster in XY plane
    clustering = DBSCAN(
        eps=config['dbscan_eps'], 
        min_samples=config['min_points_per_cluster']
    )
    cluster_labels = clustering.fit_predict(elevated_points[:, :2])
    
    # Step 3: Analyze clusters
    unique_labels = set(cluster_labels)
    unique_labels.discard(-1)  # Remove noise
    
    print(f"Found {len(unique_labels)} clusters")
    
    detections = []
    for label in unique_labels:
        cluster_mask = cluster_labels == label
        cluster_points = elevated_points[cluster_mask]
        
        # Filter by cluster size
        if not (config['min_points_per_cluster'] <= len(cluster_points) <= config['max_cluster_size']):
            continue
        
        # Calculate cluster properties
        center = cluster_points.mean(axis=0)
        height = cluster_points[:, 2].max() - cluster_points[:, 2].min()
        width_x = cluster_points[:, 0].max() - cluster_points[:, 0].min()
        width_y = cluster_points[:, 1].max() - cluster_points[:, 1].min()
        max_width = max(width_x, width_y)
        
        # Basic pile criteria
        if height >= config['min_pile_height'] and max_width <= config['max_pile_width']:
            confidence = min(1.0, height / 2.0) * min(1.0, len(cluster_points) / 100)
            
            detection = {
                'x': float(center[0]),
                'y': float(center[1]),
                'z': float(center[2]),
                'confidence': float(confidence),
                'height': float(height),
                'width_x': float(width_x),
                'width_y': float(width_y),
                'point_count': int(len(cluster_points)),
                'detection_method': 'geometric',
                'cluster_id': int(label)
            }
            
            detections.append(detection)
    
    print(f"Detected {len(detections)} potential piles")
    return detections

def enhanced_geometric_detection(points, ground_z, config):
    """Enhanced geometric detection with multi-criteria scoring."""
    
    print("\n=== ENHANCED GEOMETRIC DETECTION ===")
    
    # Step 1: Filter elevated points
    min_z = ground_z + config['min_height_above_ground']
    max_z = ground_z + config['max_height_above_ground']
    
    elevated_mask = (points[:, 2] >= min_z) & (points[:, 2] <= max_z)
    elevated_points = points[elevated_mask]
    
    print(f"Elevated points ({min_z:.1f}m - {max_z:.1f}m): {len(elevated_points):,}")
    
    if len(elevated_points) < config['min_points_per_cluster']:
        return []
    
    # Step 2: Cluster with tighter parameters
    clustering = DBSCAN(
        eps=config['dbscan_eps'], 
        min_samples=config['min_points_per_cluster']
    )
    cluster_labels = clustering.fit_predict(elevated_points[:, :2])
    
    unique_labels = set(cluster_labels)
    unique_labels.discard(-1)
    
    print(f"Found {len(unique_labels)} clusters")
    
    detections = []
    for label in unique_labels:
        cluster_mask = cluster_labels == label
        cluster_points = elevated_points[cluster_mask]
        
        # Calculate dimensions
        center = cluster_points.mean(axis=0)
        height = cluster_points[:, 2].max() - cluster_points[:, 2].min()
        width_x = cluster_points[:, 0].max() - cluster_points[:, 0].min()
        width_y = cluster_points[:, 1].max() - cluster_points[:, 1].min()
        max_width = max(width_x, width_y)
        min_width = min(width_x, width_y)
        aspect_ratio = max_width / (min_width + 1e-6)
        
        # Multi-criteria scoring
        # Criterion 1: Height score
        height_diff = abs(height - config['expected_pile_height'])
        height_score = max(0, 1.0 - (height_diff / config['height_tolerance']))
        
        # Criterion 2: Width score
        width_diff = abs(max_width - config['expected_pile_width'])
        width_score = max(0, 1.0 - (width_diff / config['width_tolerance']))
        
        # Criterion 3: Aspect ratio score
        if config['min_aspect_ratio'] <= aspect_ratio <= config['max_aspect_ratio']:
            aspect_score = 0.8
        else:
            aspect_score = 0.3
        
        # Criterion 4: Point density score
        area = width_x * width_y
        density = len(cluster_points) / (area + 1e-6)
        density_score = min(1.0, density / 50.0)  # Normalize to expected density
        
        # Combined confidence score
        confidence = (height_score * 0.3 + width_score * 0.3 + 
                     aspect_score * 0.2 + density_score * 0.2)
        
        if confidence >= confidence_threshold:
            detection = {
                'x': float(center[0]),
                'y': float(center[1]),
                'z': float(center[2]),
                'confidence': float(confidence),
                'height': float(height),
                'width_x': float(width_x),
                'width_y': float(width_y),
                'aspect_ratio': float(aspect_ratio),
                'point_count': int(len(cluster_points)),
                'height_score': float(height_score),
                'width_score': float(width_score),
                'aspect_score': float(aspect_score),
                'density_score': float(density_score),
                'detection_method': 'enhanced_geometric',
                'cluster_id': int(label)
            }
            
            detections.append(detection)
    
    print(f"Detected {len(detections)} high-confidence piles")
    return detections

# Run selected detection methods
all_detections = {}
detection_summary = {}

print("\n" + "=" * 60)
print("RUNNING PILE DETECTION ALGORITHMS")
print("=" * 60)

for method in detection_methods:
    print(f"\nRunning {method} detection...")
    start_time = datetime.now()
    
    if method == "geometric":
        detections = geometric_pile_detection(points, ground_z, detection_config['geometric'])
    elif method == "enhanced_geometric":
        detections = enhanced_geometric_detection(points, ground_z, detection_config['enhanced_geometric'])
    else:
        print(f"Unknown detection method: {method}")
        continue
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    # Filter by confidence threshold
    high_confidence_detections = [d for d in detections if d['confidence'] >= confidence_threshold]
    
    all_detections[method] = high_confidence_detections
    detection_summary[method] = {
        'total_detections': len(detections),
        'high_confidence_detections': len(high_confidence_detections),
        'processing_time_seconds': processing_time,
        'confidence_threshold': confidence_threshold,
        'mean_confidence': np.mean([d['confidence'] for d in high_confidence_detections]) if high_confidence_detections else 0.0,
        'config_used': detection_config[method]
    }
    
    print(f"  Total detections: {len(detections)}")
    print(f"  High confidence (>={confidence_threshold}): {len(high_confidence_detections)}")
    print(f"  Processing time: {processing_time:.2f} seconds")
    if high_confidence_detections:
        print(f"  Mean confidence: {detection_summary[method]['mean_confidence']:.3f}")

print(f"\n" + "=" * 60)
print("DETECTION SUMMARY")
print("=" * 60)
for method, summary in detection_summary.items():
    print(f"{method.upper()}: {summary['high_confidence_detections']} piles detected")

def combine_detections(all_detections, merge_distance=1.0):
    """Combine detections from multiple methods and remove duplicates."""
    
    print(f"\n=== COMBINING DETECTIONS ===")
    print(f"Merge distance threshold: {merge_distance}m")
    
    # Collect all detections
    combined_detections = []
    for method, detections in all_detections.items():
        for detection in detections:
            detection['source_method'] = method
            combined_detections.append(detection)
    
    if not combined_detections:
        print("No detections to combine")
        return []
    
    print(f"Total detections before merging: {len(combined_detections)}")
    
    # Extract coordinates for distance calculation
    coords = np.array([[d['x'], d['y'], d['z']] for d in combined_detections])
    
    # Calculate pairwise distances
    distances = cdist(coords, coords)
    
    # Group nearby detections
    merged_detections = []
    used_indices = set()
    
    for i, detection in enumerate(combined_detections):
        if i in used_indices:
            continue
        
        # Find all detections within merge distance
        nearby_indices = np.where(distances[i] <= merge_distance)[0]
        nearby_detections = [combined_detections[j] for j in nearby_indices if j not in used_indices]
        
        if len(nearby_detections) == 1:
            # Single detection
            merged_detections.append(nearby_detections[0])
        else:
            # Merge multiple detections
            # Use highest confidence detection as base
            best_detection = max(nearby_detections, key=lambda d: d['confidence'])
            
            # Average coordinates weighted by confidence
            total_confidence = sum(d['confidence'] for d in nearby_detections)
            avg_x = sum(d['x'] * d['confidence'] for d in nearby_detections) / total_confidence
            avg_y = sum(d['y'] * d['confidence'] for d in nearby_detections) / total_confidence
            avg_z = sum(d['z'] * d['confidence'] for d in nearby_detections) / total_confidence
            
            merged_detection = best_detection.copy()
            merged_detection.update({
                'x': float(avg_x),
                'y': float(avg_y),
                'z': float(avg_z),
                'confidence': float(max(d['confidence'] for d in nearby_detections)),
                'merged_from': [d['source_method'] for d in nearby_detections],
                'merge_count': len(nearby_detections)
            })
            
            merged_detections.append(merged_detection)
        
        # Mark indices as used
        used_indices.update(nearby_indices)
    
    print(f"Final detections after merging: {len(merged_detections)}")
    return merged_detections

# Combine detections
final_detections = combine_detections(all_detections, merge_distance=1.0)

print(f"\nFinal detection count: {len(final_detections)}")
if final_detections:
    confidences = [d['confidence'] for d in final_detections]
    print(f"Confidence range: {min(confidences):.3f} - {max(confidences):.3f}")
    print(f"Mean confidence: {np.mean(confidences):.3f}")

if save_results and final_detections:
    print("\n=== SAVING DETECTION RESULTS ===")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 1. Save detected pile coordinates (CSV)
    detection_data = []
    for i, detection in enumerate(final_detections):
        detection_data.append({
            'pile_id': f"detected_{i+1:04d}",
            'x': detection['x'],
            'y': detection['y'],
            'z': detection['z'],
            'confidence': detection['confidence'],
            'height': detection.get('height', 0.0),
            'width_x': detection.get('width_x', 0.0),
            'width_y': detection.get('width_y', 0.0),
            'point_count': detection.get('point_count', 0),
            'detection_method': detection.get('source_method', 'unknown'),
            'merge_count': detection.get('merge_count', 1)
        })
    
    detections_df = pd.DataFrame(detection_data)
    csv_file = output_dir / f"{site_name}_detected_pile_coordinates_{timestamp}.csv"
    detections_df.to_csv(csv_file, index=False)
    print(f"Saved detected pile coordinates: {csv_file}")
    
    # 2. Save detection metadata (JSON)
    metadata = {
        'site_name': site_name,
        'ground_method': ground_method,
        'detection_timestamp': timestamp,
        'point_cloud_file': str(point_cloud_file),
        'total_input_points': len(points),
        'estimated_ground_level': float(ground_z),
        'confidence_threshold': confidence_threshold,
        'final_detection_count': len(final_detections),
        'detection_methods_used': detection_methods,
        'detection_summary': detection_summary,
        'detection_config': detection_config,
        'output_files': {
            'coordinates_csv': str(csv_file),
            'metadata_json': str(output_dir / f"{site_name}_detection_metadata_{timestamp}.json")
        }
    }
    
    metadata_file = output_dir / f"{site_name}_detection_metadata_{timestamp}.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    print(f"Saved detection metadata: {metadata_file}")
    
    # 3. Save individual method results
    for method, detections in all_detections.items():
        if detections:
            method_data = []
            for i, detection in enumerate(detections):
                method_data.append({
                    'pile_id': f"{method}_{i+1:04d}",
                    **detection
                })
            
            method_df = pd.DataFrame(method_data)
            method_file = output_dir / f"{site_name}_{method}_detections_{timestamp}.csv"
            method_df.to_csv(method_file, index=False)
            print(f"Saved {method} detections: {method_file}")
    
    print(f"\nAll results saved to: {output_dir}")
    
elif not final_detections:
    print("\nNo detections to save")
else:
    print("\nSaving disabled (save_results=False)")

if create_visualizations and final_detections:
    print("\n=== CREATING VISUALIZATIONS ===")
    
    # Create comprehensive detection visualization
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Top view - all detections
    detection_coords = np.array([[d['x'], d['y']] for d in final_detections])
    confidences = np.array([d['confidence'] for d in final_detections])
    
    # Sample point cloud for background
    sample_indices = np.random.choice(len(points), min(10000, len(points)), replace=False)
    sample_points = points[sample_indices]
    
    ax1.scatter(sample_points[:, 0], sample_points[:, 1], c='lightgray', s=0.1, alpha=0.3, label='Point Cloud')
    scatter = ax1.scatter(detection_coords[:, 0], detection_coords[:, 1], 
                         c=confidences, s=100, cmap='viridis', 
                         edgecolors='black', linewidth=1, label='Detected Piles')
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title(f'Pile Detection Results - Top View\n{len(final_detections)} piles detected')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    plt.colorbar(scatter, ax=ax1, label='Confidence')
    
    # 2. Confidence distribution
    ax2.hist(confidences, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(confidence_threshold, color='red', linestyle='--', 
                label=f'Threshold ({confidence_threshold})')
    ax2.set_xlabel('Confidence Score')
    ax2.set_ylabel('Number of Detections')
    ax2.set_title('Detection Confidence Distribution')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 3. Detection method comparison
    method_counts = {}
    for detection in final_detections:
        method = detection.get('source_method', 'unknown')
        method_counts[method] = method_counts.get(method, 0) + 1
    
    if method_counts:
        methods = list(method_counts.keys())
        counts = list(method_counts.values())
        ax3.bar(methods, counts, alpha=0.7, color=['skyblue', 'lightcoral', 'lightgreen'][:len(methods)])
        ax3.set_xlabel('Detection Method')
        ax3.set_ylabel('Number of Detections')
        ax3.set_title('Detections by Method')
        ax3.grid(True, alpha=0.3)
        
        # Add count labels on bars
        for i, count in enumerate(counts):
            ax3.text(i, count + 0.5, str(count), ha='center', va='bottom')
    
    # 4. Height vs Confidence scatter
    heights = [d.get('height', 0) for d in final_detections]
    if any(h > 0 for h in heights):
        ax4.scatter(heights, confidences, alpha=0.7, s=60, color='orange', edgecolors='black')
        ax4.set_xlabel('Pile Height (m)')
        ax4.set_ylabel('Confidence Score')
        ax4.set_title('Height vs Confidence')
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(0.5, 0.5, 'Height data not available', 
                ha='center', va='center', transform=ax4.transAxes, fontsize=12)
        ax4.set_title('Height vs Confidence (No Data)')
    
    plt.tight_layout()
    
    if save_results:
        viz_file = output_dir / f"{site_name}_detection_visualization_{timestamp}.png"
        plt.savefig(viz_file, dpi=300, bbox_inches='tight')
        print(f"Saved visualization: {viz_file}")
    
    plt.show()
    
elif not final_detections:
    print("\nNo detections to visualize")
else:
    print("\nVisualization disabled (create_visualizations=False)")

print("\n" + "=" * 80)
print("COMPREHENSIVE PILE DETECTION PIPELINE - SUMMARY")
print("=" * 80)

print(f"\n📊 DETECTION RESULTS:")
print(f"  Site: {site_name}")
print(f"  Ground method: {ground_method}")
print(f"  Point cloud: {point_cloud_file.name}")
print(f"  Total input points: {len(points):,}")
print(f"  Estimated ground level: {ground_z:.2f}m")
print(f"  Confidence threshold: {confidence_threshold}")

print(f"\n🔍 DETECTION METHODS USED:")
for method in detection_methods:
    if method in detection_summary:
        summary = detection_summary[method]
        print(f"  {method.upper()}:")
        print(f"    - Total detections: {summary['total_detections']}")
        print(f"    - High confidence: {summary['high_confidence_detections']}")
        print(f"    - Processing time: {summary['processing_time_seconds']:.2f}s")
        print(f"    - Mean confidence: {summary['mean_confidence']:.3f}")

print(f"\n🎯 FINAL RESULTS:")
print(f"  Final detected piles: {len(final_detections)}")
if final_detections:
    final_confidences = [d['confidence'] for d in final_detections]
    print(f"  Confidence range: {min(final_confidences):.3f} - {max(final_confidences):.3f}")
    print(f"  Mean confidence: {np.mean(final_confidences):.3f}")
    
    # Method distribution
    method_dist = {}
    for detection in final_detections:
        method = detection.get('source_method', 'unknown')
        method_dist[method] = method_dist.get(method, 0) + 1
    
    print(f"  Method distribution:")
    for method, count in method_dist.items():
        print(f"    - {method}: {count} piles")

if save_results and final_detections:
    print(f"\n💾 OUTPUT FILES:")
    print(f"  Output directory: {output_dir}")
    print(f"  Key files:")
    print(f"    - Detected coordinates: {site_name}_detected_pile_coordinates_{timestamp}.csv")
    print(f"    - Detection metadata: {site_name}_detection_metadata_{timestamp}.json")
    if create_visualizations:
        print(f"    - Visualization: {site_name}_detection_visualization_{timestamp}.png")
    
    for method in detection_methods:
        if method in all_detections and all_detections[method]:
            print(f"    - {method} results: {site_name}_{method}_detections_{timestamp}.csv")

print(f"\n🔄 NEXT STEPS:")
print(f"  1. Run validation notebook to compare detected piles against ground truth")
print(f"  2. Use detected coordinates for as-built vs as-planned analysis")
print(f"  3. Adjust detection parameters based on validation results")
print(f"  4. Consider training ML models using detected pile locations")

print(f"\nPIPELINE COMPLETE")
print("=" * 80)