# Import required libraries
import sys
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
from scipy.spatial import cKDTree
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

print("Libraries imported successfully")

# Setup notebook path and import shared utilities
current_path = Path.cwd()
while current_path.name != "notebooks":
    if current_path.parent == current_path:
        raise RuntimeError("Could not find 'notebooks' directory")
    current_path = current_path.parent

notebooks_root = current_path
if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

from shared.config import get_processed_data_path, find_latest_file
print(f"Notebooks root: {notebooks_root}")

# Configuration - These are the main settings for our analysis
site_name = "trino_enel"  # Name of the construction site
ground_method = "ransac_pmf"  # Ground segmentation method used
patch_radius = 5.0  # How far around each pile to extract points (meters)
min_points_per_patch = 100  # Minimum points needed to consider a patch valid
max_positive_samples = 5000  # Maximum number of pile samples to extract
max_negative_samples = 2500  # Maximum number of non-pile samples to extract

print("Configuration:")
print(f"  Site: {site_name}")
print(f"  Ground method: {ground_method}")
print(f"  Patch radius: {patch_radius} meters")
print(f"  Min points per patch: {min_points_per_patch}")
print(f"  Max positive samples: {max_positive_samples}")
print(f"  Max negative samples: {max_negative_samples}")

# Load the aligned point cloud data
print("Loading point cloud data...")

# First try to find the filtered version
alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method / "filtered"
point_cloud_file = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected_filtered.ply")

# If filtered version doesn't exist, use the non-filtered version
if not point_cloud_file.exists():
    print("Filtered version not found, using non-filtered version...")
    alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method
    point_cloud_file = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected.ply")

print(f"Loading from: {point_cloud_file}")

# Load the point cloud using Open3D
point_cloud = o3d.io.read_point_cloud(str(point_cloud_file))
points = np.asarray(point_cloud.points)

print(f"Successfully loaded {len(points):,} points")

# Examine the point cloud data
print("Point cloud information:")
print(f"  Number of points: {len(points):,}")
print(f"  Data shape: {points.shape}")
print(f"  Data type: {points.dtype}")

print("\nCoordinate ranges:")
for i, axis in enumerate(['X', 'Y', 'Z']):
    min_val = points[:, i].min()
    max_val = points[:, i].max()
    range_val = max_val - min_val
    print(f"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)")

print("\nFirst 5 points:")
for i in range(min(5, len(points))):
    print(f"  Point {i+1}: X={points[i,0]:.2f}, Y={points[i,1]:.2f}, Z={points[i,2]:.2f}")

# Load IFC metadata containing pile information
print("Loading IFC metadata...")

ifc_metadata_path = get_processed_data_path(site_name, "ifc_metadata")
metadata_file = find_latest_file(ifc_metadata_path, "*enhanced_metadata.csv")

print(f"Loading from: {metadata_file}")
ifc_data = pd.read_csv(metadata_file)
print(f"Loaded {len(ifc_data):,} IFC elements")

# Show what columns are available
print(f"\nAvailable columns: {list(ifc_data.columns)}")
print(f"\nElement types in data:")
print(ifc_data['Type'].value_counts().head())

# Filter for pile elements using name-based search
print("Extracting pile elements...")

# Find elements with 'Pile' in their name
pile_mask = ifc_data['Name'].str.contains('Pile', case=False, na=False)
pile_data = ifc_data[pile_mask].copy()
print(f"Found {len(pile_data):,} pile elements")

# Extract coordinates and remove any rows with missing coordinates
coordinate_columns = ['X', 'Y', 'Z']
pile_coordinates = pile_data[coordinate_columns].dropna().values
print(f"Valid pile coordinates: {len(pile_coordinates):,}")

# Show coordinate ranges
print("\nPile coordinate ranges:")
for i, axis in enumerate(coordinate_columns):
    min_val = pile_coordinates[:, i].min()
    max_val = pile_coordinates[:, i].max()
    range_val = max_val - min_val
    print(f"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)")

# Show sample pile data
print("\nSample pile data:")
print(pile_data[['Name', 'Type', 'X', 'Y', 'Z']].head())

# Create a spatial index (KD-Tree) for fast point searching
print("Creating spatial index for point cloud...")

# Build KD-Tree for efficient nearest neighbor searches
point_tree = cKDTree(points)
print(f"Spatial index created for {len(points):,} points")

# Test the spatial index with a sample query
if len(pile_coordinates) > 0:
    test_pile = pile_coordinates[0]
    test_indices = point_tree.query_ball_point(test_pile, patch_radius)
    print(f"\nTest query around first pile location:")
    print(f"  Pile location: X={test_pile[0]:.2f}, Y={test_pile[1]:.2f}, Z={test_pile[2]:.2f}")
    print(f"  Points within {patch_radius}m radius: {len(test_indices)}")

# Function to extract points around a location
def extract_patch_around_location(center_point, radius, tree, all_points, min_points=100):
    """
    Extract points within a radius around a center point.
    
    Args:
        center_point: [x, y, z] coordinates of center
        radius: search radius in meters
        tree: KD-Tree for spatial searching
        all_points: array of all point coordinates
        min_points: minimum points required for valid patch
    
    Returns:
        patch_points: array of points in patch, or None if insufficient points
    """
    # Find all points within radius
    point_indices = tree.query_ball_point(center_point, radius)
    
    # Check if we have enough points
    if len(point_indices) < min_points:
        return None
    
    # Extract the actual point coordinates
    patch_points = all_points[point_indices]
    
    return patch_points

print("Patch extraction function defined")

# Extract positive samples (patches around pile locations)
print("Extracting positive samples around pile locations...")

positive_patches = []
positive_info = []
successful_extractions = 0
failed_extractions = 0

# Try different radii if the default doesn't work
radii_to_try = [patch_radius, patch_radius * 1.5, patch_radius * 2.0]

for i, pile_location in enumerate(pile_coordinates):
    if successful_extractions >= max_positive_samples:
        print(f"Reached maximum positive samples ({max_positive_samples})")
        break
    
    patch_extracted = False
    
    # Try different radii to get enough points
    for radius in radii_to_try:
        patch_points = extract_patch_around_location(
            pile_location, radius, point_tree, points, min_points_per_patch
        )
        
        if patch_points is not None:
            positive_patches.append(patch_points)
            positive_info.append({
                'pile_index': i,
                'pile_location': pile_location.tolist(),
                'radius_used': radius,
                'num_points': len(patch_points)
            })
            successful_extractions += 1
            patch_extracted = True
            
            if successful_extractions % 100 == 0:
                print(f"  Extracted {successful_extractions} positive patches...")
            break
    
    if not patch_extracted:
        failed_extractions += 1

print(f"\nPositive sample extraction complete:")
print(f"  Successful extractions: {successful_extractions}")
print(f"  Failed extractions: {failed_extractions}")
print(f"  Success rate: {successful_extractions/(successful_extractions+failed_extractions)*100:.1f}%")

# Extract negative samples (patches from areas without piles)
print("Extracting negative samples from non-pile areas...")

# Calculate point cloud bounds for random sampling
x_min, x_max = points[:, 0].min(), points[:, 0].max()
y_min, y_max = points[:, 1].min(), points[:, 1].max()
z_min, z_max = points[:, 2].min(), points[:, 2].max()

print(f"Point cloud bounds for negative sampling:")
print(f"  X: {x_min:.2f} to {x_max:.2f}")
print(f"  Y: {y_min:.2f} to {y_max:.2f}")
print(f"  Z: {z_min:.2f} to {z_max:.2f}")

# Minimum distance from pile locations for negative samples
min_distance_from_piles = patch_radius * 3.0
print(f"Minimum distance from piles: {min_distance_from_piles:.1f} meters")

# Generate negative samples
negative_patches = []
negative_info = []
negative_attempts = 0
max_attempts = max_negative_samples * 10  # Try up to 10x the target number

print(f"Generating negative samples (target: {max_negative_samples})...")

while len(negative_patches) < max_negative_samples and negative_attempts < max_attempts:
    negative_attempts += 1
    
    # Generate random location within point cloud bounds
    random_x = np.random.uniform(x_min, x_max)
    random_y = np.random.uniform(y_min, y_max)
    random_z = np.random.uniform(z_min, z_max)
    random_location = np.array([random_x, random_y, random_z])
    
    # Check if location is far enough from all pile locations
    distances_to_piles = np.linalg.norm(pile_coordinates - random_location, axis=1)
    min_distance = distances_to_piles.min()
    
    if min_distance >= min_distance_from_piles:
        # Try to extract patch at this location
        patch_points = extract_patch_around_location(
            random_location, patch_radius, point_tree, points, min_points_per_patch
        )
        
        if patch_points is not None:
            negative_patches.append(patch_points)
            negative_info.append({
                'location': random_location.tolist(),
                'radius_used': patch_radius,
                'num_points': len(patch_points),
                'min_distance_to_pile': min_distance
            })
            
            if len(negative_patches) % 100 == 0:
                print(f"  Generated {len(negative_patches)} negative patches...")

print(f"\nNegative sample extraction complete:")
print(f"  Generated samples: {len(negative_patches)}")
print(f"  Attempts made: {negative_attempts}")
print(f"  Success rate: {len(negative_patches)/negative_attempts*100:.1f}%")

# Create output directory with timestamp
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_dir = Path(f"../data/output_runs/pile_detection_{timestamp}")
output_dir.mkdir(parents=True, exist_ok=True)

print(f"Saving prepared data to: {output_dir}")

# Save the output directory path for the next notebook
with open("output_dir.txt", "w") as f:
    f.write(str(output_dir))

print(f"Output directory saved to output_dir.txt")

# Prepare data for saving (convert numpy arrays to lists for JSON)
save_data = {
    'positive_patches': [patch.tolist() for patch in positive_patches],
    'negative_patches': [patch.tolist() for patch in negative_patches],
    'positive_info': positive_info,
    'negative_info': negative_info,
    'pile_coords': pile_coordinates.tolist(),
    'parameters': {
        'site_name': site_name,
        'ground_method': ground_method,
        'patch_radius': patch_radius,
        'min_points_per_patch': min_points_per_patch,
        'max_positive_samples': max_positive_samples,
        'max_negative_samples': max_negative_samples,
        'timestamp': timestamp
    }
}

# Save to JSON file
output_file = output_dir / "extracted_patches.json"
with open(output_file, 'w') as f:
    json.dump(save_data, f, indent=2)

print(f"Data saved to: {output_file}")
print(f"\nSummary of saved data:")
print(f"  Positive patches: {len(positive_patches)}")
print(f"  Negative patches: {len(negative_patches)}")
print(f"  Total pile coordinates: {len(pile_coordinates)}")
print(f"  File size: {output_file.stat().st_size / 1024 / 1024:.1f} MB")