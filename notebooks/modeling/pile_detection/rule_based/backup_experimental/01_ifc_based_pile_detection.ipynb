import sys
from pathlib import Path

# If running inside a notebook, use Path.cwd(); else, use __file__
current_path = Path(__file__).resolve() if '__file__' in globals() else Path.cwd()

# Traverse upward until we find the `notebooks/` directory
while current_path.name != "notebooks":
    if current_path.parent == current_path:
        raise RuntimeError("Could not find 'notebooks' directory in path hierarchy.")
    current_path = current_path.parent

notebooks_root = current_path
print(f"Notebooks root: {notebooks_root}")

# Add to sys.path if not already
if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import get_processed_data_path, find_latest_file

# Parameters
site_name = "trino_enel"
ground_method = "ransac_pmf"
patch_size = 5.0  # meters radius around each pile
min_points_per_patch = 100
save_results = True

import numpy as np
import pandas as pd
import open3d as o3d
from scipy.spatial import cKDTree
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

print("=== IFC-BASED PILE DETECTION ===")
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Patch size: {patch_size}m radius")

# Load aligned point cloud
alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method / "filtered"
aligned_file = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected_filtered.ply")

if not aligned_file.exists():
    print(f"Aligned file not found: {aligned_file}")
    print("Using non-filtered version...")
    alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method
    aligned_file = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected.ply")

print(f"Loading aligned point cloud: {aligned_file}")
pcd = o3d.io.read_point_cloud(str(aligned_file))
points = np.asarray(pcd.points)
print(f"Loaded {len(points):,} points")

# Load IFC metadata
ifc_metadata_path = get_processed_data_path(site_name, "ifc_metadata")
metadata_file = find_latest_file(ifc_metadata_path, "*enhanced_metadata.csv")

print(f"Loading IFC metadata: {metadata_file}")
ifc_df = pd.read_csv(metadata_file)
print(f"Loaded {len(ifc_df):,} IFC elements")

# Filter for pile elements
pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)
pile_df = ifc_df[pile_mask].copy()
print(f"Found {len(pile_df):,} pile elements")

# Extract pile coordinates
coord_cols = ['X', 'Y', 'Z']
pile_coords = pile_df[coord_cols].dropna().values
print(f"Valid pile coordinates: {len(pile_coords):,}")
print(f"Coordinate ranges:")
for i, col in enumerate(coord_cols):
    print(f"  {col}: {pile_coords[:, i].min():.2f} to {pile_coords[:, i].max():.2f}")

def extract_pile_patches(points, pile_coords, patch_radius=8.0, min_points=20):
    """Extract point cloud patches around pile locations"""
    tree = cKDTree(points[:, :2])  # XY search only
    patches = []
    patch_info = []
    
    for i, pile_center in enumerate(pile_coords):
        for radius in [5.0, 7.5, 10.0]:
            indices = tree.query_ball_point(pile_center[:2], radius)
            if len(indices) >= min_points:
                print(f"Pile {i}: using radius {radius} with {len(indices)} points")
                patch_points = points[indices]
                centered_points = patch_points - pile_center
                patches.append(centered_points)
                patch_info.append({
                    'pile_id': i,
                    'center': pile_center,
                    'num_points': len(indices),
                    'label': 1
                })
                break
        else:
            print(f"Pile {i}: skipped, not enough points in any radius")
    
    return patches, patch_info

print("Extracting pile patches...")
pile_patches, pile_patch_info = extract_pile_patches(
    points, pile_coords, patch_size, min_points_per_patch
)

print(f"Extracted {len(pile_patches)} pile patches")
print(f"Average points per patch: {np.mean([info['num_points'] for info in pile_patch_info]):.0f}")

print("Pile coords sample:", pile_coords[:3])
print("Point cloud X range:", points[:,0].min(), "-", points[:,0].max())
print("Point cloud Y range:", points[:,1].min(), "-", points[:,1].max())

print("Pile Y mean:", np.mean(pile_coords[:, 1]))
print("Point cloud Y mean:", np.mean(points[:, 1]))
print("Y delta mean:", np.mean(pile_coords[:, 1]) - np.mean(points[:, 1]))


import numpy as np
from scipy.spatial import cKDTree
from typing import List, Tuple, Dict, Optional
import time

def extract_pile_patches_improved(
    points: np.ndarray, 
    pile_coords: np.ndarray, 
    radii: List[float] = [5.0, 7.5, 10.0, 12.5],
    min_points: int = 100,
    max_points: Optional[int] = None,
    adaptive_radius: bool = True,
    quality_threshold: float = 0.8,
    verbose: bool = True
) -> Tuple[List[np.ndarray], List[Dict]]:
    """
    Enhanced pile patch extraction with improved efficiency and features.
    
    Args:
        points: Point cloud array (N, 3+) with XYZ coordinates
        pile_coords: Pile center coordinates (M, 3+)
        radii: List of radii to try in order of preference
        min_points: Minimum points required for a valid patch
        max_points: Maximum points per patch (None for no limit)
        adaptive_radius: Whether to use adaptive radius based on point density
        quality_threshold: Quality threshold for patch acceptance (0-1)
        verbose: Whether to print progress information
        
    Returns:
        patches: List of centered point cloud patches
        patch_info: List of dictionaries with patch metadata
    """
    
    if verbose:
        print(f"Building spatial index for {len(points)} points...")
    start_time = time.time()
    
    # Build KD-tree for efficient spatial queries
    tree = cKDTree(points[:, :2])
    
    if verbose:
        print(f"Index built in {time.time() - start_time:.2f}s")
    
    patches = []
    patch_info = []
    skipped_piles = []
    
    # Calculate point density for adaptive radius
    if adaptive_radius:
        sample_size = min(1000, len(points))
        sample_indices = np.random.choice(len(points), sample_size, replace=False)
        sample_points = points[sample_indices, :2]
        
        # Estimate local point density
        distances, _ = tree.query(sample_points, k=min(10, len(points)))
        avg_density = np.mean(1.0 / (np.pi * distances[:, -1]**2))
        base_radius = np.sqrt(min_points / (avg_density * np.pi))
        
        if verbose:
            print(f"Estimated point density: {avg_density:.2f} points/unit²")
            print(f"Suggested base radius: {base_radius:.2f}")
    
    successful_extractions = 0
    
    for i, pile_center in enumerate(pile_coords):
        best_patch = None
        best_info = None
        best_quality = 0
        
        # Determine radii to try
        test_radii = radii.copy()
        if adaptive_radius and base_radius > 0:
            # Insert adaptive radius in appropriate position
            adaptive_r = base_radius * (1.2 if i < len(pile_coords) * 0.1 else 1.0)
            test_radii = sorted(set(test_radii + [adaptive_r]))
        
        for radius in test_radii:
            # Query points within radius
            indices = tree.query_ball_point(pile_center[:2], radius)
            num_points = len(indices)
            
            if num_points < min_points:
                continue
                
            # Apply max_points limit if specified
            if max_points and num_points > max_points:
                # Sample points to stay within limit
                indices = np.random.choice(indices, max_points, replace=False)
                num_points = max_points
            
            # Extract patch points
            patch_points = points[indices]
            
            # Calculate patch quality metrics
            quality_score = calculate_patch_quality(
                patch_points, pile_center, radius, min_points
            )
            
            # Check if this patch meets quality threshold
            if quality_score >= quality_threshold and quality_score > best_quality:
                # Center the points relative to pile center
                centered_points = patch_points - pile_center
                
                best_patch = centered_points
                best_quality = quality_score
                best_info = {
                    'pile_id': i,
                    'center': pile_center.copy(),
                    'radius_used': radius,
                    'num_points': num_points,
                    'quality_score': quality_score,
                    'point_density': num_points / (np.pi * radius**2),
                    'label': 1
                }
                
                if verbose and i % 1000 == 0:
                    print(f"Pile {i}: radius {radius:.1f}, {num_points} points, quality {quality_score:.3f}")
                
                # If quality is very good, don't try larger radii
                if quality_score > 0.95:
                    break
        
        # Add the best patch found for this pile
        if best_patch is not None:
            patches.append(best_patch)
            patch_info.append(best_info)
            successful_extractions += 1
        else:
            skipped_piles.append(i)
            if verbose and len(skipped_piles) <= 10:  # Show first 10 skipped piles
                print(f"Pile {i}: skipped, insufficient points/quality in any radius")
    
    if verbose:
        print(f"\nExtraction completed:")
        print(f"- Successfully extracted: {successful_extractions}/{len(pile_coords)} patches")
        print(f"- Skipped piles: {len(skipped_piles)}")
        if patch_info:
            avg_points = np.mean([info['num_points'] for info in patch_info])
            avg_quality = np.mean([info['quality_score'] for info in patch_info])
            radius_stats = {}
            for info in patch_info:
                r = info['radius_used']
                radius_stats[r] = radius_stats.get(r, 0) + 1
            
            print(f"- Average points per patch: {avg_points:.0f}")
            print(f"- Average quality score: {avg_quality:.3f}")
            print(f"- Radius usage: {dict(sorted(radius_stats.items()))}")
    
    return patches, patch_info

def calculate_patch_quality(
    patch_points: np.ndarray, 
    pile_center: np.ndarray, 
    radius: float,
    min_points: int
) -> float:
    """
    Calculate quality score for a patch based on various metrics.
    
    Args:
        patch_points: Points in the patch
        pile_center: Center of the pile
        radius: Radius used for extraction
        min_points: Minimum required points
        
    Returns:
        Quality score between 0 and 1
    """
    if len(patch_points) == 0:
        return 0.0
    
    # Point count score (normalized)
    count_score = min(len(patch_points) / (min_points * 2), 1.0)
    
    # Spatial distribution score
    distances = np.linalg.norm(patch_points[:, :2] - pile_center[:2], axis=1)
    
    # Prefer more uniform distribution across the radius
    normalized_distances = distances / radius
    distribution_score = 1.0 - np.std(normalized_distances)
    distribution_score = max(0, distribution_score)
    
    # Coverage score (how much of the circle is covered)
    if len(patch_points) > 3:
        angles = np.arctan2(
            patch_points[:, 1] - pile_center[1],
            patch_points[:, 0] - pile_center[0]
        )
        angle_bins = np.histogram(angles, bins=8)[0]
        coverage_score = np.sum(angle_bins > 0) / 8.0
    else:
        coverage_score = 0.0
    
    # Height variation score (if Z coordinate available)
    if patch_points.shape[1] > 2:
        height_std = np.std(patch_points[:, 2])
        # Normalize by some expected height variation (adjust as needed)
        height_score = min(height_std / 2.0, 1.0)  # Assume 2m is good variation
    else:
        height_score = 0.5  # Neutral score if no height info
    
    # Combine scores with weights
    quality_score = (
        0.3 * count_score +
        0.25 * distribution_score +
        0.25 * coverage_score +
        0.2 * height_score
    )
    
    return min(quality_score, 1.0)

def filter_patches_by_quality(
    patches: List[np.ndarray], 
    patch_info: List[Dict],
    min_quality: float = 0.7,
    max_patches: Optional[int] = None
) -> Tuple[List[np.ndarray], List[Dict]]:
    """
    Filter patches based on quality score and optionally limit total number.
    
    Args:
        patches: List of patch point clouds
        patch_info: List of patch metadata
        min_quality: Minimum quality score to keep
        max_patches: Maximum number of patches to keep (keeps best ones)
        
    Returns:
        Filtered patches and patch_info
    """
    # Filter by quality
    good_indices = [
        i for i, info in enumerate(patch_info) 
        if info['quality_score'] >= min_quality
    ]
    
    if max_patches and len(good_indices) > max_patches:
        # Sort by quality and keep the best ones
        quality_scores = [patch_info[i]['quality_score'] for i in good_indices]
        sorted_indices = sorted(
            range(len(good_indices)), 
            key=lambda x: quality_scores[x], 
            reverse=True
        )
        good_indices = [good_indices[i] for i in sorted_indices[:max_patches]]
    
    filtered_patches = [patches[i] for i in good_indices]
    filtered_info = [patch_info[i] for i in good_indices]
    
    return filtered_patches, filtered_info

# Example usage with your data
if __name__ == "__main__":
    # Assuming you have 'points' and 'pile_coords' defined
    print("Extracting pile patches with improved method...")
    
    pile_patches, pile_patch_info = extract_pile_patches_improved(
        points=points,  # Your point cloud
        pile_coords=pile_coords,  # Your pile coordinates
        radii=[5.0, 7.5, 10.0, 12.5],  # Extended radius options
        min_points=100,  # Increased from 20 for better quality
        max_points=300,  # Limit to prevent huge patches
        adaptive_radius=True,  # Enable adaptive radius
        quality_threshold=0.6,  # Quality threshold
        verbose=True
    )
    
    # Optional: Further filter by quality
    high_quality_patches, high_quality_info = filter_patches_by_quality(
        pile_patches, pile_patch_info,
        min_quality=0.8,  # Keep only high-quality patches
        max_patches=5000  # Limit total number if needed
    )
    
    print(f"\nFinal results:")
    print(f"- High quality patches: {len(high_quality_patches)}")
    if high_quality_info:
        avg_quality = np.mean([info['quality_score'] for info in high_quality_info])
        print(f"- Average quality: {avg_quality:.3f}")

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Circle
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from scipy.spatial.distance import cdist
from typing import List, Dict, Tuple, Optional

def visualize_pile_detection_overview(
    points: np.ndarray,
    pile_coords: np.ndarray,
    patch_info: List[Dict],
    skipped_indices: Optional[List[int]] = None,
    figsize: Tuple[int, int] = (15, 12),
    point_subsample: int = 10000
) -> None:
    """
    Create comprehensive overview of pile detection results.
    
    Args:
        points: Original point cloud
        pile_coords: All pile coordinates
        patch_info: Information about successfully extracted patches
        skipped_indices: Indices of skipped piles
        figsize: Figure size
        point_subsample: Number of points to show for performance
    """
    
    # Subsample points for visualization
    if len(points) > point_subsample:
        indices = np.random.choice(len(points), point_subsample, replace=False)
        vis_points = points[indices]
    else:
        vis_points = points
    
    # Extract successful pile info
    successful_piles = {info['pile_id']: info for info in patch_info}
    if skipped_indices is None:
        skipped_indices = [i for i in range(len(pile_coords)) if i not in successful_piles]
    
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    fig.suptitle('Pile Detection Analysis', fontsize=16, fontweight='bold')
    
    # 1. Overview with all piles
    ax1 = axes[0, 0]
    ax1.scatter(vis_points[:, 0], vis_points[:, 1], c='lightgray', s=0.5, alpha=0.3, label='Point Cloud')
    
    # Plot successful piles
    if successful_piles:
        successful_coords = pile_coords[[info['pile_id'] for info in patch_info]]
        qualities = [info['quality_score'] for info in patch_info]
        scatter = ax1.scatter(
            successful_coords[:, 0], successful_coords[:, 1],
            c=qualities, cmap='RdYlGn', s=30, edgecolors='black', linewidth=0.5,
            vmin=0.5, vmax=1.0, label='Detected Piles'
        )
        plt.colorbar(scatter, ax=ax1, label='Quality Score')
    
    # Plot skipped piles
    if skipped_indices:
        skipped_coords = pile_coords[skipped_indices]
        ax1.scatter(
            skipped_coords[:, 0], skipped_coords[:, 1],
            c='red', s=30, marker='x', linewidth=2, label='Skipped Piles'
        )
    
    ax1.set_title(f'Detection Overview\n{len(successful_piles)}/{len(pile_coords)} piles detected')
    ax1.legend()
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.axis('equal')
    
    # 2. Quality distribution
    ax2 = axes[0, 1]
    if patch_info:
        qualities = [info['quality_score'] for info in patch_info]
        ax2.hist(qualities, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(np.mean(qualities), color='red', linestyle='--', label=f'Mean: {np.mean(qualities):.3f}')
        ax2.set_xlabel('Quality Score')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Quality Score Distribution')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 3. Radius usage
    ax3 = axes[1, 0]
    if patch_info:
        radii = [info['radius_used'] for info in patch_info]
        unique_radii, counts = np.unique(radii, return_counts=True)
        bars = ax3.bar(range(len(unique_radii)), counts, color='lightcoral', edgecolor='black')
        ax3.set_xlabel('Radius Used')
        ax3.set_ylabel('Number of Patches')
        ax3.set_title('Radius Usage Distribution')
        ax3.set_xticks(range(len(unique_radii)))
        ax3.set_xticklabels([f'{r:.1f}' for r in unique_radii], rotation=45)
        
        # Add count labels on bars
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + max(counts)*0.01,
                    f'{count}', ha='center', va='bottom')
    
    # 4. Point count vs Quality
    ax4 = axes[1, 1]
    if patch_info:
        point_counts = [info['num_points'] for info in patch_info]
        qualities = [info['quality_score'] for info in patch_info]
        scatter = ax4.scatter(point_counts, qualities, alpha=0.6, c='purple')
        ax4.set_xlabel('Number of Points')
        ax4.set_ylabel('Quality Score')
        ax4.set_title('Points vs Quality Relationship')
        ax4.grid(True, alpha=0.3)
        
        # Add correlation info
        correlation = np.corrcoef(point_counts, qualities)[0, 1]
        ax4.text(0.05, 0.95, f'Correlation: {correlation:.3f}', 
                transform=ax4.transAxes, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

def visualize_detection_details(
    points: np.ndarray,
    pile_coords: np.ndarray,
    patch_info: List[Dict],
    region_center: Tuple[float, float],
    region_size: float = 100,
    show_circles: bool = True,
    figsize: Tuple[int, int] = (12, 10)
) -> None:
    """
    Detailed view of detection results in a specific region.
    
    Args:
        points: Original point cloud
        pile_coords: All pile coordinates
        patch_info: Patch information
        region_center: Center of region to visualize
        region_size: Size of region to show
        show_circles: Whether to show detection circles
        figsize: Figure size
    """
    
    # Filter points and piles in region
    x_min, x_max = region_center[0] - region_size/2, region_center[0] + region_size/2
    y_min, y_max = region_center[1] - region_size/2, region_center[1] + region_size/2
    
    # Filter points
    point_mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) & 
                  (points[:, 1] >= y_min) & (points[:, 1] <= y_max))
    region_points = points[point_mask]
    
    # Filter piles
    pile_mask = ((pile_coords[:, 0] >= x_min) & (pile_coords[:, 0] <= x_max) & 
                 (pile_coords[:, 1] >= y_min) & (pile_coords[:, 1] <= y_max))
    region_pile_coords = pile_coords[pile_mask]
    region_pile_indices = np.where(pile_mask)[0]
    
    # Filter patch info for this region
    region_patch_info = [info for info in patch_info if info['pile_id'] in region_pile_indices]
    successful_ids = {info['pile_id'] for info in region_patch_info}
    
    plt.figure(figsize=figsize)
    
    # Plot points
    if len(region_points) > 0:
        plt.scatter(region_points[:, 0], region_points[:, 1], 
                   c='lightblue', s=1, alpha=0.5, label='Point Cloud')
    
    # Plot successful detections
    for info in region_patch_info:
        pile_coord = pile_coords[info['pile_id']]
        quality = info['quality_score']
        radius = info['radius_used']
        
        # Color based on quality
        color = plt.cm.RdYlGn(quality)
        
        # Plot pile center
        plt.scatter(pile_coord[0], pile_coord[1], 
                   c=[color], s=100, edgecolors='black', linewidth=1,
                   label='Detected Pile' if info == region_patch_info[0] else "")
        
        # Show detection circle
        if show_circles:
            circle = Circle((pile_coord[0], pile_coord[1]), radius, 
                          fill=False, color=color, linewidth=2, alpha=0.7)
            plt.gca().add_patch(circle)
        
        # Add labels
        plt.text(pile_coord[0] + radius*0.3, pile_coord[1] + radius*0.3,
                f'ID:{info["pile_id"]}\nQ:{quality:.2f}\nP:{info["num_points"]}',
                fontsize=8, bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    # Plot skipped piles
    skipped_piles = [i for i in region_pile_indices if i not in successful_ids]
    if skipped_piles:
        skipped_coords = pile_coords[skipped_piles]
        plt.scatter(skipped_coords[:, 0], skipped_coords[:, 1],
                   c='red', s=100, marker='x', linewidth=3, label='Skipped Piles')
        
        for i, coord in zip(skipped_piles, skipped_coords):
            plt.text(coord[0] + 2, coord[1] + 2, f'ID:{i}',
                    fontsize=8, color='red', fontweight='bold')
    
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.title(f'Detection Details - Region ({region_center[0]:.0f}, {region_center[1]:.0f})')
    plt.legend()
    plt.axis('equal')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

def create_detection_heatmap(
    pile_coords: np.ndarray,
    patch_info: List[Dict],
    grid_size: int = 50,
    figsize: Tuple[int, int] = (12, 8)
) -> None:
    """
    Create heatmap showing detection success rate across the area.
    
    Args:
        pile_coords: All pile coordinates
        patch_info: Patch information
        grid_size: Size of grid for heatmap
        figsize: Figure size
    """
    
    successful_ids = {info['pile_id'] for info in patch_info}
    
    # Create grid
    x_min, x_max = pile_coords[:, 0].min(), pile_coords[:, 0].max()
    y_min, y_max = pile_coords[:, 1].min(), pile_coords[:, 1].max()
    
    x_edges = np.linspace(x_min, x_max, grid_size + 1)
    y_edges = np.linspace(y_min, y_max, grid_size + 1)
    
    # Count total and successful piles in each cell
    total_grid = np.zeros((grid_size, grid_size))
    success_grid = np.zeros((grid_size, grid_size))
    
    for i, coord in enumerate(pile_coords):
        x_idx = min(np.searchsorted(x_edges, coord[0]) - 1, grid_size - 1)
        y_idx = min(np.searchsorted(y_edges, coord[1]) - 1, grid_size - 1)
        
        total_grid[y_idx, x_idx] += 1
        if i in successful_ids:
            success_grid[y_idx, x_idx] += 1
    
    # Calculate success rate
    success_rate = np.divide(success_grid, total_grid, 
                            out=np.zeros_like(success_grid), 
                            where=total_grid > 0)
    
    # Mask cells with no piles
    success_rate = np.ma.masked_where(total_grid == 0, success_rate)
    
    plt.figure(figsize=figsize)
    
    # Create heatmap
    im = plt.imshow(success_rate, extent=[x_min, x_max, y_min, y_max], 
                    origin='lower', cmap='RdYlGn', vmin=0, vmax=1, alpha=0.8)
    
    # Add colorbar
    cbar = plt.colorbar(im, label='Detection Success Rate')
    
    # Overlay pile locations
    successful_coords = pile_coords[[info['pile_id'] for info in patch_info]]
    skipped_indices = [i for i in range(len(pile_coords)) if i not in successful_ids]
    
    if len(successful_coords) > 0:
        plt.scatter(successful_coords[:, 0], successful_coords[:, 1], 
                   c='green', s=10, alpha=0.6, label='Detected')
    
    if skipped_indices:
        skipped_coords = pile_coords[skipped_indices]
        plt.scatter(skipped_coords[:, 0], skipped_coords[:, 1], 
                   c='red', s=10, alpha=0.8, label='Skipped')
    
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.title('Pile Detection Success Rate Heatmap')
    plt.legend()
    plt.tight_layout()
    plt.show()

def analyze_spatial_patterns(
    pile_coords: np.ndarray,
    patch_info: List[Dict],
    figsize: Tuple[int, int] = (15, 5)
) -> None:
    """
    Analyze spatial patterns in detection success.
    
    Args:
        pile_coords: All pile coordinates
        patch_info: Patch information
        figsize: Figure size
    """
    
    successful_ids = {info['pile_id'] for info in patch_info}
    skipped_indices = [i for i in range(len(pile_coords)) if i not in successful_ids]
    
    fig, axes = plt.subplots(1, 3, figsize=figsize)
    
    # 1. Distance to nearest neighbor analysis
    ax1 = axes[0]
    
    # Calculate nearest neighbor distances
    distances_all = cdist(pile_coords, pile_coords)
    np.fill_diagonal(distances_all, np.inf)
    nearest_distances = np.min(distances_all, axis=1)
    
    successful_distances = nearest_distances[[info['pile_id'] for info in patch_info]]
    skipped_distances = nearest_distances[skipped_indices] if skipped_indices else []
    
    if len(successful_distances) > 0:
        ax1.hist(successful_distances, bins=30, alpha=0.7, label='Detected', color='green')
    if len(skipped_distances) > 0:
        ax1.hist(skipped_distances, bins=30, alpha=0.7, label='Skipped', color='red')
    
    ax1.set_xlabel('Distance to Nearest Pile')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Nearest Neighbor Distance')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Spatial clustering analysis
    ax2 = axes[1]
    
    if len(successful_distances) > 0 and len(skipped_distances) > 0:
        # Box plot comparison
        data_to_plot = [successful_distances, skipped_distances]
        box = ax2.boxplot(data_to_plot, labels=['Detected', 'Skipped'], patch_artist=True)
        box['boxes'][0].set_facecolor('green')
        box['boxes'][0].set_alpha(0.7)
        if len(box['boxes']) > 1:
            box['boxes'][1].set_facecolor('red')
            box['boxes'][1].set_alpha(0.7)
    
    ax2.set_ylabel('Distance to Nearest Pile')
    ax2.set_title('Distance Distribution Comparison')
    ax2.grid(True, alpha=0.3)
    
    # 3. Quality vs spatial density
    ax3 = axes[2]
    
    if patch_info:
        # Calculate local density for each successful pile
        local_densities = []
        qualities = []
        
        for info in patch_info:
            pile_id = info['pile_id']
            pile_coord = pile_coords[pile_id]
            
            # Count piles within 50 units
            distances = np.linalg.norm(pile_coords - pile_coord, axis=1)
            local_density = np.sum(distances < 50) - 1  # Exclude self
            
            local_densities.append(local_density)
            qualities.append(info['quality_score'])
        
        scatter = ax3.scatter(local_densities, qualities, alpha=0.6, c='purple')
        ax3.set_xlabel('Local Pile Density (within 50 units)')
        ax3.set_ylabel('Quality Score')
        ax3.set_title('Quality vs Local Density')
        ax3.grid(True, alpha=0.3)
        
        # Add correlation
        if len(local_densities) > 1:
            correlation = np.corrcoef(local_densities, qualities)[0, 1]
            ax3.text(0.05, 0.95, f'Correlation: {correlation:.3f}', 
                    transform=ax3.transAxes, 
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

# Example usage with your data
def main_visualization(points, pile_coords, patch_info):
    """Main function to run all visualizations"""
    
    # Calculate skipped indices
    successful_ids = {info['pile_id'] for info in patch_info}
    skipped_indices = [i for i in range(len(pile_coords)) if i not in successful_ids]
    
    print("Creating visualizations...")
    
    # 1. Overview
    visualize_pile_detection_overview(points, pile_coords, patch_info, skipped_indices)
    
    # 2. Detailed view of a region (adjust coordinates as needed)
    if len(pile_coords) > 0:
        center_x, center_y = np.mean(pile_coords[:, 0]), np.mean(pile_coords[:, 1])
        visualize_detection_details(points, pile_coords, patch_info, 
                                   (center_x, center_y), region_size=200)
    
    # 3. Heatmap
    create_detection_heatmap(pile_coords, patch_info)
    
    # 4. Spatial analysis
    analyze_spatial_patterns(pile_coords, patch_info)
    
    print("Visualization complete!")

# Run the visualizations
if __name__ == "__main__":
    # Assuming you have points, pile_coords, and patch_info from your extraction
    main_visualization(points, pile_coords, high_quality_info)

import xml.etree.ElementTree as ET
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon as MPLPolygon
from shapely.geometry import Point, Polygon
from shapely.ops import transform
import pyproj
from typing import List, Dict, Tuple, Optional
import os
import re
from dataclasses import dataclass

@dataclass
class KMLRegion:
    """Class to store KML region information"""
    name: str
    description: str
    coordinates: np.ndarray
    polygon: Polygon

class KMLPileValidator:
    """Validator for comparing detected piles with KML regions"""
    
    def __init__(self, kml_file_path: str):
        """
        Initialize validator with KML file.
        
        Args:
            kml_file_path: Path to the KML file
        """
        self.kml_file_path = kml_file_path
        self.regions = []
        self.coordinate_transformer = None
        self._parse_kml()
        
    def _parse_kml(self):
        """Parse KML file and extract polygon regions"""
        try:
            tree = ET.parse(self.kml_file_path)
            root = tree.getroot()
            
            # Handle KML namespace
            namespace = {'kml': 'http://www.opengis.net/kml/2.2'}
            if root.tag.startswith('{'):
                # Extract namespace from root tag
                ns_match = re.match(r'\{([^}]+)\}', root.tag)
                if ns_match:
                    namespace = {'kml': ns_match.group(1)}
            
            # Find all placemarks
            placemarks = root.findall('.//kml:Placemark', namespace)
            if not placemarks:
                # Try without namespace
                placemarks = root.findall('.//Placemark')
            
            print(f"Found {len(placemarks)} placemarks in KML file")
            
            for i, placemark in enumerate(placemarks):
                try:
                    # Get name and description
                    name_elem = placemark.find('kml:name', namespace) or placemark.find('name')
                    description_elem = placemark.find('kml:description', namespace) or placemark.find('description')
                    
                    name = name_elem.text if name_elem is not None else f"Region_{i}"
                    description = description_elem.text if description_elem is not None else "No description"
                    
                    # Find polygon coordinates
                    coords_elem = placemark.find('.//kml:coordinates', namespace) or placemark.find('.//coordinates')
                    
                    if coords_elem is not None:
                        coords_text = coords_elem.text.strip()
                        coordinates = self._parse_coordinates(coords_text)
                        
                        if len(coordinates) >= 3:  # Need at least 3 points for a polygon
                            # Create Shapely polygon
                            polygon = Polygon(coordinates[:, :2])  # Use only X,Y coordinates
                            
                            region = KMLRegion(
                                name=name,
                                description=description,
                                coordinates=coordinates,
                                polygon=polygon
                            )
                            self.regions.append(region)
                            
                except Exception as e:
                    print(f"Error parsing placemark {i}: {e}")
                    continue
            
            print(f"Successfully parsed {len(self.regions)} regions")
            
        except Exception as e:
            print(f"Error parsing KML file: {e}")
            raise
    
    def _parse_coordinates(self, coords_text: str) -> np.ndarray:
        """
        Parse coordinate string from KML.
        
        Args:
            coords_text: Coordinate string from KML
            
        Returns:
            Array of coordinates [lon, lat, alt]
        """
        coordinates = []
        
        # Split by whitespace and newlines
        coord_pairs = coords_text.split()
        
        for pair in coord_pairs:
            if pair.strip():
                try:
                    parts = pair.split(',')
                    if len(parts) >= 2:
                        lon = float(parts[0])
                        lat = float(parts[1])
                        alt = float(parts[2]) if len(parts) > 2 else 0.0
                        coordinates.append([lon, lat, alt])
                except ValueError:
                    continue
        
        return np.array(coordinates)
    
    def setup_coordinate_transform(self, target_crs: str = "EPSG:32632"):
        """
        Setup coordinate transformation from WGS84 (KML) to target CRS.
        
        Args:
            target_crs: Target coordinate reference system
        """
        try:
            # Create transformer from WGS84 to target CRS
            transformer = pyproj.Transformer.from_crs("EPSG:4326", target_crs, always_xy=True)
            self.coordinate_transformer = transformer
            
            # Transform all polygon coordinates
            for region in self.regions:
                # Transform coordinates
                lon_lat = region.coordinates[:, :2]
                x, y = transformer.transform(lon_lat[:, 0], lon_lat[:, 1])
                transformed_coords = np.column_stack([x, y])
                
                # Update polygon with transformed coordinates
                region.polygon = Polygon(transformed_coords)
                
            print(f"Transformed {len(self.regions)} regions to {target_crs}")
            
        except Exception as e:
            print(f"Error setting up coordinate transform: {e}")
            print("Continuing with original coordinates (assuming they match your pile coordinates)")
    
    def validate_pile_detections(
        self, 
        pile_coords: np.ndarray, 
        patch_info: List[Dict],
        buffer_distance: float = 5.0
    ) -> Dict:
        """
        Validate pile detections against KML regions.
        
        Args:
            pile_coords: Array of pile coordinates
            patch_info: List of patch information dictionaries
            buffer_distance: Buffer around regions for tolerance
            
        Returns:
            Dictionary with validation results
        """
        successful_ids = {info['pile_id'] for info in patch_info}
        
        # Create buffered regions for tolerance
        buffered_regions = []
        for region in self.regions:
            try:
                buffered = region.polygon.buffer(buffer_distance)
                buffered_regions.append(buffered)
            except Exception as e:
                print(f"Error buffering region {region.name}: {e}")
                buffered_regions.append(region.polygon)
        
        # Validate each pile
        validation_results = {
            'total_piles': len(pile_coords),
            'detected_piles': len(successful_ids),
            'skipped_piles': len(pile_coords) - len(successful_ids),
            'piles_in_regions': 0,
            'detected_in_regions': 0,
            'false_positives': 0,  # Detected outside regions
            'missed_in_regions': 0,  # Not detected but in regions
            'pile_region_mapping': [],
            'detection_stats_by_region': []
        }
        
        # Check each pile against all regions
        for pile_id, pile_coord in enumerate(pile_coords):
            pile_point = Point(pile_coord[0], pile_coord[1])
            is_detected = pile_id in successful_ids
            in_any_region = False
            containing_regions = []
            
            # Check against all regions
            for region_idx, (region, buffered_region) in enumerate(zip(self.regions, buffered_regions)):
                if buffered_region.contains(pile_point):
                    in_any_region = True
                    containing_regions.append(region_idx)
            
            # Record pile information
            pile_info = {
                'pile_id': pile_id,
                'coordinates': pile_coord,
                'is_detected': is_detected,
                'in_region': in_any_region,
                'containing_regions': containing_regions
            }
            validation_results['pile_region_mapping'].append(pile_info)
            
            # Update statistics
            if in_any_region:
                validation_results['piles_in_regions'] += 1
                if is_detected:
                    validation_results['detected_in_regions'] += 1
                else:
                    validation_results['missed_in_regions'] += 1
            elif is_detected:
                validation_results['false_positives'] += 1
        
        # Calculate per-region statistics
        for region_idx, region in enumerate(self.regions):
            region_piles = [p for p in validation_results['pile_region_mapping'] 
                           if region_idx in p['containing_regions']]
            
            total_in_region = len(region_piles)
            detected_in_region = sum(1 for p in region_piles if p['is_detected'])
            
            region_stats = {
                'region_name': region.name,
                'region_description': region.description,
                'total_piles': total_in_region,
                'detected_piles': detected_in_region,
                'detection_rate': detected_in_region / total_in_region if total_in_region > 0 else 0
            }
            validation_results['detection_stats_by_region'].append(region_stats)
        
        # Calculate overall metrics
        if validation_results['piles_in_regions'] > 0:
            validation_results['detection_rate_in_regions'] = (
                validation_results['detected_in_regions'] / validation_results['piles_in_regions']
            )
        else:
            validation_results['detection_rate_in_regions'] = 0
        
        if validation_results['detected_piles'] > 0:
            validation_results['precision'] = (
                validation_results['detected_in_regions'] / validation_results['detected_piles']
            )
        else:
            validation_results['precision'] = 0
        
        return validation_results
    
    def print_validation_summary(self, validation_results: Dict):
        """Print summary of validation results"""
        print("="*60)
        print("PILE DETECTION VALIDATION SUMMARY")
        print("="*60)
        
        print(f"\nOverall Statistics:")
        print(f"  Total piles: {validation_results['total_piles']:,}")
        print(f"  Detected piles: {validation_results['detected_piles']:,}")
        print(f"  Skipped piles: {validation_results['skipped_piles']:,}")
        print(f"  Detection rate: {validation_results['detected_piles']/validation_results['total_piles']*100:.1f}%")
        
        print(f"\nRegion-based Validation:")
        print(f"  Piles in KML regions: {validation_results['piles_in_regions']:,}")
        print(f"  Detected in regions: {validation_results['detected_in_regions']:,}")
        print(f"  Missed in regions: {validation_results['missed_in_regions']:,}")
        print(f"  False positives (detected outside regions): {validation_results['false_positives']:,}")
        
        print(f"\nPerformance Metrics:")
        print(f"  Detection rate in regions: {validation_results['detection_rate_in_regions']*100:.1f}%")
        print(f"  Precision (detected in regions / total detected): {validation_results['precision']*100:.1f}%")
        
        print(f"\nPer-Region Statistics:")
        for stats in validation_results['detection_stats_by_region']:
            if stats['total_piles'] > 0:
                print(f"  {stats['region_name']}: {stats['detected_piles']}/{stats['total_piles']} "
                      f"({stats['detection_rate']*100:.1f}%) - {stats['region_description']}")
    
    def visualize_validation(
        self, 
        pile_coords: np.ndarray, 
        patch_info: List[Dict],
        validation_results: Dict,
        figsize: Tuple[int, int] = (15, 12),
        show_region_labels: bool = True
    ):
        """
        Visualize validation results.
        
        Args:
            pile_coords: Array of pile coordinates
            patch_info: List of patch information
            validation_results: Results from validation
            figsize: Figure size
            show_region_labels: Whether to show region labels
        """
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        fig.suptitle('Pile Detection Validation Against KML Regions', fontsize=16, fontweight='bold')
        
        successful_ids = {info['pile_id'] for info in patch_info}
        
        # Main validation plot
        ax1 = axes[0, 0]
        
        # Plot KML regions
        for i, region in enumerate(self.regions):
            if hasattr(region.polygon, 'exterior'):
                coords = np.array(region.polygon.exterior.coords)
                polygon = MPLPolygon(coords, fill=False, edgecolor='blue', linewidth=2, alpha=0.7)
                ax1.add_patch(polygon)
                
                if show_region_labels:
                    centroid = region.polygon.centroid
                    ax1.text(centroid.x, centroid.y, f'R{i}', fontsize=8, ha='center', va='center',
                            bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # Plot piles with validation status
        for pile_info in validation_results['pile_region_mapping']:
            pile_id = pile_info['pile_id']
            coord = pile_info['coordinates']
            
            if pile_info['in_region'] and pile_info['is_detected']:
                # True positive
                color, marker, label = 'green', 'o', 'True Positive'
            elif pile_info['in_region'] and not pile_info['is_detected']:
                # False negative (missed)
                color, marker, label = 'orange', 's', 'False Negative'
            elif not pile_info['in_region'] and pile_info['is_detected']:
                # False positive
                color, marker, label = 'red', '^', 'False Positive'
            else:
                # True negative (correctly not detected outside regions)
                color, marker, label = 'gray', 'x', 'True Negative'
            
            ax1.scatter(coord[0], coord[1], c=color, marker=marker, s=30, alpha=0.7)
        
        # Create legend
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=8, label='True Positive'),
            plt.Line2D([0], [0], marker='s', color='w', markerfacecolor='orange', markersize=8, label='False Negative'),
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='red', markersize=8, label='False Positive'),
            plt.Line2D([0], [0], marker='x', color='w', markerfacecolor='gray', markersize=8, label='True Negative'),
            plt.Line2D([0], [0], color='blue', linewidth=2, label='KML Regions')
        ]
        ax1.legend(handles=legend_elements, loc='upper right')
        ax1.set_title('Validation Overview')
        ax1.set_xlabel('X')
        ax1.set_ylabel('Y')
        ax1.axis('equal')
        ax1.grid(True, alpha=0.3)
        
        # Detection rate by region
        ax2 = axes[0, 1]
        region_stats = validation_results['detection_stats_by_region']
        region_stats_with_piles = [s for s in region_stats if s['total_piles'] > 0]
        
        if region_stats_with_piles:
            names = [s['region_name'][:10] for s in region_stats_with_piles]  # Truncate names
            rates = [s['detection_rate'] * 100 for s in region_stats_with_piles]
            totals = [s['total_piles'] for s in region_stats_with_piles]
            
            bars = ax2.bar(range(len(names)), rates, color='skyblue', edgecolor='black')
            ax2.set_xlabel('KML Regions')
            ax2.set_ylabel('Detection Rate (%)')
            ax2.set_title('Detection Rate by Region')
            ax2.set_xticks(range(len(names)))
            ax2.set_xticklabels(names, rotation=45, ha='right')
            ax2.grid(True, alpha=0.3)
            
            # Add pile count labels on bars
            for bar, total in zip(bars, totals):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'n={total}', ha='center', va='bottom', fontsize=8)
        
        # Confusion matrix-style plot
        ax3 = axes[1, 0]
        
        true_pos = validation_results['detected_in_regions']
        false_neg = validation_results['missed_in_regions'] 
        false_pos = validation_results['false_positives']
        true_neg = (validation_results['total_piles'] - validation_results['piles_in_regions'] - 
                   validation_results['false_positives'])
        
        confusion_data = np.array([[true_pos, false_neg], [false_pos, true_neg]])
        im = ax3.imshow(confusion_data, cmap='Blues', alpha=0.7)
        
        # Add text annotations
        for i in range(2):
            for j in range(2):
                text = ax3.text(j, i, confusion_data[i, j], ha="center", va="center",
                              color="black", fontsize=14, fontweight='bold')
        
        ax3.set_xticks([0, 1])
        ax3.set_yticks([0, 1])
        ax3.set_xticklabels(['In Region', 'Outside Region'])
        ax3.set_yticklabels(['Detected', 'Not Detected'])
        ax3.set_xlabel('Actual')
        ax3.set_ylabel('Predicted')
        ax3.set_title('Confusion Matrix')
        
        # Summary statistics
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        summary_text = f"""
        VALIDATION SUMMARY
        
        Total Piles: {validation_results['total_piles']:,}
        Detected: {validation_results['detected_piles']:,}
        
        In KML Regions: {validation_results['piles_in_regions']:,}
        Detected in Regions: {validation_results['detected_in_regions']:,}
        
        Detection Rate in Regions: {validation_results['detection_rate_in_regions']*100:.1f}%
        Precision: {validation_results['precision']*100:.1f}%
        
        False Positives: {validation_results['false_positives']:,}
        False Negatives: {validation_results['missed_in_regions']:,}
        """
        
        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        plt.show()

# Main validation function
def validate_piles_against_kml(
    kml_file_path: str,
    pile_coords: np.ndarray,
    patch_info: List[Dict],
    target_crs: str = "EPSG:32632",  # Adjust based on your coordinate system
    buffer_distance: float = 5.0,
    visualize: bool = True
):
    """
    Main function to validate pile detections against KML regions.
    
    Args:
        kml_file_path: Path to KML file
        pile_coords: Array of pile coordinates
        patch_info: List of patch information
        target_crs: Target coordinate reference system
        buffer_distance: Buffer around regions for tolerance
        visualize: Whether to create visualizations
    """
    
    # Initialize validator
    validator = KMLPileValidator(kml_file_path)
    
    # Setup coordinate transformation if needed
    validator.setup_coordinate_transform(target_crs)
    
    # Perform validation
    validation_results = validator.validate_pile_detections(
        pile_coords, patch_info, buffer_distance
    )
    
    # Print summary
    validator.print_validation_summary(validation_results)
    
    # Create visualizations
    if visualize:
        validator.visualize_validation(pile_coords, patch_info, validation_results)
    
    return validation_results, validator

# Example usage
if __name__ == "__main__":
    # Assuming you have pile_coords and patch_info from your detection
    kml_path = "../../../../data/raw/trino_enel/kml/pile.kml"  # Adjust path as needed
    
    # Run validation
    validation_results, validator = validate_piles_against_kml(
        kml_file_path=kml_path,
        pile_coords=pile_coords,
        patch_info=high_quality_info,  # or pile_patch_info
        target_crs="EPSG:32632",  # Adjust to match your coordinate system
        buffer_distance=10.0,  # 10 meter tolerance
        visualize=True)

import numpy as np
import matplotlib.pyplot as plt
import pyproj
from shapely.geometry import Point, Polygon
from shapely.ops import transform
import xml.etree.ElementTree as ET
from typing import List, Tuple, Optional
import re

class CoordinateDiagnostic:
    """Tool to diagnose and fix coordinate system mismatches"""
    
    def __init__(self, kml_file_path: str):
        self.kml_file_path = kml_file_path
        self.kml_coords = None
        self.kml_bounds = None
        self._extract_kml_coordinates()
    
    def _extract_kml_coordinates(self):
        """Extract raw coordinates from KML file"""
        try:
            tree = ET.parse(self.kml_file_path)
            root = tree.getroot()
            
            # Handle namespace
            namespace = {'kml': 'http://www.opengis.net/kml/2.2'}
            if root.tag.startswith('{'):
                ns_match = re.match(r'\{([^}]+)\}', root.tag)
                if ns_match:
                    namespace = {'kml': ns_match.group(1)}
            
            # Find all coordinate elements
            coords_elements = root.findall('.//kml:coordinates', namespace)
            if not coords_elements:
                coords_elements = root.findall('.//coordinates')
            
            all_coords = []
            for coords_elem in coords_elements:
                coords_text = coords_elem.text.strip()
                coord_pairs = coords_text.split()
                
                for pair in coord_pairs:
                    if pair.strip():
                        try:
                            parts = pair.split(',')
                            if len(parts) >= 2:
                                lon = float(parts[0])
                                lat = float(parts[1])
                                all_coords.append([lon, lat])
                        except ValueError:
                            continue
            
            self.kml_coords = np.array(all_coords)
            
            if len(self.kml_coords) > 0:
                self.kml_bounds = {
                    'lon_min': self.kml_coords[:, 0].min(),
                    'lon_max': self.kml_coords[:, 0].max(),
                    'lat_min': self.kml_coords[:, 1].min(),
                    'lat_max': self.kml_coords[:, 1].max()
                }
                
            print(f"Extracted {len(self.kml_coords)} coordinate points from KML")
            
        except Exception as e:
            print(f"Error extracting KML coordinates: {e}")
    
    def analyze_coordinate_systems(self, pile_coords: np.ndarray):
        """Analyze and compare coordinate systems"""
        print("="*60)
        print("COORDINATE SYSTEM ANALYSIS")
        print("="*60)
        
        # Analyze pile coordinates
        pile_bounds = {
            'x_min': pile_coords[:, 0].min(),
            'x_max': pile_coords[:, 0].max(),
            'y_min': pile_coords[:, 1].min(),
            'y_max': pile_coords[:, 1].max()
        }
        
        print(f"\nPile Coordinates Analysis:")
        print(f"  X range: {pile_bounds['x_min']:.2f} to {pile_bounds['x_max']:.2f}")
        print(f"  Y range: {pile_bounds['y_min']:.2f} to {pile_bounds['y_max']:.2f}")
        print(f"  X span: {pile_bounds['x_max'] - pile_bounds['x_min']:.2f}")
        print(f"  Y span: {pile_bounds['y_max'] - pile_bounds['y_min']:.2f}")
        
        # Determine likely coordinate system for piles
        if (pile_bounds['x_min'] > 100000 and pile_bounds['x_max'] < 1000000 and 
            pile_bounds['y_min'] > 1000000 and pile_bounds['y_max'] < 10000000):
            print(f"  Likely system: UTM (projected coordinates)")
        elif (pile_bounds['x_min'] > -180 and pile_bounds['x_max'] < 180 and
              pile_bounds['y_min'] > -90 and pile_bounds['y_max'] < 90):
            print(f"  Likely system: Geographic (WGS84)")
        else:
            print(f"  Likely system: Local/Custom coordinate system")
        
        # Analyze KML coordinates
        if self.kml_coords is not None and len(self.kml_coords) > 0:
            print(f"\nKML Coordinates Analysis:")
            print(f"  Longitude range: {self.kml_bounds['lon_min']:.6f} to {self.kml_bounds['lon_max']:.6f}")
            print(f"  Latitude range: {self.kml_bounds['lat_min']:.6f} to {self.kml_bounds['lat_max']:.6f}")
            print(f"  Longitude span: {self.kml_bounds['lon_max'] - self.kml_bounds['lon_min']:.6f}")
            print(f"  Latitude span: {self.kml_bounds['lat_max'] - self.kml_bounds['lat_min']:.6f}")
            print(f"  System: Geographic (WGS84 - standard for KML)")
            
            # Estimate UTM zone from KML coordinates
            center_lon = (self.kml_bounds['lon_min'] + self.kml_bounds['lon_max']) / 2
            utm_zone = int((center_lon + 180) / 6) + 1
            utm_epsg = 32600 + utm_zone if self.kml_bounds['lat_min'] >= 0 else 32700 + utm_zone
            
            print(f"\nRecommended UTM Zone: {utm_zone}")
            print(f"Recommended EPSG Code: {utm_epsg}")
        
        return pile_bounds, self.kml_bounds
    
    def suggest_transformations(self, pile_coords: np.ndarray) -> List[str]:
        """Suggest possible coordinate transformations"""
        suggestions = []
        
        if self.kml_coords is None:
            return ["Cannot suggest transformations - KML coordinates not available"]
        
        # Determine UTM zone from KML
        center_lon = (self.kml_bounds['lon_min'] + self.kml_bounds['lon_max']) / 2
        center_lat = (self.kml_bounds['lat_min'] + self.kml_bounds['lat_max']) / 2
        utm_zone = int((center_lon + 180) / 6) + 1
        utm_epsg = 32600 + utm_zone if center_lat >= 0 else 32700 + utm_zone
        
        # Common coordinate systems to try
        crs_options = [
            f"EPSG:{utm_epsg}",  # Calculated UTM
            "EPSG:32632",  # UTM Zone 32N (common in Europe)
            "EPSG:32633",  # UTM Zone 33N
            "EPSG:3003",   # Monte Mario / Italy zone 1
            "EPSG:3004",   # Monte Mario / Italy zone 2
            "EPSG:25832",  # ETRS89 / UTM zone 32N
            "EPSG:25833",  # ETRS89 / UTM zone 33N
        ]
        
        pile_bounds = {
            'x_min': pile_coords[:, 0].min(),
            'x_max': pile_coords[:, 0].max(),
            'y_min': pile_coords[:, 1].min(),
            'y_max': pile_coords[:, 1].max()
        }
        
        print(f"\nTesting coordinate transformations...")
        
        for crs in crs_options:
            try:
                # Transform KML coordinates to this CRS
                transformer = pyproj.Transformer.from_crs("EPSG:4326", crs, always_xy=True)
                transformed_coords = []
                
                for coord in self.kml_coords:
                    x, y = transformer.transform(coord[0], coord[1])
                    transformed_coords.append([x, y])
                
                transformed_coords = np.array(transformed_coords)
                
                # Check if transformed KML bounds overlap with pile bounds
                kml_bounds_transformed = {
                    'x_min': transformed_coords[:, 0].min(),
                    'x_max': transformed_coords[:, 0].max(),
                    'y_min': transformed_coords[:, 1].min(),
                    'y_max': transformed_coords[:, 1].max()
                }
                
                # Calculate overlap
                x_overlap = (min(pile_bounds['x_max'], kml_bounds_transformed['x_max']) - 
                           max(pile_bounds['x_min'], kml_bounds_transformed['x_min']))
                y_overlap = (min(pile_bounds['y_max'], kml_bounds_transformed['y_max']) - 
                           max(pile_bounds['y_min'], kml_bounds_transformed['y_min']))
                
                overlap_area = max(0, x_overlap) * max(0, y_overlap)
                pile_area = (pile_bounds['x_max'] - pile_bounds['x_min']) * (pile_bounds['y_max'] - pile_bounds['y_min'])
                overlap_ratio = overlap_area / pile_area if pile_area > 0 else 0
                
                print(f"  {crs}: Overlap ratio = {overlap_ratio:.3f}")
                
                if overlap_ratio > 0.1:  # 10% overlap threshold
                    suggestions.append(crs)
                    
            except Exception as e:
                print(f"  {crs}: Error - {e}")
                continue
        
        return suggestions
    
    def visualize_coordinate_comparison(self, pile_coords: np.ndarray, suggested_crs: List[str]):
        """Visualize coordinates in different systems"""
        if not suggested_crs or self.kml_coords is None:
            print("No valid coordinate systems to visualize")
            return
        
        n_plots = min(len(suggested_crs) + 1, 4)  # Max 4 subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        # Plot 1: Original coordinates (likely mismatched)
        ax = axes[0]
        ax.scatter(pile_coords[:, 0], pile_coords[:, 1], c='red', s=1, alpha=0.5, label='Piles')
        ax.scatter(self.kml_coords[:, 0], self.kml_coords[:, 1], c='blue', s=10, alpha=0.7, label='KML (WGS84)')
        ax.set_title('Original Coordinates (Likely Mismatched)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Plot transformed coordinates
        for i, crs in enumerate(suggested_crs[:3]):
            ax = axes[i + 1]
            
            try:
                # Transform KML to suggested CRS
                transformer = pyproj.Transformer.from_crs("EPSG:4326", crs, always_xy=True)
                transformed_coords = []
                
                for coord in self.kml_coords:
                    x, y = transformer.transform(coord[0], coord[1])
                    transformed_coords.append([x, y])
                
                transformed_coords = np.array(transformed_coords)
                
                # Plot both
                ax.scatter(pile_coords[:, 0], pile_coords[:, 1], c='red', s=1, alpha=0.5, label='Piles')
                ax.scatter(transformed_coords[:, 0], transformed_coords[:, 1], c='blue', s=10, alpha=0.7, label=f'KML ({crs})')
                ax.set_title(f'Coordinates in {crs}')
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                # Set equal aspect if coordinates are close
                pile_x_range = pile_coords[:, 0].max() - pile_coords[:, 0].min()
                kml_x_range = transformed_coords[:, 0].max() - transformed_coords[:, 0].min()
                
                if abs(pile_x_range - kml_x_range) / max(pile_x_range, kml_x_range) < 10:
                    ax.axis('equal')
                
            except Exception as e:
                ax.text(0.5, 0.5, f'Error transforming to {crs}', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{crs} - Error')
        
        plt.tight_layout()
        plt.show()
    
    def test_coordinate_fix(self, pile_coords: np.ndarray, target_crs: str, buffer_distance: float = 10.0):
        """Test a specific coordinate transformation"""
        try:
            # Transform KML coordinates
            transformer = pyproj.Transformer.from_crs("EPSG:4326", target_crs, always_xy=True)
            
            # Create polygons in the target CRS
            transformed_regions = []
            
            # Re-parse KML to get polygon structure
            tree = ET.parse(self.kml_file_path)
            root = tree.getroot()
            
            namespace = {'kml': 'http://www.opengis.net/kml/2.2'}
            if root.tag.startswith('{'):
                ns_match = re.match(r'\{([^}]+)\}', root.tag)
                if ns_match:
                    namespace = {'kml': ns_match.group(1)}
            
            placemarks = root.findall('.//kml:Placemark', namespace)
            if not placemarks:
                placemarks = root.findall('.//Placemark')
            
            for placemark in placemarks:
                coords_elem = placemark.find('.//kml:coordinates', namespace) or placemark.find('.//coordinates')
                
                if coords_elem is not None:
                    coords_text = coords_elem.text.strip()
                    coordinates = []
                    
                    coord_pairs = coords_text.split()
                    for pair in coord_pairs:
                        if pair.strip():
                            try:
                                parts = pair.split(',')
                                if len(parts) >= 2:
                                    lon, lat = float(parts[0]), float(parts[1])
                                    x, y = transformer.transform(lon, lat)
                                    coordinates.append([x, y])
                            except ValueError:
                                continue
                    
                    if len(coordinates) >= 3:
                        polygon = Polygon(coordinates).buffer(buffer_distance)
                        transformed_regions.append(polygon)
            
            # Test how many piles fall within regions
            piles_in_regions = 0
            for pile_coord in pile_coords:
                pile_point = Point(pile_coord[0], pile_coord[1])
                for region in transformed_regions:
                    if region.contains(pile_point):
                        piles_in_regions += 1
                        break
            
            print(f"\nTesting {target_crs}:")
            print(f"  Transformed {len(transformed_regions)} KML regions")
            print(f"  Found {piles_in_regions}/{len(pile_coords)} piles within regions")
            print(f"  Success rate: {piles_in_regions/len(pile_coords)*100:.1f}%")
            
            return piles_in_regions, transformed_regions
            
        except Exception as e:
            print(f"Error testing {target_crs}: {e}")
            return 0, []

def diagnose_and_fix_coordinates(kml_file_path: str, pile_coords: np.ndarray):
    """Main function to diagnose and fix coordinate issues"""
    
    # Initialize diagnostic tool
    diagnostic = CoordinateDiagnostic(kml_file_path)
    
    # Analyze coordinate systems
    pile_bounds, kml_bounds = diagnostic.analyze_coordinate_systems(pile_coords)
    
    # Get suggestions
    suggestions = diagnostic.suggest_transformations(pile_coords)
    
    if suggestions:
        print(f"\nSuggested coordinate systems to try:")
        for crs in suggestions:
            print(f"  - {crs}")
        
        # Visualize comparisons
        diagnostic.visualize_coordinate_comparison(pile_coords, suggestions)
        
        # Test each suggestion
        print(f"\nTesting suggested transformations:")
        best_crs = None
        best_score = 0
        
        for crs in suggestions:
            score, regions = diagnostic.test_coordinate_fix(pile_coords, crs)
            if score > best_score:
                best_score = score
                best_crs = crs
        
        if best_crs:
            print(f"\nBest coordinate system: {best_crs}")
            print(f"Success rate: {best_score/len(pile_coords)*100:.1f}%")
            return best_crs
        else:
            print(f"\nNo coordinate system provided good results.")
            print(f"Consider checking if:")
            print(f"  1. KML file contains the expected regions")
            print(f"  2. Pile coordinates are in a different local coordinate system")
            print(f"  3. There's an offset or scaling issue")
    else:
        print(f"\nNo suitable coordinate transformations found.")
        print(f"Manual inspection may be required.")
    
    return None

# Example usage
if __name__ == "__main__":
    # Run the diagnostic
    kml_path = "../../../../data/raw/trino_enel/kml/pile.kml"  # Adjust path as needed
    
    best_crs = diagnose_and_fix_coordinates(kml_path, pile_coords)
    
    if best_crs:
        print(f"\nRe-running validation with {best_crs}...")
        
        # Re-run validation with the best coordinate system
        validation_results, validator = validate_piles_against_kml(
            kml_file_path=kml_path,
            pile_coords=pile_coords,
            patch_info=high_quality_info,
            target_crs=best_crs,
            buffer_distance=10.0,
            visualize=True
        )

# Simple test to check what's actually happening
from shapely.geometry import Point

# Use the original validation but with debug info
validation_results, validator = validate_piles_against_kml(
    kml_file_path="../../../../data/raw/trino_enel/kml/pile.kml",
    pile_coords=pile_coords,
    patch_info=high_quality_info,
    target_crs="EPSG:25832",  # Or even try None to skip transformation
    buffer_distance=50.0,     # Try a much larger buffer first
    visualize=True
)

# Also try without any coordinate transformation:
validation_results_no_transform, validator_no_transform = validate_piles_against_kml(
    kml_file_path="../../../../data/raw/trino_enel/kml/pile.kml",
    pile_coords=pile_coords,
    patch_info=high_quality_info,
    target_crs=None,  # Skip transformation entirely
    buffer_distance=50.0,
    visualize=True
)

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon as MPLPolygon
import xml.etree.ElementTree as ET
from shapely.geometry import Point, Polygon
import pyproj
import re
from typing import Dict, List, Tuple, Optional

class KMLMetadataOverlay:
    """Tool to overlay KML regions with pile metadata CSV"""
    
    def __init__(self, kml_file_path: str, csv_file_path: str):
        self.kml_file_path = kml_file_path
        self.csv_file_path = csv_file_path
        self.kml_regions = []
        self.pile_metadata = None
        self.coordinate_transformer = None
        
    def load_pile_metadata(self):
        """Load and analyze pile metadata CSV"""
        try:
            # Try different encodings and separators
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    for sep in [',', ';', '\t']:
                        try:
                            df = pd.read_csv(self.csv_file_path, encoding=encoding, sep=sep)
                            if len(df.columns) > 1:  # Valid CSV found
                                self.pile_metadata = df
                                print(f"Successfully loaded CSV with {encoding} encoding and '{sep}' separator")
                                break
                        except:
                            continue
                    if self.pile_metadata is not None:
                        break
                except:
                    continue
            
            if self.pile_metadata is None:
                raise ValueError("Could not read CSV file with any encoding/separator combination")
            
            print(f"Loaded pile metadata: {len(self.pile_metadata)} rows, {len(self.pile_metadata.columns)} columns")
            print("Columns:", list(self.pile_metadata.columns))
            print("\nFirst few rows:")
            print(self.pile_metadata.head())
            
            # Try to identify coordinate columns
            self._identify_coordinate_columns()
            
        except Exception as e:
            print(f"Error loading pile metadata: {e}")
            raise
    
    def _identify_coordinate_columns(self):
        """Identify which columns contain coordinates"""
        potential_x_cols = []
        potential_y_cols = []
        
        for col in self.pile_metadata.columns:
            col_lower = col.lower()
            # Check for X/Easting coordinates
            if any(x in col_lower for x in ['x', 'east', 'lon', 'longitude']):
                potential_x_cols.append(col)
            # Check for Y/Northing coordinates  
            if any(y in col_lower for y in ['y', 'north', 'lat', 'latitude']):
                potential_y_cols.append(col)
        
        print(f"\nPotential X/Easting columns: {potential_x_cols}")
        print(f"Potential Y/Northing columns: {potential_y_cols}")
        
        # Also check numeric columns that might be coordinates
        numeric_cols = self.pile_metadata.select_dtypes(include=[np.number]).columns
        print(f"Numeric columns: {list(numeric_cols)}")
        
        # Show sample values to help identify
        print("\nSample numeric values:")
        for col in numeric_cols[:6]:  # Show first 6 numeric columns
            sample_vals = self.pile_metadata[col].dropna().head(3).values
            print(f"  {col}: {sample_vals}")
    
    def set_coordinate_columns(self, x_col: str, y_col: str, crs: str = "EPSG:25832"):
        """Set which columns contain coordinates and their CRS"""
        if x_col not in self.pile_metadata.columns:
            raise ValueError(f"Column '{x_col}' not found in CSV")
        if y_col not in self.pile_metadata.columns:
            raise ValueError(f"Column '{y_col}' not found in CSV")
        
        self.x_col = x_col
        self.y_col = y_col
        self.metadata_crs = crs
        
        # Remove rows with missing coordinates
        valid_coords = self.pile_metadata[[x_col, y_col]].dropna()
        print(f"Found {len(valid_coords)} piles with valid coordinates out of {len(self.pile_metadata)} total")
        
        # Show coordinate ranges
        x_range = (valid_coords[x_col].min(), valid_coords[x_col].max())
        y_range = (valid_coords[y_col].min(), valid_coords[y_col].max())
        print(f"X coordinate range: {x_range[0]:.2f} to {x_range[1]:.2f}")
        print(f"Y coordinate range: {y_range[0]:.2f} to {y_range[1]:.2f}")
    
    def load_kml_regions(self, target_crs: str = "EPSG:25832"):
        """Load KML regions and transform to target CRS"""
        try:
            tree = ET.parse(self.kml_file_path)
            root = tree.getroot()
            
            # Handle namespace
            namespace = {'kml': 'http://www.opengis.net/kml/2.2'}
            if root.tag.startswith('{'):
                ns_match = re.match(r'\{([^}]+)\}', root.tag)
                if ns_match:
                    namespace = {'kml': ns_match.group(1)}
            
            # Find all placemarks
            placemarks = root.findall('.//kml:Placemark', namespace)
            if not placemarks:
                placemarks = root.findall('.//Placemark')
            
            print(f"Found {len(placemarks)} placemarks in KML")
            
            # Setup coordinate transformation
            if target_crs:
                transformer = pyproj.Transformer.from_crs("EPSG:4326", target_crs, always_xy=True)
                self.coordinate_transformer = transformer
            
            # Parse each placemark
            for i, placemark in enumerate(placemarks):
                try:
                    # Get name and description
                    name_elem = placemark.find('kml:name', namespace) or placemark.find('name')
                    description_elem = placemark.find('kml:description', namespace) or placemark.find('description')
                    
                    name = name_elem.text if name_elem is not None else f"Region_{i}"
                    description = description_elem.text if description_elem is not None else "No description"
                    
                    # Find coordinates
                    coords_elem = placemark.find('.//kml:coordinates', namespace) or placemark.find('.//coordinates')
                    
                    if coords_elem is not None:
                        coords_text = coords_elem.text.strip()
                        coordinates = self._parse_kml_coordinates(coords_text)
                        
                        if len(coordinates) >= 3:
                            # Transform coordinates if needed
                            if self.coordinate_transformer:
                                transformed_coords = []
                                for coord in coordinates:
                                    x, y = self.coordinate_transformer.transform(coord[0], coord[1])
                                    transformed_coords.append([x, y])
                                coordinates = np.array(transformed_coords)
                            
                            polygon = Polygon(coordinates[:, :2])
                            
                            region_info = {
                                'name': name,
                                'description': description,
                                'coordinates': coordinates,
                                'polygon': polygon,
                                'bounds': {
                                    'x_min': coordinates[:, 0].min(),
                                    'x_max': coordinates[:, 0].max(),
                                    'y_min': coordinates[:, 1].min(),
                                    'y_max': coordinates[:, 1].max()
                                }
                            }
                            self.kml_regions.append(region_info)
                            
                except Exception as e:
                    print(f"Error parsing placemark {i}: {e}")
                    continue
            
            print(f"Successfully loaded {len(self.kml_regions)} KML regions")
            
        except Exception as e:
            print(f"Error loading KML: {e}")
            raise
    
    def _parse_kml_coordinates(self, coords_text: str) -> np.ndarray:
        """Parse coordinate string from KML"""
        coordinates = []
        coord_pairs = coords_text.split()
        
        for pair in coord_pairs:
            if pair.strip():
                try:
                    parts = pair.split(',')
                    if len(parts) >= 2:
                        lon = float(parts[0])
                        lat = float(parts[1])
                        alt = float(parts[2]) if len(parts) > 2 else 0.0
                        coordinates.append([lon, lat, alt])
                except ValueError:
                    continue
        
        return np.array(coordinates)
    
    def analyze_pile_region_overlap(self) -> Dict:
        """Analyze which metadata piles fall within KML regions"""
        if not hasattr(self, 'x_col') or not hasattr(self, 'y_col'):
            raise ValueError("Coordinate columns not set. Call set_coordinate_columns() first.")
        
        # Get piles with valid coordinates
        valid_piles = self.pile_metadata[[self.x_col, self.y_col]].dropna()
        
        analysis_results = {
            'total_metadata_piles': len(self.pile_metadata),
            'valid_coordinate_piles': len(valid_piles),
            'total_kml_regions': len(self.kml_regions),
            'piles_in_regions': 0,
            'piles_outside_regions': 0,
            'piles_per_region': [],
            'region_statistics': []
        }
        
        # Check each pile against all regions
        pile_region_mapping = []
        
        for idx, row in valid_piles.iterrows():
            pile_point = Point(row[self.x_col], row[self.y_col])
            pile_info = {
                'index': idx,
                'coordinates': (row[self.x_col], row[self.y_col]),
                'in_region': False,
                'containing_regions': []
            }
            
            # Check against all regions
            for region_idx, region in enumerate(self.kml_regions):
                if region['polygon'].contains(pile_point):
                    pile_info['in_region'] = True
                    pile_info['containing_regions'].append(region_idx)
            
            pile_region_mapping.append(pile_info)
            
            if pile_info['in_region']:
                analysis_results['piles_in_regions'] += 1
            else:
                analysis_results['piles_outside_regions'] += 1
        
        # Calculate per-region statistics
        for region_idx, region in enumerate(self.kml_regions):
            piles_in_this_region = [p for p in pile_region_mapping if region_idx in p['containing_regions']]
            
            region_stats = {
                'region_index': region_idx,
                'region_name': region['name'],
                'region_description': region['description'],
                'pile_count': len(piles_in_this_region),
                'region_area': region['polygon'].area,
                'pile_density': len(piles_in_this_region) / region['polygon'].area if region['polygon'].area > 0 else 0
            }
            analysis_results['region_statistics'].append(region_stats)
        
        analysis_results['pile_region_mapping'] = pile_region_mapping
        
        return analysis_results
    
    def print_analysis_summary(self, analysis_results: Dict):
        """Print summary of the analysis"""
        print("="*60)
        print("PILE METADATA vs KML REGIONS ANALYSIS")
        print("="*60)
        
        print(f"\nDataset Overview:")
        print(f"  Total piles in metadata: {analysis_results['total_metadata_piles']:,}")
        print(f"  Piles with valid coordinates: {analysis_results['valid_coordinate_piles']:,}")
        print(f"  Total KML regions: {analysis_results['total_kml_regions']:,}")
        
        print(f"\nSpatial Analysis:")
        print(f"  Piles within KML regions: {analysis_results['piles_in_regions']:,}")
        print(f"  Piles outside KML regions: {analysis_results['piles_outside_regions']:,}")
        
        coverage = analysis_results['piles_in_regions'] / analysis_results['valid_coordinate_piles'] * 100
        print(f"  Coverage: {coverage:.1f}% of metadata piles are within KML regions")
        
        print(f"\nPer-Region Statistics:")
        for stats in analysis_results['region_statistics']:
            if stats['pile_count'] > 0:
                print(f"  {stats['region_name']}: {stats['pile_count']} piles "
                      f"(density: {stats['pile_density']:.6f} piles/unit²)")
    
    def visualize_overlay(self, analysis_results: Dict, figsize: Tuple[int, int] = (15, 10)):
        """Visualize the overlay of metadata piles and KML regions"""
        fig, axes = plt.subplots(1, 2, figsize=figsize)
        
        # Get valid coordinates
        valid_piles = self.pile_metadata[[self.x_col, self.y_col]].dropna()
        
        # Main overlay plot
        ax1 = axes[0]
        
        # Plot KML regions
        for i, region in enumerate(self.kml_regions):
            coords = np.array(region['polygon'].exterior.coords)
            polygon = MPLPolygon(coords, fill=False, edgecolor='blue', linewidth=2, alpha=0.7)
            ax1.add_patch(polygon)
            
            # Add region labels
            centroid = region['polygon'].centroid
            ax1.text(centroid.x, centroid.y, f'R{i}\n({region["name"][:10]})', 
                    fontsize=8, ha='center', va='center',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))
        
        # Plot piles by status
        for pile_info in analysis_results['pile_region_mapping']:
            coord = pile_info['coordinates']
            if pile_info['in_region']:
                ax1.scatter(coord[0], coord[1], c='green', s=20, alpha=0.6, label='In Region' if pile_info == analysis_results['pile_region_mapping'][0] else "")
            else:
                ax1.scatter(coord[0], coord[1], c='red', s=20, alpha=0.6, label='Outside Region' if pile_info == analysis_results['pile_region_mapping'][0] else "")
        
        ax1.set_title('Metadata Piles vs KML Regions')
        ax1.set_xlabel('X Coordinate')
        ax1.set_ylabel('Y Coordinate')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        
        # Statistics plot
        ax2 = axes[1]
        
        region_names = [f"R{i}" for i, stats in enumerate(analysis_results['region_statistics'])]
        pile_counts = [stats['pile_count'] for stats in analysis_results['region_statistics']]
        
        bars = ax2.bar(range(len(region_names)), pile_counts, color='skyblue', edgecolor='black')
        ax2.set_xlabel('KML Regions')
        ax2.set_ylabel('Number of Metadata Piles')
        ax2.set_title('Pile Count by Region')
        ax2.set_xticks(range(len(region_names)))
        ax2.set_xticklabels(region_names, rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # Add count labels on bars
        for bar, count in zip(bars, pile_counts):
            if count > 0:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + max(pile_counts)*0.01,
                        f'{count}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()

# Main function to run the overlay analysis
def analyze_pile_metadata_kml_overlay(
    kml_file_path: str,
    csv_file_path: str,
    x_column: str = None,
    y_column: str = None,
    coordinate_crs: str = "EPSG:25832"
):
    """
    Main function to analyze pile metadata against KML regions.
    
    Args:
        kml_file_path: Path to KML file
        csv_file_path: Path to pile metadata CSV
        x_column: Name of X/Easting coordinate column (if known)
        y_column: Name of Y/Northing coordinate column (if known)
        coordinate_crs: Coordinate reference system of the pile coordinates
    """
    
    # Initialize overlay tool
    overlay = KMLMetadataOverlay(kml_file_path, csv_file_path)
    
    # Load pile metadata
    overlay.load_pile_metadata()
    
    # Set coordinate columns (prompt user if not provided)
    if x_column is None or y_column is None:
        print("\nPlease specify the coordinate columns from the CSV.")
        print("Available columns:", list(overlay.pile_metadata.columns))
        return overlay  # Return for manual column selection
    
    overlay.set_coordinate_columns(x_column, y_column, coordinate_crs)
    
    # Load KML regions
    overlay.load_kml_regions(coordinate_crs)
    
    # Perform analysis
    analysis_results = overlay.analyze_pile_region_overlap()
    
    # Print summary
    overlay.print_analysis_summary(analysis_results)
    
    # Create visualization
    overlay.visualize_overlay(analysis_results)
    
    return overlay, analysis_results

# Example usage
if __name__ == "__main__":
    # Adjust paths as needed
    kml_path = "../../../../data/raw/trino_enel/kml/pile.kml"
    csv_path = "./../../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv"
    
    # Run analysis - you'll need to specify the coordinate columns
    overlay, results = analyze_pile_metadata_kml_overlay(
        kml_file_path=kml_path,
        csv_file_path=csv_path,
        x_column="X",  # Projected X coordinate
        y_column="Y",  # Projected Y coordinate  
        coordinate_crs="EPSG:25832"  # Assuming this matches your pile coordinates
    )


# Simple direct CSV reading approach
import pandas as pd
import os

csv_path = "./../../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv"

# Check if file exists
print(f"File exists: {os.path.exists(csv_path)}")

# Try the simplest pandas approach first
try:
    df = pd.read_csv(csv_path)
    print(f"✅ Success! Loaded {len(df)} rows, {len(df.columns)} columns")
    print("Columns:", list(df.columns))
    print("\nFirst 3 rows:")
    print(df.head(3))
except Exception as e:
    print(f"Standard CSV read failed: {e}")
    
    # Try with semicolon separator (common in European CSV files)
    try:
        df = pd.read_csv(csv_path, sep=';')
        print(f"✅ Success with semicolon! Loaded {len(df)} rows, {len(df.columns)} columns")
        print("Columns:", list(df.columns))
        print("\nFirst 3 rows:")
        print(df.head(3))
    except Exception as e2:
        print(f"Semicolon CSV read failed: {e2}")
        
        # Try with different encoding
        try:
            df = pd.read_csv(csv_path, encoding='latin-1')
            print(f"✅ Success with latin-1 encoding! Loaded {len(df)} rows, {len(df.columns)} columns")
            print("Columns:", list(df.columns))
            print("\nFirst 3 rows:")
            print(df.head(3))
        except Exception as e3:
            print(f"All attempts failed. Last error: {e3}")

# Now let's do the proper overlay analysis with the loaded data
import matplotlib.pyplot as plt
import numpy as np
from shapely.geometry import Point, Polygon
import xml.etree.ElementTree as ET
import pyproj

# We have the CSV loaded as 'df' with 14,460 piles
print(f"Pile metadata summary:")
print(f"- Total piles: {len(df)}")
print(f"- X coordinate range: {df['X'].min():.1f} to {df['X'].max():.1f}")
print(f"- Y coordinate range: {df['Y'].min():.1f} to {df['Y'].max():.1f}")

# Load KML regions properly
kml_path = "../../../../data/raw/trino_enel/kml/pile.kml"

def parse_kml_regions(kml_path):
    tree = ET.parse(kml_path)
    root = tree.getroot()
    
    # Find all placemarks with polygons
    regions = []
    
    # Handle KML namespace
    namespace = {'kml': 'http://www.opengis.net/kml/2.2'}
    if root.tag.startswith('{'):
        import re
        ns_match = re.match(r'\{([^}]+)\}', root.tag)
        if ns_match:
            namespace = {'kml': ns_match.group(1)}
    
    placemarks = root.findall('.//kml:Placemark', namespace)
    if not placemarks:
        placemarks = root.findall('.//Placemark')
    
    print(f"Found {len(placemarks)} placemarks in KML")
    
    for i, placemark in enumerate(placemarks):
        coords_elem = placemark.find('.//kml:coordinates', namespace) or placemark.find('.//coordinates')
        
        if coords_elem is not None:
            coords_text = coords_elem.text.strip()
            coordinates = []
            
            for pair in coords_text.split():
                if pair.strip() and ',' in pair:
                    try:
                        parts = pair.split(',')
                        lon, lat = float(parts[0]), float(parts[1])
                        coordinates.append([lon, lat])
                    except:
                        continue
            
            if len(coordinates) >= 3:
                regions.append({
                    'id': i,
                    'coordinates': np.array(coordinates),
                    'polygon_wgs84': Polygon(coordinates)
                })
    
    return regions

# Parse KML regions
kml_regions = parse_kml_regions(kml_path)
print(f"Successfully parsed {len(kml_regions)} polygon regions from KML")

# Convert KML regions to same coordinate system as piles (EPSG:25832)
transformer = pyproj.Transformer.from_crs("EPSG:4326", "EPSG:25832", always_xy=True)

kml_regions_projected = []
for region in kml_regions:
    # Transform coordinates
    transformed_coords = []
    for coord in region['coordinates']:
        x, y = transformer.transform(coord[0], coord[1])
        transformed_coords.append([x, y])
    
    kml_regions_projected.append({
        'id': region['id'],
        'coordinates': np.array(transformed_coords),
        'polygon': Polygon(transformed_coords)
    })

print(f"Transformed {len(kml_regions_projected)} regions to EPSG:25832")

# Now check which piles fall within which regions
pile_region_analysis = {
    'total_piles': len(df),
    'piles_in_regions': 0,
    'piles_outside_regions': 0,
    'region_pile_counts': []
}

pile_assignments = []

for idx, row in df.iterrows():
    pile_point = Point(row['X'], row['Y'])
    in_region = False
    containing_regions = []
    
    for region in kml_regions_projected:
        if region['polygon'].contains(pile_point):
            in_region = True
            containing_regions.append(region['id'])
    
    pile_assignments.append({
        'pile_id': idx,
        'coordinates': (row['X'], row['Y']),
        'in_region': in_region,
        'containing_regions': containing_regions
    })
    
    if in_region:
        pile_region_analysis['piles_in_regions'] += 1
    else:
        pile_region_analysis['piles_outside_regions'] += 1

# Calculate per-region statistics
for region in kml_regions_projected:
    piles_in_this_region = [p for p in pile_assignments if region['id'] in p['containing_regions']]
    pile_region_analysis['region_pile_counts'].append({
        'region_id': region['id'],
        'pile_count': len(piles_in_this_region)
    })

# Print summary
print("\n" + "="*60)
print("PILE METADATA vs KML REGIONS ANALYSIS")
print("="*60)
print(f"Total piles in metadata: {pile_region_analysis['total_piles']:,}")
print(f"Piles within KML regions: {pile_region_analysis['piles_in_regions']:,}")
print(f"Piles outside KML regions: {pile_region_analysis['piles_outside_regions']:,}")

coverage_pct = pile_region_analysis['piles_in_regions'] / pile_region_analysis['total_piles'] * 100
print(f"Coverage: {coverage_pct:.1f}% of metadata piles are within KML regions")

print(f"\nPer-region pile counts:")
for region_stats in pile_region_analysis['region_pile_counts']:
    if region_stats['pile_count'] > 0:
        print(f"  Region {region_stats['region_id']}: {region_stats['pile_count']:,} piles")

# Create visualization
fig, axes = plt.subplots(1, 2, figsize=(20, 8))

# Plot 1: Overview with all data
ax1 = axes[0]

# Plot all piles
in_region_piles = [p for p in pile_assignments if p['in_region']]
outside_region_piles = [p for p in pile_assignments if not p['in_region']]

if in_region_piles:
    in_coords = np.array([p['coordinates'] for p in in_region_piles])
    ax1.scatter(in_coords[:, 0], in_coords[:, 1], c='green', s=2, alpha=0.6, label=f'In Regions ({len(in_region_piles):,})')

if outside_region_piles:
    out_coords = np.array([p['coordinates'] for p in outside_region_piles])
    ax1.scatter(out_coords[:, 0], out_coords[:, 1], c='red', s=2, alpha=0.6, label=f'Outside Regions ({len(outside_region_piles):,})')

# Plot KML region boundaries
for region in kml_regions_projected:
    coords = np.array(region['polygon'].exterior.coords)
    ax1.plot(coords[:, 0], coords[:, 1], 'blue', linewidth=2, alpha=0.8)

ax1.set_title('Pile Metadata vs KML Regions (Overview)')
ax1.set_xlabel('X (EPSG:25832)')
ax1.set_ylabel('Y (EPSG:25832)')
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.axis('equal')

# Plot 2: Zoomed view of a region with piles
ax2 = axes[1]

# Find a region with piles for detailed view
region_with_piles = None
for region_stats in pile_region_analysis['region_pile_counts']:
    if region_stats['pile_count'] > 0:
        region_with_piles = region_stats['region_id']
        break

if region_with_piles is not None:
    region = kml_regions_projected[region_with_piles]
    
    # Get bounds of this region
    bounds = region['polygon'].bounds
    buffer = 100  # 100 meter buffer
    
    # Plot piles in this area
    area_piles = []
    for p in pile_assignments:
        x, y = p['coordinates']
        if bounds[0]-buffer <= x <= bounds[2]+buffer and bounds[1]-buffer <= y <= bounds[3]+buffer:
            area_piles.append(p)
    
    for p in area_piles:
        color = 'green' if p['in_region'] else 'red'
        ax2.scatter(p['coordinates'][0], p['coordinates'][1], c=color, s=10, alpha=0.8)
    
    # Plot region boundary
    coords = np.array(region['polygon'].exterior.coords)
    ax2.plot(coords[:, 0], coords[:, 1], 'blue', linewidth=3, label=f'Region {region_with_piles}')
    
    ax2.set_xlim(bounds[0]-buffer, bounds[2]+buffer)
    ax2.set_ylim(bounds[1]-buffer, bounds[3]+buffer)
    ax2.set_title(f'Detailed View: Region {region_with_piles}')
    ax2.set_xlabel('X (EPSG:25832)')
    ax2.set_ylabel('Y (EPSG:25832)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')

plt.tight_layout()
plt.show()

# Store results for later validation comparison
metadata_pile_analysis = {
    'pile_assignments': pile_assignments,
    'region_analysis': pile_region_analysis,
    'kml_regions_projected': kml_regions_projected
}

import xml.etree.ElementTree as ET
import numpy as np
from shapely.geometry import Polygon
import re

def debug_kml_structure(kml_path):
    """Debug the KML file structure to understand the format"""
    tree = ET.parse(kml_path)
    root = tree.getroot()
    
    print("="*60)
    print("KML STRUCTURE ANALYSIS")
    print("="*60)
    
    print(f"Root tag: {root.tag}")
    print(f"Root attributes: {root.attrib}")
    
    # Find all unique tags
    all_tags = set()
    for elem in root.iter():
        all_tags.add(elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag)
    
    print(f"All tags found: {sorted(all_tags)}")
    
    # Look for placemarks
    placemarks = []
    for elem in root.iter():
        if 'placemark' in elem.tag.lower():
            placemarks.append(elem)
    
    print(f"\nFound {len(placemarks)} Placemark elements")
    
    # Analyze first few placemarks
    for i, placemark in enumerate(placemarks[:5]):
        print(f"\nPlacemark {i+1}:")
        print(f"  Tag: {placemark.tag}")
        print(f"  Attributes: {placemark.attrib}")
        
        # Look for name
        for child in placemark:
            if 'name' in child.tag.lower():
                print(f"  Name: {child.text}")
            elif 'description' in child.tag.lower():
                print(f"  Description: {child.text}")
        
        # Look for geometry
        geometries = []
        for elem in placemark.iter():
            if any(geo in elem.tag.lower() for geo in ['polygon', 'linestring', 'point', 'coordinates']):
                geometries.append(elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag)
        
        print(f"  Geometry elements: {geometries}")
        
        # Look for coordinates
        coords_elements = []
        for elem in placemark.iter():
            if 'coordinates' in elem.tag.lower():
                coords_elements.append(elem)
        
        print(f"  Coordinate elements: {len(coords_elements)}")
        if coords_elements:
            coords_text = coords_elements[0].text
            if coords_text:
                coords_preview = coords_text.strip()[:100] + "..." if len(coords_text.strip()) > 100 else coords_text.strip()
                print(f"  Sample coordinates: {coords_preview}")
    
    return placemarks

def robust_parse_kml_regions(kml_path):
    """More robust KML parsing that handles various formats"""
    
    # First debug the structure
    placemarks = debug_kml_structure(kml_path)
    
    print(f"\n" + "="*60)
    print("EXTRACTING POLYGON REGIONS")
    print("="*60)
    
    regions = []
    
    for i, placemark in enumerate(placemarks):
        try:
            # Get name
            name = f"Region_{i}"
            for child in placemark.iter():
                if 'name' in child.tag.lower() and child.text:
                    name = child.text
                    break
            
            # Look for coordinates in any coordinate element
            coordinates_found = []
            
            for coords_elem in placemark.iter():
                if 'coordinates' in coords_elem.tag.lower() and coords_elem.text:
                    coords_text = coords_elem.text.strip()
                    
                    # Parse coordinates
                    coords = parse_coordinate_string(coords_text)
                    if len(coords) >= 3:  # Need at least 3 points for polygon
                        coordinates_found.append(coords)
            
            # Create regions from found coordinates
            for j, coords in enumerate(coordinates_found):
                try:
                    polygon = Polygon(coords[:, :2])  # Use only X,Y
                    if polygon.is_valid and polygon.area > 0:
                        region_name = f"{name}_{j}" if len(coordinates_found) > 1 else name
                        regions.append({
                            'id': len(regions),
                            'name': region_name,
                            'coordinates': coords,
                            'polygon_wgs84': polygon
                        })
                        print(f"✅ Extracted region '{region_name}': {len(coords)} points, area: {polygon.area:.6f}")
                except Exception as e:
                    print(f"❌ Error creating polygon for {name}_{j}: {e}")
        
        except Exception as e:
            print(f"❌ Error processing placemark {i}: {e}")
            continue
    
    print(f"\nTotal regions extracted: {len(regions)}")
    return regions

def parse_coordinate_string(coords_text):
    """Parse coordinate string handling various formats"""
    coordinates = []
    
    # Remove extra whitespace and normalize
    coords_text = re.sub(r'\s+', ' ', coords_text.strip())
    
    # Try different parsing approaches
    
    # Approach 1: Space-separated coordinate pairs
    parts = coords_text.split()
    for part in parts:
        if ',' in part:
            try:
                components = part.split(',')
                if len(components) >= 2:
                    lon = float(components[0])
                    lat = float(components[1])
                    alt = float(components[2]) if len(components) > 2 else 0.0
                    coordinates.append([lon, lat, alt])
            except ValueError:
                continue
    
    # Approach 2: If no coordinates found, try comma-separated with spaces
    if not coordinates:
        # Replace multiple spaces with single space, then split by space
        normalized = re.sub(r'\s+', ' ', coords_text)
        for part in normalized.split(' '):
            if ',' in part:
                try:
                    components = part.split(',')
                    if len(components) >= 2:
                        lon = float(components[0])
                        lat = float(components[1])
                        coordinates.append([lon, lat, 0.0])
                except ValueError:
                    continue
    
    # Approach 3: Try splitting by newlines first
    if not coordinates:
        for line in coords_text.split('\n'):
            line = line.strip()
            if line and ',' in line:
                try:
                    components = line.split(',')
                    if len(components) >= 2:
                        lon = float(components[0])
                        lat = float(components[1])
                        coordinates.append([lon, lat, 0.0])
                except ValueError:
                    continue
    
    return np.array(coordinates) if coordinates else np.array([])

# Test the robust parser
def test_kml_parsing(kml_path):
    """Test the KML parsing and show results"""
    
    print("Testing robust KML parsing...")
    regions = robust_parse_kml_regions(kml_path)
    
    if regions:
        print(f"\n✅ Successfully extracted {len(regions)} regions!")
        
        # Show region summary
        for region in regions[:10]:  # Show first 10 regions
            bounds = region['polygon_wgs84'].bounds
            print(f"Region '{region['name']}': {len(region['coordinates'])} points, "
                  f"bounds: ({bounds[0]:.6f}, {bounds[1]:.6f}) to ({bounds[2]:.6f}, {bounds[3]:.6f})")
        
        return regions
    else:
        print("❌ No regions could be extracted")
        return []

# If this works, we can then proceed with the overlay analysis
if __name__ == "__main__":
    kml_path = "../../../../data/raw/trino_enel/kml/pile.kml"
    
    # Test the robust parsing
    regions = test_kml_parsing(kml_path)
    
    if regions:
        print(f"\n🎉 KML parsing successful! Found {len(regions)} regions.")
        print("Now we can proceed with the pile metadata overlay...")
    else:
        print("\n❌ KML parsing failed. Need to investigate the file structure further.")

# Fix the coordinate transformation
import pyproj
import matplotlib.pyplot as plt
from shapely.geometry import Point
import numpy as np

print("Performing pile metadata vs KML regions overlay analysis...")

# Convert the extracted KML regions to the same coordinate system as pile metadata (EPSG:25832)
transformer = pyproj.Transformer.from_crs("EPSG:4326", "EPSG:25832", always_xy=True)

kml_regions_projected = []
for region in regions:
    # Transform coordinates from WGS84 to EPSG:25832
    transformed_coords = []
    for coord in region['coordinates']:
        x, y = transformer.transform(coord[0], coord[1])
        transformed_coords.append([x, y])
    
    # Convert to numpy array before slicing
    transformed_coords_array = np.array(transformed_coords)
    
    kml_regions_projected.append({
        'id': region['id'],
        'name': region['name'],
        'coordinates': transformed_coords_array,
        'polygon': Polygon(transformed_coords_array[:, :2])  # Now this will work
    })

print(f"Transformed {len(kml_regions_projected)} regions to EPSG:25832")

# Analyze pile metadata against KML regions with buffer for tolerance
buffer_distance = 5.0  # 5 meter buffer around each KML region

pile_region_analysis = {
    'total_piles': len(df),
    'piles_in_regions': 0,
    'piles_outside_regions': 0,
    'matched_regions': 0,
    'unmatched_regions': 0
}

pile_assignments = []
region_matches = {region['id']: [] for region in kml_regions_projected}

print(f"Checking {len(df)} piles against {len(kml_regions_projected)} regions...")
print("This may take a moment with 14,460 piles vs 1,288 regions...")

for idx, row in df.iterrows():
    pile_point = Point(row['X'], row['Y'])
    in_region = False
    containing_regions = []
    
    # Check against all regions (with buffer)
    for region in kml_regions_projected:
        buffered_polygon = region['polygon'].buffer(buffer_distance)
        if buffered_polygon.contains(pile_point):
            in_region = True
            containing_regions.append(region['id'])
            region_matches[region['id']].append(idx)
    
    pile_assignments.append({
        'pile_id': idx,
        'tag': row['Tag'],
        'coordinates': (row['X'], row['Y']),
        'in_region': in_region,
        'containing_regions': containing_regions
    })
    
    if in_region:
        pile_region_analysis['piles_in_regions'] += 1
    else:
        pile_region_analysis['piles_outside_regions'] += 1
    
    # Progress indicator
    if idx % 2000 == 0:
        print(f"  Processed {idx:,} piles... ({idx/len(df)*100:.1f}% complete)")

# Count matched vs unmatched regions
for region_id, matched_piles in region_matches.items():
    if matched_piles:
        pile_region_analysis['matched_regions'] += 1
    else:
        pile_region_analysis['unmatched_regions'] += 1

# Print detailed analysis
print("\n" + "="*80)
print("PILE METADATA vs KML REGIONS ANALYSIS RESULTS")
print("="*80)

print(f"\nPile Analysis:")
print(f"  Total piles in metadata: {pile_region_analysis['total_piles']:,}")
print(f"  Piles matched to KML regions: {pile_region_analysis['piles_in_regions']:,}")
print(f"  Piles without matching regions: {pile_region_analysis['piles_outside_regions']:,}")

coverage_pct = pile_region_analysis['piles_in_regions'] / pile_region_analysis['total_piles'] * 100
print(f"  Coverage: {coverage_pct:.1f}% of metadata piles have corresponding KML regions")

print(f"\nRegion Analysis:")
print(f"  Total KML regions: {len(kml_regions_projected):,}")
print(f"  Regions with matching piles: {pile_region_analysis['matched_regions']:,}")
print(f"  Regions without matching piles: {pile_region_analysis['unmatched_regions']:,}")

match_pct = pile_region_analysis['matched_regions'] / len(kml_regions_projected) * 100
print(f"  Match rate: {match_pct:.1f}% of KML regions have corresponding metadata piles")

# Quick visualization (simplified for performance)
fig, axes = plt.subplots(1, 2, figsize=(16, 6))

# Plot 1: Overview with sampling for performance
ax1 = axes[0]

# Sample piles for visualization (every 10th pile)
sample_piles = pile_assignments[::10]

in_region_piles = [p for p in sample_piles if p['in_region']]
outside_region_piles = [p for p in sample_piles if not p['in_region']]

if in_region_piles:
    in_coords = np.array([p['coordinates'] for p in in_region_piles])
    ax1.scatter(in_coords[:, 0], in_coords[:, 1], c='green', s=2, alpha=0.6, 
               label=f'Matched Piles (sample)')

if outside_region_piles:
    out_coords = np.array([p['coordinates'] for p in outside_region_piles])
    ax1.scatter(out_coords[:, 0], out_coords[:, 1], c='red', s=2, alpha=0.6, 
               label=f'Unmatched Piles (sample)')

ax1.set_title('Overview: Pile Metadata vs KML Regions')
ax1.set_xlabel('X (EPSG:25832)')
ax1.set_ylabel('Y (EPSG:25832)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot 2: Statistics
ax2 = axes[1]

categories = ['Matched\nPiles', 'Unmatched\nPiles', 'Matched\nRegions', 'Unmatched\nRegions']
values = [pile_region_analysis['piles_in_regions'], 
          pile_region_analysis['piles_outside_regions'],
          pile_region_analysis['matched_regions'],
          pile_region_analysis['unmatched_regions']]
colors = ['green', 'red', 'blue', 'orange']

bars = ax2.bar(categories, values, color=colors, alpha=0.7)
ax2.set_title('Match Statistics')
ax2.set_ylabel('Count')

# Add value labels on bars
for bar, value in zip(bars, values):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
             f'{value:,}', ha='center', va='bottom', fontweight='bold')

ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\n📊 Analysis complete!")
print(f"   - Buffer distance used: {buffer_distance} meters")
print(f"   - This analysis provides the ground truth for validating your detection algorithm")

# Store results for comparison with detected piles
metadata_analysis_results = {
    'pile_assignments': pile_assignments,
    'region_analysis': pile_region_analysis,
    'kml_regions_projected': kml_regions_projected,
    'buffer_distance': buffer_distance
}

# Now let's validate your detection algorithm against both KML regions and metadata piles
print("="*80)
print("VALIDATING PILE DETECTION ALGORITHM")
print("="*80)

# You had 5,000 high-quality detected piles from your algorithm
detected_pile_coords = pile_coords[[info['pile_id'] for info in high_quality_info]]

print(f"Detected piles to validate: {len(detected_pile_coords):,}")
print(f"Metadata piles (ground truth): {len(df):,}")
print(f"KML regions: {len(kml_regions_projected):,}")

# Validation 1: How many detected piles are within KML regions?
detection_vs_kml = {
    'detected_in_kml_regions': 0,
    'detected_outside_kml_regions': 0
}

for detected_coord in detected_pile_coords:
    detected_point = Point(detected_coord[0], detected_coord[1])
    in_kml_region = False
    
    for region in kml_regions_projected:
        buffered_polygon = region['polygon'].buffer(10.0)  # 10m buffer
        if buffered_polygon.contains(detected_point):
            in_kml_region = True
            break
    
    if in_kml_region:
        detection_vs_kml['detected_in_kml_regions'] += 1
    else:
        detection_vs_kml['detected_outside_kml_regions'] += 1

print(f"\nValidation 1 - Detection vs KML Regions:")
print(f"  Detected piles in KML regions: {detection_vs_kml['detected_in_kml_regions']:,}")
print(f"  Detected piles outside KML regions: {detection_vs_kml['detected_outside_kml_regions']:,}")

kml_precision = detection_vs_kml['detected_in_kml_regions'] / len(detected_pile_coords) * 100
print(f"  KML Precision: {kml_precision:.1f}% (detected piles in valid KML regions)")

# Validation 2: How many detected piles are near actual metadata piles?
detection_vs_metadata = {
    'detected_near_metadata': 0,
    'detected_far_from_metadata': 0,
    'closest_distances': []
}

print(f"\nValidation 2 - Detection vs Metadata Piles (this may take a moment)...")

from scipy.spatial.distance import cdist

# Use spatial indexing for efficiency
metadata_coords = df[['X', 'Y']].values
detected_coords_array = np.array([[coord[0], coord[1]] for coord in detected_pile_coords])

# Calculate distances between detected and metadata piles
distances = cdist(detected_coords_array, metadata_coords)
min_distances = np.min(distances, axis=1)

tolerance = 20.0  # 20 meter tolerance for matching

for min_dist in min_distances:
    detection_vs_metadata['closest_distances'].append(min_dist)
    if min_dist <= tolerance:
        detection_vs_metadata['detected_near_metadata'] += 1
    else:
        detection_vs_metadata['detected_far_from_metadata'] += 1

print(f"  Detected piles near metadata piles (≤{tolerance}m): {detection_vs_metadata['detected_near_metadata']:,}")
print(f"  Detected piles far from metadata piles (>{tolerance}m): {detection_vs_metadata['detected_far_from_metadata']:,}")

metadata_precision = detection_vs_metadata['detected_near_metadata'] / len(detected_pile_coords) * 100
print(f"  Metadata Precision: {metadata_precision:.1f}% (detected piles near actual piles)")

avg_distance = np.mean(detection_vs_metadata['closest_distances'])
print(f"  Average distance to nearest metadata pile: {avg_distance:.1f} meters")

# Validation 3: Detection recall - how many metadata piles were detected?
detected_metadata_piles = 0
for _, row in df.iterrows():
    metadata_point = Point(row['X'], row['Y'])
    
    # Check if any detected pile is within tolerance
    for detected_coord in detected_pile_coords:
        detected_point = Point(detected_coord[0], detected_coord[1])
        if metadata_point.distance(detected_point) <= tolerance:
            detected_metadata_piles += 1
            break

recall = detected_metadata_piles / len(df) * 100
print(f"\nValidation 3 - Detection Recall:")
print(f"  Metadata piles that were detected: {detected_metadata_piles:,} out of {len(df):,}")
print(f"  Recall: {recall:.1f}% (actual piles that were found)")

# Summary
print(f"\n" + "="*80)
print("DETECTION ALGORITHM PERFORMANCE SUMMARY")
print("="*80)
print(f"📊 Overall Performance Metrics:")
print(f"   • Total detected piles: {len(detected_pile_coords):,}")
print(f"   • KML Region Precision: {kml_precision:.1f}% (piles in designated regions)")
print(f"   • Metadata Precision: {metadata_precision:.1f}% (piles near actual locations)")
print(f"   • Recall: {recall:.1f}% (actual piles successfully detected)")
print(f"   • Average localization error: {avg_distance:.1f} meters")

# Determine overall assessment
if metadata_precision > 80 and recall > 70:
    assessment = "🎉 EXCELLENT - High precision and recall"
elif metadata_precision > 60 and recall > 50:
    assessment = "✅ GOOD - Solid performance with room for improvement"
elif metadata_precision > 40 or recall > 30:
    assessment = "⚠️  MODERATE - Some detection capability but needs improvement"
else:
    assessment = "❌ POOR - Significant improvements needed"

print(f"\n🔍 Assessment: {assessment}")

# Recommendations based on results
print(f"\n💡 Recommendations:")
if kml_precision < 50:
    print(f"   • Consider adjusting detection parameters to focus on KML regions")
if metadata_precision < 70:
    print(f"   • Fine-tune detection algorithm to reduce false positives")
if recall < 60:
    print(f"   • Improve detection sensitivity to catch more actual piles")
if avg_distance > 10:
    print(f"   • Improve localization accuracy (current avg error: {avg_distance:.1f}m)")

print(f"\n✨ Next steps: Use this validation to tune your detection parameters!")

!python -m pip install chardet
import pandas as pd
import os
import chardet

def diagnose_csv_file(file_path: str):
    """Diagnose CSV file reading issues"""
    
    print(f"Diagnosing CSV file: {file_path}")
    print("="*60)
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"❌ ERROR: File does not exist at {file_path}")
        return None
    
    print(f"✅ File exists")
    
    # Check file size
    file_size = os.path.getsize(file_path)
    print(f"📁 File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    
    if file_size == 0:
        print("❌ ERROR: File is empty")
        return None
    
    # Detect encoding
    print("\n🔍 Detecting file encoding...")
    try:
        with open(file_path, 'rb') as f:
            sample = f.read(10000)  # Read first 10KB
            encoding_result = chardet.detect(sample)
            detected_encoding = encoding_result['encoding']
            confidence = encoding_result['confidence']
        print(f"Detected encoding: {detected_encoding} (confidence: {confidence:.2f})")
    except Exception as e:
        print(f"❌ Error detecting encoding: {e}")
        detected_encoding = 'utf-8'
    
    # Try to read first few lines as text
    print("\n📄 Reading first few lines as text...")
    try:
        with open(file_path, 'r', encoding=detected_encoding) as f:
            for i, line in enumerate(f):
                if i < 5:  # Show first 5 lines
                    print(f"Line {i+1}: {repr(line[:100])}")  # Show first 100 chars
                else:
                    break
    except Exception as e:
        print(f"❌ Error reading as text: {e}")
        # Try with different encodings
        for enc in ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']:
            try:
                with open(file_path, 'r', encoding=enc) as f:
                    first_line = f.readline()
                    print(f"✅ Readable with {enc}: {repr(first_line[:100])}")
                    detected_encoding = enc
                    break
            except:
                continue
    
    # Try different separators
    print(f"\n🔧 Trying to read as CSV with different separators...")
    separators = [',', ';', '\t', '|']
    
    for sep in separators:
        try:
            df = pd.read_csv(file_path, encoding=detected_encoding, sep=sep, nrows=5)
            print(f"✅ Success with separator '{sep}': {len(df.columns)} columns, {len(df)} rows")
            print(f"   Columns: {list(df.columns)}")
            print(f"   Sample data:")
            print(df.head(2).to_string())
            print()
            return df, detected_encoding, sep
        except Exception as e:
            print(f"❌ Failed with separator '{sep}': {str(e)[:100]}")
    
    # If all else fails, try reading without header
    print(f"\n🔧 Trying to read without header...")
    try:
        df = pd.read_csv(file_path, encoding=detected_encoding, header=None, nrows=10)
        print(f"✅ Success without header: {len(df.columns)} columns, {len(df)} rows")
        print(df.head())
        return df, detected_encoding, ','
    except Exception as e:
        print(f"❌ Failed without header: {e}")
    
    print("\n❌ Could not read file as CSV with any method")
    return None

def load_csv_with_diagnosis(file_path: str):
    """Load CSV with automatic diagnosis and correction"""
    
    result = diagnose_csv_file(file_path)
    
    if result is None:
        return None
    
    df, encoding, separator = result
    
    # Now load the full file
    print(f"\n📊 Loading full CSV file...")
    try:
        full_df = pd.read_csv(file_path, encoding=encoding, sep=separator)
        print(f"✅ Successfully loaded: {len(full_df)} rows, {len(full_df.columns)} columns")
        print(f"\nColumns: {list(full_df.columns)}")
        print(f"\nFirst few rows:")
        print(full_df.head())
        return full_df
    except Exception as e:
        print(f"❌ Error loading full file: {e}")
        return None

# Test the specific file
if __name__ == "__main__":
    csv_path = "./../../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv"
    
    df = load_csv_with_diagnosis(csv_path)
    
    if df is not None:
        print("\n" + "="*60)
        print("CSV ANALYSIS SUCCESSFUL")
        print("="*60)
        print(f"Total rows: {len(df)}")
        print(f"Total columns: {len(df.columns)}")
        
        # Look for coordinate columns
        print(f"\nLooking for coordinate columns...")
        coord_cols = []
        for col in df.columns:
            col_lower = col.lower()
            if any(x in col_lower for x in ['x', 'y', 'lat', 'lon', 'east', 'north']):
                coord_cols.append(col)
        
        print(f"Potential coordinate columns: {coord_cols}")
        
        if coord_cols:
            print(f"\nSample coordinate values:")
            for col in coord_cols:
                if col in df.columns:
                    sample_vals = df[col].dropna().head(3).values
                    print(f"  {col}: {sample_vals}")

!ls -lh ./../../../../data/processed/trino_enel/ifc_metadata/


if __name__ == "__main__":
    # Run validation with the known correct coordinate system
    kml_path = "../../../../data/raw/trino_enel/kml/pile.kml"
    
    print("Running validation with EPSG:25832 (ETRS89 / UTM zone 32N)...")
    
    validation_results, validator = validate_piles_against_kml(
        kml_file_path=kml_path,
        pile_coords=pile_coords,
        patch_info=high_quality_info,
        target_crs="EPSG:25832",  # Use the correct coordinate system
        buffer_distance=10.0,
        visualize=True
    )

!ls -lh ../../../../data/raw/trino_enel/kml/

def generate_negative_samples(points, pile_coords, patch_radius=5.0, min_points=100, num_samples=None):
    """Generate negative samples away from pile locations"""
    if num_samples is None:
        num_samples = len(pile_coords)  # Same number as positive samples
    
    # Create exclusion zones around piles
    pile_tree = cKDTree(pile_coords[:, :2])
    exclusion_radius = patch_radius * 2  # Avoid overlap
    
    # Sample random locations
    point_bounds = {
        'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),
        'y_min': points[:, 1].min(), 'y_max': points[:, 1].max()
    }
    
    tree = cKDTree(points[:, :2])
    negative_patches = []
    negative_patch_info = []
    
    attempts = 0
    max_attempts = num_samples * 10
    
    while len(negative_patches) < num_samples and attempts < max_attempts:
        # Random location
        center = np.array([
            np.random.uniform(point_bounds['x_min'], point_bounds['x_max']),
            np.random.uniform(point_bounds['y_min'], point_bounds['y_max'])
        ])
        
        # Check if too close to piles
        distances_to_piles = pile_tree.query(center, k=1)[0]
        if distances_to_piles < exclusion_radius:
            attempts += 1
            continue
        
        # Extract patch
        indices = tree.query_ball_point(center, patch_radius)
        
        if len(indices) >= min_points:
            patch_points = points[indices]
            
            # Use median Z as center Z
            center_3d = np.array([center[0], center[1], np.median(patch_points[:, 2])])
            centered_points = patch_points - center_3d
            
            negative_patches.append(centered_points)
            negative_patch_info.append({
                'patch_id': len(negative_patches) - 1,
                'center': center_3d,
                'num_points': len(indices),
                'label': 0  # Negative sample
            })
        
        attempts += 1
    
    return negative_patches, negative_patch_info

print("Generating negative samples...")
negative_patches, negative_patch_info = generate_negative_samples(
    points, pile_coords, patch_size, min_points_per_patch, len(pile_patches)
)

print(f"Generated {len(negative_patches)} negative patches")
print(f"Average points per negative patch: {np.mean([info['num_points'] for info in negative_patch_info]):.0f}")

def analyze_patch_features(patch_points):
    """Extract geometric features from a point patch"""
    if len(patch_points) == 0:
        return {}
    
    # Basic statistics
    features = {
        'num_points': len(patch_points),
        'height_range': patch_points[:, 2].max() - patch_points[:, 2].min(),
        'height_std': np.std(patch_points[:, 2]),
        'height_mean': np.mean(patch_points[:, 2]),
        'xy_extent_x': patch_points[:, 0].max() - patch_points[:, 0].min(),
        'xy_extent_y': patch_points[:, 1].max() - patch_points[:, 1].min()
    }
    
    # Density features
    xy_area = features['xy_extent_x'] * features['xy_extent_y']
    features['point_density'] = features['num_points'] / max(xy_area, 1e-6)
    
    # Vertical distribution
    z_values = patch_points[:, 2]
    features['z_percentile_25'] = np.percentile(z_values, 25)
    features['z_percentile_75'] = np.percentile(z_values, 75)
    features['z_iqr'] = features['z_percentile_75'] - features['z_percentile_25']
    
    # Clustering analysis
    if len(patch_points) > 10:
        clustering = DBSCAN(eps=0.5, min_samples=5).fit(patch_points)
        features['num_clusters'] = len(set(clustering.labels_)) - (1 if -1 in clustering.labels_ else 0)
        features['noise_ratio'] = np.sum(clustering.labels_ == -1) / len(clustering.labels_)
    else:
        features['num_clusters'] = 0
        features['noise_ratio'] = 1.0
    
    return features

# Analyze all patches
print("Analyzing patch features...")

pile_features = []
for i, patch in enumerate(pile_patches):
    features = analyze_patch_features(patch)
    features['label'] = 1
    features['patch_type'] = 'pile'
    pile_features.append(features)

negative_features = []
for i, patch in enumerate(negative_patches):
    features = analyze_patch_features(patch)
    features['label'] = 0
    features['patch_type'] = 'negative'
    negative_features.append(features)

# Combine all features
all_features = pile_features + negative_features
features_df = pd.DataFrame(all_features)

print(f"Feature analysis complete:")
print(f"  Pile patches: {len(pile_features)}")
print(f"  Negative patches: {len(negative_features)}")
print(f"  Total features: {len(features_df.columns)}")

# Feature comparison analysis
print("=== FEATURE COMPARISON: PILE vs NEGATIVE ===")

feature_cols = ['height_range', 'height_std', 'point_density', 'num_clusters', 'noise_ratio']

for feature in feature_cols:
    pile_values = features_df[features_df['label'] == 1][feature]
    negative_values = features_df[features_df['label'] == 0][feature]
    
    print(f"\n{feature.upper()}:")
    print(f"  Pile patches: {pile_values.mean():.3f} ± {pile_values.std():.3f}")
    print(f"  Negative patches: {negative_values.mean():.3f} ± {negative_values.std():.3f}")
    print(f"  Separation ratio: {abs(pile_values.mean() - negative_values.mean()) / (pile_values.std() + negative_values.std()):.2f}")

def classify_pile_rules(features):
    """Simple rule-based pile classification"""
    score = 0
    
    # Rule 1: Height characteristics
    if features['height_range'] > 2.0:  # Piles should have significant height
        score += 1
    
    # Rule 2: Point density
    if features['point_density'] > 50:  # Dense point clusters
        score += 1
    
    # Rule 3: Vertical structure
    if features['height_std'] > 0.5:  # Vertical variation
        score += 1
    
    # Rule 4: Clustering
    if features['num_clusters'] >= 1 and features['noise_ratio'] < 0.5:
        score += 1
    
    # Rule 5: Aspect ratio
    if features['xy_extent_x'] < 3.0 and features['xy_extent_y'] < 3.0:  # Compact footprint
        score += 1
    
    return score >= 3  # Threshold for pile classification

# Apply rule-based classification
print("Applying rule-based classification...")

predictions = []
for _, row in features_df.iterrows():
    prediction = classify_pile_rules(row)
    predictions.append(int(prediction))

features_df['predicted'] = predictions

# Calculate performance metrics
true_labels = features_df['label'].values
predicted_labels = features_df['predicted'].values

tp = np.sum((true_labels == 1) & (predicted_labels == 1))
tn = np.sum((true_labels == 0) & (predicted_labels == 0))
fp = np.sum((true_labels == 0) & (predicted_labels == 1))
fn = np.sum((true_labels == 1) & (predicted_labels == 0))

accuracy = (tp + tn) / len(true_labels)
precision = tp / (tp + fp) if (tp + fp) > 0 else 0
recall = tp / (tp + fn) if (tp + fn) > 0 else 0
f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

print(f"\n=== RULE-BASED CLASSIFICATION RESULTS ===")
print(f"Accuracy: {accuracy:.3f}")
print(f"Precision: {precision:.3f}")
print(f"Recall: {recall:.3f}")
print(f"F1-Score: {f1_score:.3f}")
print(f"\nConfusion Matrix:")
print(f"  True Positives: {tp}")
print(f"  True Negatives: {tn}")
print(f"  False Positives: {fp}")
print(f"  False Negatives: {fn}")

if save_results:
    print("=== SAVING TRAINING DATA ===")
    
    # Create output directory
    output_dir = Path("../data/output_runs") / f"ifc_pile_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save features
    features_file = output_dir / "pile_detection_features.csv"
    features_df.to_csv(features_file, index=False)
    print(f"Saved features: {features_file}")
    
    # Save patch data for ML training
    training_data = {
        'positive_patches': [patch.tolist() for patch in pile_patches],
        'negative_patches': [patch.tolist() for patch in negative_patches],
        'positive_info': pile_patch_info,
        'negative_info': negative_patch_info,
        'parameters': {
            'site_name': site_name,
            'ground_method': ground_method,
            'patch_size': patch_size,
            'min_points_per_patch': min_points_per_patch
        },
        'performance': {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1_score)
        }
    }
    
    training_file = output_dir / "training_data.json"
    with open(training_file, 'w') as f:
        json.dump(training_data, f, indent=2)
    print(f"Saved training data: {training_file}")
    
    # Save summary
    summary = {
        'dataset_summary': {
            'total_patches': len(all_features),
            'positive_patches': len(pile_features),
            'negative_patches': len(negative_features),
            'avg_points_per_patch': float(np.mean([f['num_points'] for f in all_features]))
        },
        'rule_based_performance': {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1_score)
        },
        'next_steps': [
            "Use training_data.json for PointNet++ implementation",
            "Use training_data.json for DGCNN implementation",
            "Compare ML results with rule-based baseline"
        ]
    }
    
    summary_file = output_dir / "summary.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"Saved summary: {summary_file}")
    
    print(f"\nAll results saved to: {output_dir}")
    print(f"Ready for PointNet++ and DGCNN implementation.")

else:
    print("Skipping save - save_results=False")