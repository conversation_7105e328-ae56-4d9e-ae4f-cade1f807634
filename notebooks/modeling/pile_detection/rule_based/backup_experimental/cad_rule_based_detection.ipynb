{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔍 CAD Rule-Based Pile Detection\n", "\n", "This notebook implements rule-based pile detection for CAD-only sites (Castro, Mudjar, Giorgio) where no IFC data is available. The approach uses geometric rules, spatial patterns, and heuristics to identify pile locations in point cloud data.\n", "\n", "**Target Sites**: <PERSON>, <PERSON>, Giorgio (CAD-only, no IFC)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Overview\n", "\n", "### What This Notebook Does:\n", "1. **Load CAD Metadata**: Extract available geometric information from CAD files\n", "2. **Load Point Cloud Data**: Import aligned point cloud from photogrammetry\n", "3. **Apply Geometric Rules**: Use shape, size, and pattern recognition\n", "4. **Spatial Analysis**: Identify regular patterns typical of pile layouts\n", "5. **Validate Detections**: Apply consistency checks and filtering\n", "6. **Export Results**: Save detected pile locations for further analysis\n", "\n", "### Key Challenges for CAD-Only Sites:\n", "- **Limited Metadata**: CAD files may lack detailed semantic information\n", "- **Geometric Inference**: Must infer pile locations from geometric patterns\n", "- **Noise Handling**: Point clouds may have artifacts requiring robust detection\n", "- **Validation**: No ground truth IFC data for direct validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree, ConvexHull\n", "from scipy.spatial.distance import cdist\n", "from sklearn.cluster import DBSCAN, KMeans\n", "from sklearn.preprocessing import StandardScaler\n", "import cv2\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "output_path = base_path / 'output' / 'cad_detection'\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"🔍 CAD Rule-Based Pile Detection - Ready!\")\n", "print(f\"📁 Data path: {data_path}\")\n", "print(f\"💾 Output path: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1️⃣ Load CAD Metadata (Optional)\n", "\n", "Load any available CAD metadata that might provide hints about pile locations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load CAD metadata if available (from extract_cad_metadata.ipynb output)\n", "cad_metadata_path = data_path / 'cad_metadata.csv'  # Adjust path as needed\n", "cad_metadata = None\n", "\n", "try:\n", "    cad_metadata = pd.read_csv(cad_metadata_path)\n", "    print(f\"✅ Loaded CAD metadata: {len(cad_metadata)} elements\")\n", "    print(f\"📊 Columns: {list(cad_metadata.columns)}\")\n", "    \n", "    # Display sample data\n", "    print(\"\\n📋 Sample CAD metadata:\")\n", "    display(cad_metadata.head())\n", "    \n", "    # Analyze block types that might indicate piles\n", "    if 'Block Name' in cad_metadata.columns:\n", "        block_types = cad_metadata['Block Name'].value_counts()\n", "        print(f\"\\n🔍 Block Types (potential pile indicators):\")\n", "        for block_type, count in block_types.head(10).items():\n", "            print(f\"  - {block_type}: {count}\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"⚠️ CAD metadata file not found. Proceeding with point cloud analysis only.\")\n", "    print(f\"Expected path: {cad_metadata_path}\")\n", "    print(\"This is normal for pure geometric detection approaches.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2️⃣ Load Point Cloud Data\n", "\n", "Load the aligned point cloud data for analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load point cloud data\n", "point_cloud_path = data_path / 'aligned_point_cloud.ply'  # Adjust path as needed\n", "site_name = \"castro\"  # Change to \"mudjar\" or \"giorgio\" as needed\n", "\n", "try:\n", "    # Load point cloud\n", "    pcd = o3d.io.read_point_cloud(str(point_cloud_path))\n", "    points = np.asarray(pcd.points)\n", "    \n", "    print(f\"✅ Loaded point cloud: {len(points):,} points\")\n", "    print(f\"📊 Point cloud bounds:\")\n", "    print(f\"  - X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]\")\n", "    print(f\"  - Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]\")\n", "    print(f\"  - Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]\")\n", "    \n", "    # Check if colors are available\n", "    if pcd.has_colors():\n", "        colors = np.asarray(pcd.colors)\n", "        print(f\"🎨 Point cloud has RGB colors\")\n", "    else:\n", "        colors = None\n", "        print(f\"⚪ Point cloud has no color information\")\n", "        \n", "    # Check if normals are available\n", "    if pcd.has_normals():\n", "        normals = np.asarray(pcd.normals)\n", "        print(f\"📐 Point cloud has normal vectors\")\n", "    else:\n", "        # Estimate normals\n", "        pcd.estimate_normals()\n", "        normals = np.asarray(pcd.normals)\n", "        print(f\"📐 Estimated normal vectors\")\n", "        \n", "except FileNotFoundError:\n", "    print(\"❌ Point cloud file not found.\")\n", "    print(f\"Expected path: {point_cloud_path}\")\n", "    print(\"Please ensure you have an aligned point cloud from the alignment workflow.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3️⃣ Geometric Feature Extraction\n", "\n", "Extract geometric features that can help identify pile-like structures."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_geometric_features(points, normals, neighborhood_size=0.5):\n", "    \"\"\"\n", "    Extract geometric features for each point that help identify pile structures.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point coordinates (N, 3)\n", "    normals : numpy.n<PERSON><PERSON>\n", "        Normal vectors (N, 3)\n", "    neighborhood_size : float\n", "        Radius for local neighborhood analysis\n", "        \n", "    Returns:\n", "    --------\n", "    features : dict\n", "        Dictionary of geometric features\n", "    \"\"\"\n", "    \n", "    print(f\"🔍 Extracting geometric features...\")\n", "    \n", "    # Build KD-tree for efficient neighborhood queries\n", "    tree = cKDTree(points)\n", "    \n", "    features = {\n", "        'height': points[:, 2],  # Z-coordinate (height)\n", "        'planarity': np.zeros(len(points)),\n", "        'linearity': np.zeros(len(points)),\n", "        'sphericity': np.zeros(len(points)),\n", "        'verticality': np.zeros(len(points)),\n", "        'local_density': np.zeros(len(points)),\n", "        'height_variation': np.zeros(len(points))\n", "    }\n", "    \n", "    # Process points in batches for efficiency\n", "    batch_size = 10000\n", "    n_batches = (len(points) + batch_size - 1) // batch_size\n", "    \n", "    for batch_idx in range(n_batches):\n", "        start_idx = batch_idx * batch_size\n", "        end_idx = min((batch_idx + 1) * batch_size, len(points))\n", "        batch_points = points[start_idx:end_idx]\n", "        \n", "        for i, point in enumerate(batch_points):\n", "            global_idx = start_idx + i\n", "            \n", "            # Find neighbors\n", "            neighbor_indices = tree.query_ball_point(point, neighborhood_size)\n", "            \n", "            if len(neighbor_indices) < 10:  # Need minimum neighbors\n", "                continue\n", "                \n", "            neighbor_points = points[neighbor_indices]\n", "            neighbor_normals = normals[neighbor_indices]\n", "            \n", "            # Local density\n", "            features['local_density'][global_idx] = len(neighbor_indices)\n", "            \n", "            # Height variation in neighborhood\n", "            features['height_variation'][global_idx] = np.std(neighbor_points[:, 2])\n", "            \n", "            # Verticality (how vertical are the normals)\n", "            vertical_alignment = np.abs(neighbor_normals[:, 2])  # Z-component of normals\n", "            features['verticality'][global_idx] = np.mean(vertical_alignment)\n", "            \n", "            # Eigenvalue-based features (planarity, linearity, sphericity)\n", "            if len(neighbor_points) >= 3:\n", "                # Center the points\n", "                centered = neighbor_points - np.mean(neighbor_points, axis=0)\n", "                \n", "                # Compute covariance matrix\n", "                cov_matrix = np.cov(centered.T)\n", "                \n", "                # Eigenvalues\n", "                eigenvals = np.linalg.eigvals(cov_matrix)\n", "                eigenvals = np.sort(eigenvals)[::-1]  # Sort descending\n", "                \n", "                if eigenvals[0] > 1e-10:  # Avoid division by zero\n", "                    # Normalize eigenvalues\n", "                    e1, e2, e3 = eigenvals / np.sum(eigenvals)\n", "                    \n", "                    # Geometric features\n", "                    features['linearity'][global_idx] = (e1 - e2) / e1 if e1 > 0 else 0\n", "                    features['planarity'][global_idx] = (e2 - e3) / e1 if e1 > 0 else 0\n", "                    features['sphericity'][global_idx] = e3 / e1 if e1 > 0 else 0\n", "        \n", "        if (batch_idx + 1) % 5 == 0:\n", "            print(f\"  Processed {batch_idx + 1}/{n_batches} batches...\")\n", "    \n", "    print(f\"✅ Feature extraction complete\")\n", "    return features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract geometric features\n", "if 'points' in locals() and 'normals' in locals():\n", "    \n", "    # Configuration\n", "    NEIGHBORHOOD_SIZE = 1.0  # meters - adjust based on expected pile size\n", "    \n", "    print(f\"🔍 Extracting features with neighborhood size: {NEIGHBORHOOD_SIZE}m\")\n", "    \n", "    # Extract features\n", "    geometric_features = extract_geometric_features(\n", "        points=points,\n", "        normals=normals,\n", "        neighborhood_size=NEIGHBORHOOD_SIZE\n", "    )\n", "    \n", "    # Display feature statistics\n", "    print(f\"\\n📊 Feature Statistics:\")\n", "    for feature_name, feature_values in geometric_features.items():\n", "        print(f\"  {feature_name}:\")\n", "        print(f\"    Mean: {np.mean(feature_values):.3f}\")\n", "        print(f\"    Std:  {np.std(feature_values):.3f}\")\n", "        print(f\"    Range: [{np.min(feature_values):.3f}, {np.max(feature_values):.3f}]\")\n", "        \n", "else:\n", "    print(\"❌ Please ensure point cloud and normals are loaded.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4️⃣ Rule-Based Pile Detection\n", "\n", "Apply geometric rules to identify potential pile locations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_piles_rule_based(points, features, colors=None):\n", "    \"\"\"\n", "    Detect piles using rule-based geometric criteria.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point coordinates\n", "    features : dict\n", "        Geometric features\n", "    colors : numpy.ndarray, optional\n", "        RGB colors\n", "        \n", "    Returns:\n", "    --------\n", "    pile_candidates : nump<PERSON>.n<PERSON><PERSON>\n", "        Boolean mask for potential pile points\n", "    detection_info : dict\n", "        Information about detection process\n", "    \"\"\"\n", "    \n", "    print(f\"🔍 Applying rule-based pile detection...\")\n", "    \n", "    # Initialize candidate mask\n", "    pile_candidates = np.ones(len(points), dtype=bool)\n", "    \n", "    detection_info = {\n", "        'initial_points': len(points),\n", "        'rules_applied': [],\n", "        'points_remaining': []\n", "    }\n", "    \n", "    # Rule 1: Height filter (piles are typically above ground)\n", "    ground_level = np.percentile(features['height'], 10)  # Estimate ground level\n", "    min_pile_height = ground_level + 0.5  # Piles should be at least 0.5m above ground\n", "    \n", "    height_filter = features['height'] >= min_pile_height\n", "    pile_candidates &= height_filter\n", "    \n", "    detection_info['rules_applied'].append(f\"Height filter (>= {min_pile_height:.2f}m)\")\n", "    detection_info['points_remaining'].append(np.sum(pile_candidates))\n", "    print(f\"  After height filter: {np.sum(pile_candidates):,} points\")\n", "    \n", "    # Rule 2: Verticality filter (piles have vertical surfaces)\n", "    verticality_threshold = 0.3  # Adjust based on expected pile orientation\n", "    verticality_filter = features['verticality'] >= verticality_threshold\n", "    pile_candidates &= verticality_filter\n", "    \n", "    detection_info['rules_applied'].append(f\"Verticality filter (>= {verticality_threshold})\")\n", "    detection_info['points_remaining'].append(np.sum(pile_candidates))\n", "    print(f\"  After verticality filter: {np.sum(pile_candidates):,} points\")\n", "    \n", "    # Rule 3: Linearity filter (piles have linear/cylindrical structure)\n", "    linearity_threshold = 0.2\n", "    linearity_filter = features['linearity'] >= linearity_threshold\n", "    pile_candidates &= linearity_filter\n", "    \n", "    detection_info['rules_applied'].append(f\"Linearity filter (>= {linearity_threshold})\")\n", "    detection_info['points_remaining'].append(np.sum(pile_candidates))\n", "    print(f\"  After linearity filter: {np.sum(pile_candidates):,} points\")\n", "    \n", "    # Rule 4: Density filter (piles should have reasonable point density)\n", "    density_threshold = np.percentile(features['local_density'], 25)  # Bottom 25%\n", "    density_filter = features['local_density'] >= density_threshold\n", "    pile_candidates &= density_filter\n", "    \n", "    detection_info['rules_applied'].append(f\"Density filter (>= {density_threshold:.0f} points)\")\n", "    detection_info['points_remaining'].append(np.sum(pile_candidates))\n", "    print(f\"  After density filter: {np.sum(pile_candidates):,} points\")\n", "    \n", "    # Rule 5: Height variation filter (piles have consistent height in local area)\n", "    height_var_threshold = np.percentile(features['height_variation'], 75)  # Top 25%\n", "    height_var_filter = features['height_variation'] <= height_var_threshold\n", "    pile_candidates &= height_var_filter\n", "    \n", "    detection_info['rules_applied'].append(f\"Height variation filter (<= {height_var_threshold:.2f}m)\")\n", "    detection_info['points_remaining'].append(np.sum(pile_candidates))\n", "    print(f\"  After height variation filter: {np.sum(pile_candidates):,} points\")\n", "    \n", "    # Optional Rule 6: Color-based filtering (if colors available)\n", "    if colors is not None:\n", "        # Assume piles have different color characteristics than vegetation/ground\n", "        # This is site-specific and may need adjustment\n", "        \n", "        # Convert to HSV for better color analysis\n", "        hsv_colors = np.array([cv2.cvtColor(colors.reshape(1, 1, 3).astype(np.float32), cv2.COLOR_RGB2HSV).flatten() \n", "                              for colors in colors])\n", "        \n", "        # Filter out very green colors (vegetation)\n", "        green_hue_range = (40, 80)  # Green hue range in HSV\n", "        saturation_threshold = 0.3\n", "        \n", "        is_green = ((hsv_colors[:, 0] >= green_hue_range[0]) & \n", "                   (hsv_colors[:, 0] <= green_hue_range[1]) & \n", "                   (hsv_colors[:, 1] >= saturation_threshold))\n", "        \n", "        color_filter = ~is_green  # Not green\n", "        pile_candidates &= color_filter\n", "        \n", "        detection_info['rules_applied'].append(\"Color filter (non-vegetation)\")\n", "        detection_info['points_remaining'].append(np.sum(pile_candidates))\n", "        print(f\"  After color filter: {np.sum(pile_candidates):,} points\")\n", "    \n", "    detection_info['final_candidates'] = np.sum(pile_candidates)\n", "    detection_info['detection_rate'] = np.sum(pile_candidates) / len(points)\n", "    \n", "    print(f\"\\n✅ Rule-based detection complete:\")\n", "    print(f\"  Final candidates: {np.sum(pile_candidates):,} points\")\n", "    print(f\"  Detection rate: {detection_info['detection_rate']*100:.2f}%\")\n", "    \n", "    return pile_candidates, detection_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply rule-based detection\n", "if 'geometric_features' in locals():\n", "    \n", "    pile_candidates, detection_info = detect_piles_rule_based(\n", "        points=points,\n", "        features=geometric_features,\n", "        colors=colors\n", "    )\n", "    \n", "    print(f\"\\n📊 Detection Summary:\")\n", "    for i, (rule, remaining) in enumerate(zip(detection_info['rules_applied'], \n", "                                            detection_info['points_remaining'])):\n", "        print(f\"  {i+1}. {rule}: {remaining:,} points remaining\")\n", "        \n", "else:\n", "    print(\"❌ Please run geometric feature extraction first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5️⃣ Clustering and Pile Identification\n", "\n", "Group candidate points into individual pile instances."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cluster_pile_candidates(points, pile_candidates, eps=2.0, min_samples=50):\n", "    \"\"\"\n", "    Cluster pile candidate points into individual pile instances.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        All point coordinates\n", "    pile_candidates : nump<PERSON>.n<PERSON><PERSON>\n", "        Boolean mask for pile candidate points\n", "    eps : float\n", "        DBSCAN clustering distance parameter\n", "    min_samples : int\n", "        Minimum points required to form a pile cluster\n", "        \n", "    Returns:\n", "    --------\n", "    pile_clusters : dict\n", "        Dictionary with cluster information\n", "    \"\"\"\n", "    \n", "    print(f\"🔍 Clustering pile candidates...\")\n", "    print(f\"  Parameters: eps={eps}m, min_samples={min_samples}\")\n", "    \n", "    # Extract candidate points\n", "    candidate_points = points[pile_candidates]\n", "    \n", "    if len(candidate_points) == 0:\n", "        print(\"❌ No pile candidates to cluster\")\n", "        return {'clusters': [], 'n_clusters': 0}\n", "    \n", "    # Apply DBSCAN clustering\n", "    clustering = DBSCAN(eps=eps, min_samples=min_samples)\n", "    cluster_labels = clustering.fit_predict(candidate_points)\n", "    \n", "    # Analyze clusters\n", "    unique_labels = np.unique(cluster_labels)\n", "    n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)  # Exclude noise (-1)\n", "    n_noise = np.sum(cluster_labels == -1)\n", "    \n", "    print(f\"  Found {n_clusters} pile clusters\")\n", "    print(f\"  Noise points: {n_noise}\")\n", "    \n", "    # Extract cluster information\n", "    clusters = []\n", "    \n", "    for label in unique_labels:\n", "        if label == -1:  # Skip noise\n", "            continue\n", "            \n", "        cluster_mask = cluster_labels == label\n", "        cluster_points = candidate_points[cluster_mask]\n", "        \n", "        # Calculate cluster properties\n", "        centroid = np.mean(cluster_points, axis=0)\n", "        bbox_min = np.min(cluster_points, axis=0)\n", "        bbox_max = np.max(cluster_points, axis=0)\n", "        dimensions = bbox_max - bbox_min\n", "        \n", "        cluster_info = {\n", "            'cluster_id': int(label),\n", "            'n_points': len(cluster_points),\n", "            'centroid': centroid.tolist(),\n", "            'bbox_min': bbox_min.tolist(),\n", "            'bbox_max': bbox_max.tolist(),\n", "            'dimensions': dimensions.tolist(),\n", "            'height': float(dimensions[2]),\n", "            'width': float(max(dimensions[0], dimensions[1])),\n", "            'aspect_ratio': float(dimensions[2] / max(dimensions[0], dimensions[1])) if max(dimensions[0], dimensions[1]) > 0 else 0\n", "        }\n", "        \n", "        clusters.append(cluster_info)\n", "    \n", "    # Sort clusters by number of points (largest first)\n", "    clusters.sort(key=lambda x: x['n_points'], reverse=True)\n", "    \n", "    return {\n", "        'clusters': clusters,\n", "        'n_clusters': n_clusters,\n", "        'cluster_labels': cluster_labels,\n", "        'candidate_points': candidate_points,\n", "        'n_noise': n_noise\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cluster pile candidates\n", "if 'pile_candidates' in locals():\n", "    \n", "    # Configuration\n", "    CLUSTERING_EPS = 2.0      # Distance threshold for clustering (meters)\n", "    MIN_PILE_POINTS = 30      # Minimum points to consider a valid pile\n", "    \n", "    pile_clusters = cluster_pile_candidates(\n", "        points=points,\n", "        pile_candidates=pile_candidates,\n", "        eps=CLUSTERING_EPS,\n", "        min_samples=MIN_PILE_POINTS\n", "    )\n", "    \n", "    # Display cluster statistics\n", "    if pile_clusters['n_clusters'] > 0:\n", "        print(f\"\\n📊 Cluster Analysis:\")\n", "        print(f\"  Total clusters: {pile_clusters['n_clusters']}\")\n", "        \n", "        # Show top 10 clusters\n", "        print(f\"\\n🏗️ Top Pile Clusters:\")\n", "        for i, cluster in enumerate(pile_clusters['clusters'][:10]):\n", "            print(f\"  {i+1}. Cluster {cluster['cluster_id']}: {cluster['n_points']} points, \"\n", "                  f\"H={cluster['height']:.1f}m, W={cluster['width']:.1f}m, \"\n", "                  f\"AR={cluster['aspect_ratio']:.1f}\")\n", "    \n", "    else:\n", "        print(\"❌ No valid pile clusters found. Consider adjusting detection parameters.\")\n", "        \n", "else:\n", "    print(\"❌ Please run pile detection first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6️⃣ Pile Validation and Filtering\n", "\n", "Apply additional validation rules to filter out false positives."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def validate_pile_clusters(clusters, min_height=2.0, max_width=3.0, \n", "                          min_aspect_ratio=1.5, min_points=50):\n", "    \"\"\"\n", "    Validate and filter pile clusters based on geometric constraints.\n", "    \n", "    Parameters:\n", "    -----------\n", "    clusters : list\n", "        List of cluster dictionaries\n", "    min_height : float\n", "        Minimum pile height (meters)\n", "    max_width : float\n", "        Maximum pile width (meters)\n", "    min_aspect_ratio : float\n", "        Minimum height/width ratio\n", "    min_points : int\n", "        Minimum points per pile\n", "        \n", "    Returns:\n", "    --------\n", "    valid_piles : list\n", "        Filtered list of valid pile clusters\n", "    validation_info : dict\n", "        Validation statistics\n", "    \"\"\"\n", "    \n", "    print(f\"🔍 Validating pile clusters...\")\n", "    print(f\"  Criteria: H>={min_height}m, W<={max_width}m, AR>={min_aspect_ratio}, Points>={min_points}\")\n", "    \n", "    valid_piles = []\n", "    validation_stats = {\n", "        'total_clusters': len(clusters),\n", "        'height_filter': 0,\n", "        'width_filter': 0,\n", "        'aspect_ratio_filter': 0,\n", "        'points_filter': 0,\n", "        'valid_piles': 0\n", "    }\n", "    \n", "    for cluster in clusters:\n", "        is_valid = True\n", "        \n", "        # Height filter\n", "        if cluster['height'] < min_height:\n", "            validation_stats['height_filter'] += 1\n", "            is_valid = False\n", "        \n", "        # Width filter\n", "        if cluster['width'] > max_width:\n", "            validation_stats['width_filter'] += 1\n", "            is_valid = False\n", "        \n", "        # Aspect ratio filter\n", "        if cluster['aspect_ratio'] < min_aspect_ratio:\n", "            validation_stats['aspect_ratio_filter'] += 1\n", "            is_valid = False\n", "        \n", "        # Points filter\n", "        if cluster['n_points'] < min_points:\n", "            validation_stats['points_filter'] += 1\n", "            is_valid = False\n", "        \n", "        if is_valid:\n", "            valid_piles.append(cluster)\n", "            validation_stats['valid_piles'] += 1\n", "    \n", "    print(f\"\\n📊 Validation Results:\")\n", "    print(f\"  Input clusters: {validation_stats['total_clusters']}\")\n", "    print(f\"  Failed height filter: {validation_stats['height_filter']}\")\n", "    print(f\"  Failed width filter: {validation_stats['width_filter']}\")\n", "    print(f\"  Failed aspect ratio filter: {validation_stats['aspect_ratio_filter']}\")\n", "    print(f\"  Failed points filter: {validation_stats['points_filter']}\")\n", "    print(f\"  Valid piles: {validation_stats['valid_piles']}\")\n", "    \n", "    return valid_piles, validation_stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Validate pile clusters\n", "if 'pile_clusters' in locals() and pile_clusters['n_clusters'] > 0:\n", "    \n", "    # Validation parameters (adjust based on expected pile characteristics)\n", "    MIN_PILE_HEIGHT = 2.0     # Minimum pile height (meters)\n", "    MAX_PILE_WIDTH = 2.5      # Maximum pile width (meters)\n", "    MIN_ASPECT_RATIO = 1.2    # Minimum height/width ratio\n", "    MIN_PILE_POINTS = 40      # Minimum points per pile\n", "    \n", "    valid_piles, validation_stats = validate_pile_clusters(\n", "        clusters=pile_clusters['clusters'],\n", "        min_height=MIN_PILE_HEIGHT,\n", "        max_width=MAX_PILE_WIDTH,\n", "        min_aspect_ratio=MIN_ASPECT_RATIO,\n", "        min_points=MIN_PILE_POINTS\n", "    )\n", "    \n", "    # Display valid piles\n", "    if valid_piles:\n", "        print(f\"\\n✅ Valid Pile Detections:\")\n", "        for i, pile in enumerate(valid_piles):\n", "            centroid = pile['centroid']\n", "            print(f\"  Pile {i+1}: Center({centroid[0]:.1f}, {centroid[1]:.1f}, {centroid[2]:.1f}), \"\n", "                  f\"H={pile['height']:.1f}m, W={pile['width']:.1f}m, \"\n", "                  f\"Points={pile['n_points']}\")\n", "    else:\n", "        print(\"❌ No valid piles found after validation. Consider relaxing criteria.\")\n", "        \n", "else:\n", "    print(\"❌ No pile clusters to validate.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7️⃣ Export Results\n", "\n", "Save the detected pile locations for further analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_detection_results(valid_piles, detection_info, validation_stats, \n", "                           output_path=None, site_name=\"castro\"):\n", "    \"\"\"\n", "    Export pile detection results.\n", "    \"\"\"\n", "    \n", "    if output_path is None:\n", "        output_path = Path('../..') / 'output' / 'cad_detection'\n", "    \n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # 1. Save pile locations as CSV\n", "    if valid_piles:\n", "        pile_data = []\n", "        for i, pile in enumerate(valid_piles):\n", "            pile_data.append({\n", "                'pile_id': i + 1,\n", "                'x': pile['centroid'][0],\n", "                'y': pile['centroid'][1],\n", "                'z': pile['centroid'][2],\n", "                'height': pile['height'],\n", "                'width': pile['width'],\n", "                'aspect_ratio': pile['aspect_ratio'],\n", "                'n_points': pile['n_points'],\n", "                'detection_method': 'rule_based_cad'\n", "            })\n", "        \n", "        pile_df = pd.DataFrame(pile_data)\n", "        csv_path = output_path / f\"{site_name}_detected_piles.csv\"\n", "        pile_df.to_csv(csv_path, index=False)\n", "        print(f\"💾 Saved pile locations: {csv_path}\")\n", "    \n", "    # 2. Save detection summary as JSON\n", "    summary = {\n", "        'site_name': site_name,\n", "        'detection_method': 'rule_based_cad',\n", "        'total_points': detection_info['initial_points'],\n", "        'pile_candidates': detection_info['final_candidates'],\n", "        'valid_piles': len(valid_piles),\n", "        'detection_rate': detection_info['detection_rate'],\n", "        'validation_stats': validation_stats,\n", "        'rules_applied': detection_info['rules_applied'],\n", "        'pile_details': valid_piles\n", "    }\n", "    \n", "    json_path = output_path / f\"{site_name}_detection_summary.json\"\n", "    with open(json_path, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    print(f\"💾 Saved detection summary: {json_path}\")\n", "    \n", "    return {\n", "        'csv_path': csv_path if valid_piles else None,\n", "        'json_path': json_path\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export detection results\n", "if 'valid_piles' in locals() and 'detection_info' in locals():\n", "    \n", "    exported_files = export_detection_results(\n", "        valid_piles=valid_piles,\n", "        detection_info=detection_info,\n", "        validation_stats=validation_stats,\n", "        output_path=output_path,\n", "        site_name=site_name\n", "    )\n", "    \n", "    print(f\"\\n✅ Export complete! Files saved to: {output_path}\")\n", "    \n", "else:\n", "    print(\"❌ No detection results to export.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 Summary\n", "\n", "This notebook demonstrated rule-based pile detection for CAD-only sites:\n", "\n", "### ✅ **What We Accomplished:**\n", "1. **Loaded point cloud data** from photogrammetry\n", "2. **Extracted geometric features** for pile identification\n", "3. **Applied rule-based detection** using geometric constraints\n", "4. **Clustered candidates** into individual pile instances\n", "5. **Validated detections** with dimensional constraints\n", "6. **Exported results** for further analysis\n", "\n", "### 🎯 **Key Advantages:**\n", "- **No Training Data Required**: Works without labeled examples\n", "- **Interpretable Rules**: Clear geometric criteria\n", "- **Adaptable**: Parameters can be tuned for different sites\n", "- **Fast Processing**: Efficient geometric computations\n", "\n", "### ⚙️ **Parameter Tuning Guidelines:**\n", "\n", "#### **Detection Parameters:**\n", "- **Neighborhood Size**: Increase for larger piles, decrease for dense point clouds\n", "- **Height Filter**: Adjust based on expected pile heights above ground\n", "- **Verticality Threshold**: Lower for tilted piles, higher for strict vertical detection\n", "- **Linearity Threshold**: Adjust based on pile shape (cylindrical vs. I-beam)\n", "\n", "#### **Clustering Parameters:**\n", "- **DBSCAN eps**: Distance threshold - increase for sparse point clouds\n", "- **Min <PERSON>ples**: Minimum points per pile - adjust based on point density\n", "\n", "#### **Validation Parameters:**\n", "- **Min Height**: Expected minimum pile height\n", "- **<PERSON>**: Expected maximum pile width\n", "- **Min Aspect Ratio**: Height/width ratio for pile-like structures\n", "\n", "### 🔄 **Next Steps:**\n", "1. **Apply to Multiple Sites**: Test on <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>\n", "2. **Refine Parameters**: Tune based on site-specific characteristics\n", "3. **Validate Results**: Compare with manual inspection or other methods\n", "4. **Integrate with ML**: Use results to train supervised models\n", "\n", "### 🎯 **Site-Specific Considerations:**\n", "\n", "#### **Castro Site:**\n", "- Adjust for specific pile types and construction patterns\n", "- Consider environmental factors (vegetation, terrain)\n", "\n", "#### **Mudjar Site:**\n", "- Tune parameters for local construction standards\n", "- Account for different pile materials and sizes\n", "\n", "#### **Giorgio Site:**\n", "- Adapt to site-specific geometric constraints\n", "- Consider unique construction characteristics\n", "\n", "**📧 Contact**: For questions about this workflow or site-specific tuning, reach out to the development team."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}