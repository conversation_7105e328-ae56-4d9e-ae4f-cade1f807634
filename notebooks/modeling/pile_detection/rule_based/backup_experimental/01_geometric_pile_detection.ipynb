{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# Geometric C-Section Pile Detection\n", "\n", "This notebook implements rule-based geometric detection of C-section piles using point cloud analysis.\n", "\n", "## Approach\n", "1. **Filter elevated points** above ground level\n", "2. **Cluster vertical structures** using DBSCAN\n", "3. **Analyze cluster geometry** for C-section characteristics\n", "4. **Score and filter** based on confidence thresholds\n", "5. **Validate against IFC ground truth** for performance assessment\n", "\n", "## Key Parameters\n", "- **C-section dimensions**: Based on TRJHT56PDP-BF model specifications\n", "- **Clustering**: DBSCAN with spatial proximity\n", "- **Scoring**: Multi-criteria analysis (height, width, aspect ratio, distribution)\n", "\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "markdown", "id": "5a101c14", "metadata": {}, "source": ["## 0. Import libraries\n"]}, {"cell_type": "code", "execution_count": 50, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports completed successfully!\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.decomposition import PCA\n", "from datetime import datetime\n", "import json\n", "from tabulate import tabulate\n", "import seaborn as sns\n", "\n", "print(\"Imports completed successfully!\")"]}, {"cell_type": "markdown", "id": "b562154d", "metadata": {}, "source": ["## 1. Configuration"]}, {"cell_type": "code", "execution_count": 51, "id": "configuration", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for project root from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/modeling/pile_detection/rule_based\n", "Found project root at: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n", "Configuration initialized:\n", "  Site: trino_enel\n", "  Ground method: csf\n", "  Expected piles: 14460\n", "  Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/data/output_runs/pile_detection/geometric_csf\n", " Debug mode: True\n"]}], "source": ["class GeometricPileDetectionConfig:\n", "    \"\"\"Configuration for geometric C-section pile detection\"\"\"\n", "    \n", "    def __init__(self):\n", "        # === Input/Output Paths ===\n", "        self.site_name = \"trino_enel\"\n", "        self.ground_method = \"csf\"  # or \"pmf\", \"ransac\"\n", "\n", "\n", "        # === Paths ===\n", "        self.root = self._find_project_root(marker_dir=\"data\")\n", "        #self.point_cloud_file = self.root / f\"data/output_runs//{self.ground_method}/aligned_ifc_{self.ground_method}.ply\"\n", "        self.point_cloud_file = Path(\"../../../../data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered//trino_enel_gcp_aligned_z_corrected_filtered.ply\")\n", "\n", "        self.ifc_pile_csv = Path(\"../../../../data/processed/trino_enel/advanced_ifc_metadata/advanced_pile_coordinates.csv\")\n", "        self.output_dir = self.root / f\"data/output_runs/pile_detection/geometric_{self.ground_method}\"\n", "        self.output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "        # === C-section Physical Dimensions ===\n", "        # === Pile Geometry Parameters (Based on TRJHT56PDP-BF) ===\n", "        self.expected_height = 2.0          # Expected pile height in meters\n", "        self.expected_width = 0.062         # Expected pile width in meters\n", "        self.expected_depth = 0.12          # Expected pile depth in meters\n", "        self.width_tolerance = 0.005        # Allowed variation in width\n", "        self.height_tolerance = 0.5         # Allowed variation in height\n", "\n", "        # === Elevation Filtering ===\n", "        # Tighten Z-range to remove tall/noisy objects\n", "        self.min_height = 1.0               # Minimum Z elevation from ground (tighter filter)\n", "        self.max_height = 3.5               # Maximum Z elevation from ground (tighter filter)\n", "        \n", "        # === Clustering Parameters ===\n", "        self.dbscan_eps = 0.5              # Radius for DBSCAN clustering\n", "        self.min_points_per_cluster = 8    # Minimum points in valid cluster\n", "        self.max_cluster_size = 1000         # Ignore overly large noisy clusters\n", "        \n", "        # === Scoring Parameters ===\n", "        self.confidence_threshold = 0.60     # Minimum score to accept detection\n", "        self.height_weight = 0.25            # Weight for height score\n", "        self.width_weight = 0.35             # Weight for width score\n", "        self.aspect_weight = 0.20            # Weight for aspect ratio score\n", "        self.distribution_weight = 0.15      # Weight for point distribution score\n", "        self.density_weight = 0.15           # Weight for density score\n", "\n", "        # === Aspect Ratio Constraints ===\n", "        self.min_aspect_ratio = 1.0         # Minimum width/depth ratio\n", "        self.max_aspect_ratio = 6.0         # Maximum width/depth ratio\n", "        \n", "        # === Validation Parameters ===\n", "        self.validation_radius = 3.0        # Radius for matching detections to IFC piles\n", "        self.use_kd_tree_matching = True    # Use k-d tree nearest neighbor instead of radius\n", "        self.max_kd_tree_distance = 5.0     # Maximum distance for k-d tree matching\n", "\n", "        # === Multi-Criteria Validation ===\n", "        self.enable_multi_criteria = True\n", "        self.min_criteria_passed = 3        # Must pass at least 3/5 criteria\n", "\n", "        # === Grid Constraint Parameters ===\n", "        self.enforce_grid_spacing = False     # Enforce regular grid spacing constraint\n", "        self.expected_grid_spacing = 2.5     # Expected spacing between piles in meters\n", "        self.grid_tolerance = 1.5            # Tolerance for grid alignment\n", "\n", "        # === Morphological Filtering ===\n", "        self.enable_morphological_filter = True\n", "        self.min_vertical_density = 0.8     # Points per vertical meter\n", "        self.max_horizontal_spread = 0.3    # Maximum XY spread\n", " \n", "        # === Visualization Parameters ===\n", "        self.plot_confidence_heatmap = True  # Generate confidence score heatmap\n", "        self.use_overlay_plots = True        # Use overlay visualization instead of side-by-side\n", "        self.expected_pile_count = 14460     # Ground truth pile count from IFC\n", "\n", "        # === Debug Parameters ===\n", "        self.debug_mode = True                  #  Enable debug output\n", "        self.save_intermediate_results = True   #  Save clustering results\n", "\n", "    \n", "    def _find_project_root(self, marker_dir: str, max_levels: int = 10) -> Path:\n", "        \"\"\"Finds the project root by looking for a directory containing the given marker (e.g. 'data')\"\"\"\n", "        current = Path(__file__).resolve().parent if \"__file__\" in globals() else Path().resolve()\n", "        print(f\"Searching for project root from: {current}\")\n", "\n", "        expected_subdirs = {\"processed\", \"output_runs\"}  # Add more if needed\n", "\n", "        for _ in range(max_levels):\n", "            data_dir = current / marker_dir\n", "            if data_dir.is_dir():\n", "                subdirs = {p.name for p in data_dir.iterdir() if p.is_dir()}\n", "                if expected_subdirs.issubset(subdirs):\n", "                    print(f\"Found project root at: {current}\")\n", "                    return current\n", "            if current == current.parent:\n", "                break\n", "            current = current.parent\n", "\n", "        raise FileNotFoundError(f\"Could not find project root containing '{marker_dir}' with expected subdirs in any parent.\")\n", "\n", "\n", "# Initialize configuration\n", "config = GeometricPileDetectionConfig()\n", "print(f\"Configuration initialized:\")\n", "print(f\"  Site: {config.site_name}\")\n", "print(f\"  Ground method: {config.ground_method}\")\n", "print(f\"  Expected piles: {config.expected_pile_count}\")\n", "print(f\"  Output directory: {config.output_dir}\")\n", "print(f\" Debug mode: {config.debug_mode}\")"]}, {"cell_type": "markdown", "id": "ea93a1c0", "metadata": {}, "source": ["## 2. Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 52, "id": "load_data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud...\n", "Loaded 517,002 points from ../../../../data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "Point cloud bounds:\n", "  X: 435220.3 - 436796.3\n", "  Y: 5010812.7 - 5012553.1\n", "  Z: 152.4 - 182.4\n"]}], "source": ["print(\"Loading point cloud...\")\n", "\n", "# Simple existence check and load\n", "if not config.point_cloud_file.exists():\n", "    raise FileNotFoundError(f\"Point cloud file not found: {config.point_cloud_file}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(config.point_cloud_file))\n", "points = np.asarray(pcd.points)\n", "\n", "print(f\"Loaded {points.shape[0]:,} points from {config.point_cloud_file}\")\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {points[:, 0].min():.1f} - {points[:, 0].max():.1f}\")\n", "print(f\"  Y: {points[:, 1].min():.1f} - {points[:, 1].max():.1f}\")\n", "print(f\"  Z: {points[:, 2].min():.1f} - {points[:, 2].max():.1f}\")"]}, {"cell_type": "code", "execution_count": 53, "id": "7a15aa0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 24256\n", "-rw-r--r--@ 1 <USER>  <GROUP>    12M Jul 21 13:30 trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "-rw-r--r--@ 1 <USER>  <GROUP>   227B Jul 21 13:30 trino_enel_gcp_residuals_z_corrected_filtered.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   976B Jul 21 13:30 trino_enel_gcp_transform_params_z_corrected_filtered.json\n"]}], "source": ["!ls -lh ../../../../data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/"]}, {"cell_type": "markdown", "id": "057ac6e8", "metadata": {}, "source": ["## 3. Load IFC Ground Truth"]}, {"cell_type": "code", "execution_count": 54, "id": "load_ground_truth", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 14460 IFC pile records\n", "IFC pile coordinate ranges:\n", "  X: 435267.2 - 436719.9\n", "  Y: 5010900.7 - 5012462.4\n", "  Z: 157.1 - 161.7\n", "\n", "Ground truth loaded: 14460 piles\n"]}], "source": ["def load_ifc_ground_truth(config):\n", "    \"\"\"Load IFC pile coordinates as ground truth for validation\"\"\"\n", "    try:\n", "        if not config.ifc_pile_csv.exists():\n", "            raise FileNotFoundError(f\"IFC pile CSV not found: {config.ifc_pile_csv}\")\n", "\n", "        ifc_piles = pd.read_csv(config.ifc_pile_csv)\n", "        print(f\"Loaded {len(ifc_piles)} IFC pile records\")\n", "        \n", "        required_cols = ['X', 'Y', 'Z', 'Tag', 'Name']\n", "        missing_cols = [col for col in required_cols if col not in ifc_piles.columns]\n", "        \n", "        if missing_cols:\n", "            raise ValueError(f\"Missing required columns in IFC pile CSV: {missing_cols}\")\n", "\n", "        # Extract coordinates\n", "        pile_coords = ifc_piles[['X', 'Y', 'Z']].values\n", "        pile_ids = ifc_piles['Tag'].values\n", "        pile_names = ifc_piles['Name'].values\n", "        \n", "        print(f\"IFC pile coordinate ranges:\")\n", "        print(f\"  X: {pile_coords[:, 0].min():.1f} - {pile_coords[:, 0].max():.1f}\")\n", "        print(f\"  Y: {pile_coords[:, 1].min():.1f} - {pile_coords[:, 1].max():.1f}\")\n", "        print(f\"  Z: {pile_coords[:, 2].min():.1f} - {pile_coords[:, 2].max():.1f}\")\n", "        \n", "        ground_truth = {\n", "            'coordinates': pile_coords,\n", "            'ids': pile_ids,\n", "            'names': pile_names,\n", "            'count': len(pile_coords),\n", "            'piles': [\n", "                {'x': float(coord[0]), 'y': float(coord[1]), 'z': float(coord[2]), \n", "                 'id': str(pile_ids[i]), 'name': str(pile_names[i])}\n", "                for i, coord in enumerate(pile_coords)\n", "            ]\n", "        }\n", "\n", "        \n", "        \n", "        return ground_truth\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading IFC ground truth: {e}\")\n", "        return None\n", "\n", "# Load ground truth\n", "ground_truth = load_ifc_ground_truth(config)\n", "if ground_truth:\n", "    print(f\"\\nGround truth loaded: {ground_truth['count']} piles\")\n", "else:\n", "    print(\"No ground truth available - proceeding without validation\")"]}, {"cell_type": "markdown", "id": "d50a9aa1", "metadata": {}, "source": ["## 4. Geometric Detection Functions"]}, {"cell_type": "code", "execution_count": 55, "id": "geometric_functions", "metadata": {}, "outputs": [], "source": ["def filter_elevated_points(pts, config):\n", "    \"\"\"Step 1: Filter points with tighter Z-range (elevation banding)\"\"\"\n", "    z_min = np.min(pts[:, 2])\n", "    z_max = np.max(pts[:, 2])\n", "\n", "    print(f\"Point cloud Z range: {z_min:.2f} to {z_max:.2f}\")\n", "\n", "    # Apply tighter Z-range filter to remove tall/noisy objects\n", "    height_above_ground = pts[:, 2] - z_min\n", "    mask = (height_above_ground >= config.min_height) & (height_above_ground <= config.max_height)\n", "    elevated_pts = pts[mask]\n", "    \n", "    print(f\"Elevation banding filter ({config.min_height:.1f}m - {config.max_height:.1f}m):\")\n", "    print(f\"  Filtered points: {len(elevated_pts):,} / {len(pts):,} ({len(elevated_pts)/len(pts)*100:.1f}%)\")\n", "    print(f\"  Removed {len(pts) - len(elevated_pts):,} points outside elevation band\")\n", "    return elevated_pts"]}, {"cell_type": "code", "execution_count": 56, "id": "c3bc8349", "metadata": {}, "outputs": [], "source": ["def cluster_vertical_structures(elevated_pts, config):\n", "    \"\"\"Step 2: Cluster nearby elevated points using DBSCAN\"\"\"\n", "    if len(elevated_pts) < config.min_points_per_cluster:\n", "        print(f\"Insufficient points for clustering: {len(elevated_pts)} < {config.min_points_per_cluster}\")\n", "        return []\n", "    \n", "    print(f\"Clustering {len(elevated_pts)} points with eps={config.dbscan_eps}m, min_samples={config.min_points_per_cluster}\")\n", "\n", "    # Cluster in XY plane only\n", "    labels = DBSCAN(eps=config.dbscan_eps, min_samples=config.min_points_per_cluster).fit_predict(elevated_pts[:, :2])\n", "    \n", "    # Extract clusters\n", "    unique_labels = set(labels)\n", "    clusters = []\n", "    noise_count = np.sum(labels == -1)\n", "\n", "    print(f\" DBSCAN found {len(unique_labels)-1} clusters and {noise_count} noise points\")\n", "    \n", "    for label in unique_labels:\n", "        if label == -1:  # Skip noise points\n", "            continue\n", "        \n", "        cluster_points = elevated_pts[labels == label]\n", "        \n", "        # Filter by cluster size\n", "        if config.min_points_per_cluster <= len(cluster_points) <= config.max_cluster_size:\n", "            clusters.append(cluster_points)\n", "    \n", "    print(f\"Found {len(clusters)} valid clusters from {len(unique_labels)-1} total clusters\")\n", "    if clusters:\n", "        cluster_sizes = [len(c) for c in clusters]\n", "        print(f\"  Cluster sizes: min={min(cluster_sizes)}, max={max(cluster_sizes)}, avg={np.mean(cluster_sizes):.1f}\")\n", "    \n", "    return clusters"]}, {"cell_type": "code", "execution_count": 57, "id": "2757bcc5", "metadata": {}, "outputs": [], "source": ["def is_regular_spacing(coords, expected_spacing, tolerance):\n", "    \"\"\"Check if sorted coordinates are spaced regularly within a tolerance.\"\"\"\n", "    spacings = np.diff(np.sort(coords))\n", "    if len(spacings) == 0:\n", "        return False\n", "    avg_spacing = np.mean(spacings)\n", "    return np.abs(avg_spacing - expected_spacing) < tolerance\n", "\n", "def find_grid_aligned_indices(centers, axis, config):\n", "    \"\"\"\n", "    Find indices of points aligned in regular grid lines along the specified axis ('x' or 'y').\n", "    \"\"\"\n", "    aligned_indices = set()\n", "    primary_idx = 0 if axis == 'x' else 1\n", "\n", "    for i, center in enumerate(centers):\n", "        coord = center[primary_idx]\n", "        nearby = np.abs(centers[:, primary_idx] - coord) < config.grid_tolerance\n", "\n", "        if np.sum(nearby) >= 3:\n", "            line_centers = centers[nearby]\n", "            orthogonal_coords = line_centers[:, 1 - primary_idx]\n", "\n", "            if is_regular_spacing(orthogonal_coords, config.expected_grid_spacing, config.grid_tolerance):\n", "                aligned_indices.update(np.where(nearby)[0])\n", "    \n", "    return aligned_indices\n", "\n", "def enforce_grid_spacing_constraint(clusters, config):\n", "    \"\"\"Step 2.5: Filter clusters based on regular grid spacing constraint.\"\"\"\n", "    if not config.enforce_grid_spacing or len(clusters) < 3:\n", "        return clusters\n", "\n", "    centers = np.array([cluster.mean(axis=0)[:2] for cluster in clusters])\n", "\n", "    horizontal_indices = find_grid_aligned_indices(centers, axis='y', config=config)\n", "    vertical_indices = find_grid_aligned_indices(centers, axis='x', config=config)\n", "\n", "    grid_aligned_indices = horizontal_indices.union(vertical_indices)\n", "\n", "    if grid_aligned_indices:\n", "        filtered_clusters = [clusters[i] for i in sorted(grid_aligned_indices)]\n", "        print(f\"Grid spacing constraint: kept {len(filtered_clusters)} / {len(clusters)} clusters\")\n", "        return filtered_clusters\n", "    else:\n", "        print(f\"Grid spacing constraint: no grid-aligned clusters found, keeping all {len(clusters)} clusters\")\n", "        return clusters"]}, {"cell_type": "code", "execution_count": 58, "id": "analysis_functions", "metadata": {}, "outputs": [], "source": ["def calculate_distribution_score(pts):\n", "    \"\"\"Evaluate how linearly or elongated the points are using PCA\"\"\"\n", "    if len(pts) < 10:\n", "        return 0.5  # Neutral score for small clusters\n", "    \n", "    try:\n", "        # PCA on XY coordinates\n", "        pca = PCA(n_components=2)\n", "        pca.fit(pts[:, :2])\n", "        \n", "        # Ratio of explained variance\n", "        var_ratio = pca.explained_variance_ratio_\n", "        \n", "        # For C-sections, we expect some elongation but not extreme\n", "        # Good score for moderate elongation (0.6-0.8 ratio)\n", "        primary_var = var_ratio[0]\n", "        if 0.6 <= primary_var <= 0.8:\n", "            return 0.8\n", "        elif 0.5 <= primary_var <= 0.9:\n", "            return 0.6\n", "        else:\n", "            return 0.3\n", "    except:\n", "        return 0.5\n"]}, {"cell_type": "code", "execution_count": 59, "id": "24c3090a", "metadata": {}, "outputs": [], "source": ["def analyze_cluster(cluster, config):\n", "    \"\"\"Analyze a cluster's shape and point distribution for C-section characteristics.\"\"\"\n", "\n", "    import numpy as np\n", "\n", "    # Calculate spatial extents\n", "    x_span = np.ptp(cluster[:, 0])\n", "    y_span = np.ptp(cluster[:, 1])\n", "    z_span = np.ptp(cluster[:, 2])\n", "\n", "    # Horizontal dimensions\n", "    max_horizontal = max(x_span, y_span)\n", "    min_horizontal = min(x_span, y_span)\n", "    aspect_ratio = max_horizontal / (min_horizontal + 1e-6)\n", "\n", "    # === Hard width filter ===\n", "    width_diff = abs(max_horizontal - config.expected_width)\n", "    if width_diff > config.width_tolerance:\n", "        if config.debug_mode:\n", "            print(f\"[REJECTED] Cluster width {max_horizontal:.3f}m deviates from expected {config.expected_width}m (> tol {config.width_tolerance}m)\")\n", "        return None\n", "\n", "    # === CRITERION 1: Height Score ===\n", "    height_diff = abs(z_span - config.expected_height)\n", "    height_score = max(0, 1.0 - (height_diff / config.height_tolerance))\n", "    height_pass = height_score >= config.height_weight\n", "\n", "    # === CRITERION 2: Width Score ===\n", "    width_score = max(0, 1.0 - (width_diff / config.width_tolerance))\n", "    width_pass = width_score >= config.width_weight\n", "\n", "    # === CRITERION 3: Aspect Ratio ===\n", "    if config.min_aspect_ratio <= aspect_ratio <= config.max_aspect_ratio:\n", "        aspect_score = 0.8\n", "    elif 0.5 <= aspect_ratio <= 8.0:\n", "        aspect_score = 0.6\n", "    else:\n", "        aspect_score = 0.3\n", "    aspect_pass = aspect_score >= config.aspect_weight\n", "\n", "    # === CRITERION 4: Point Density ===\n", "    volume = max(x_span * y_span * z_span, 1e-6)  # prevent div by 0\n", "    density = len(cluster) / volume\n", "    density_score = min(density / 100.0, 1.0)\n", "    density_pass = density_score >= config.density_weight\n", "\n", "    # === CRITERION 5: Vertical Distribution ===\n", "    vertical_density = len(cluster) / max(z_span, 1e-6)\n", "    vertical_density_score = min(vertical_density / 100.0, 1.0)\n", "    vertical_density_pass = vertical_density_score >= config.density_weight\n", "\n", "    # === CRITERION 6: Horizontal Compactness ===\n", "    center_xy = np.mean(cluster[:, :2], axis=0)\n", "    distances_xy = np.linalg.norm(cluster[:, :2] - center_xy, axis=1)\n", "    max_spread = np.max(distances_xy)\n", "    compactness_score = max(0, 1.0 - (max_spread / config.max_horizontal_spread))\n", "    compactness_pass = max_spread < config.max_horizontal_spread\n", "\n", "    # === Combined Confidence ===\n", "    confidence = (\n", "        height_score * config.height_weight +\n", "        width_score * config.width_weight +\n", "        aspect_score * config.aspect_weight +\n", "        vertical_density_score * config.distribution_weight +\n", "        density_score * config.density_weight +\n", "        compactness_score * config.density_weight\n", "    )\n", "\n", "    if config.debug_mode:\n", "        print(\n", "            f\"[ACCEPTED] Cluster shape → W: {max_horizontal:.3f}m, D: {min_horizontal:.3f}m, H: {z_span:.3f}m | \"\n", "            f\"Scores → Width: {width_score:.2f}, Aspect: {aspect_ratio:.2f}, Conf: {confidence:.2f}\"\n", "        )\n", "\n", "    return {\n", "        'confidence': confidence,\n", "        'height_score': height_score,\n", "        'width_score': width_score,\n", "        'aspect_score': aspect_score,\n", "        'density_score': density_score,\n", "        'distribution_score': vertical_density_score,\n", "        'compactness_score': compactness_score,\n", "        'dimensions': {\n", "            'width': max_horizontal,\n", "            'depth': min_horizontal,\n", "            'height': z_span,\n", "            'aspect_ratio': aspect_ratio,\n", "            'density': density,\n", "            'vertical_density': vertical_density,\n", "            'max_spread': max_spread\n", "        },\n", "        'point_count': len(cluster)\n", "    }\n", "\n", "def compute_center(cluster):\n", "    \"\"\"Compute the centroid of a cluster\"\"\"\n", "    return np.mean(cluster, axis=0)"]}, {"cell_type": "markdown", "id": "122a5ad3", "metadata": {}, "source": ["## 5. Geometric C-Section Pile Detection"]}, {"cell_type": "code", "execution_count": 60, "id": "main_detection", "metadata": {}, "outputs": [], "source": ["def detect_piles_from_pointcloud(points, config):\n", "    \"\"\"Main function to detect C-section piles from point cloud.\"\"\"\n", "    \n", "    print(\"\\n=== GEOMETRIC PILE DETECTION ===\")\n", "    print(f\"Input points: {len(points):,}\")\n", "\n", "    # Step 1: Elevation filtering\n", "    elevated_pts = filter_elevated_points(points, config)\n", "    if len(elevated_pts) == 0:\n", "        print(\"No elevated points found\")\n", "        return []\n", "\n", "    # Step 2: Clustering\n", "    clusters = cluster_vertical_structures(elevated_pts, config)\n", "    if len(clusters) == 0:\n", "        print(\"No valid clusters found\")\n", "        return []\n", "\n", "    # Step 2.5: Grid spacing constraint (optional)\n", "    clusters = enforce_grid_spacing_constraint(clusters, config)\n", "    if len(clusters) == 0:\n", "        print(\"No grid-aligned clusters found\")\n", "        return []\n", "\n", "    print(f\"\\nAnalyzing {len(clusters):,} clusters...\")\n", "    \n", "    # Step 3: Analyze clusters\n", "    detections = []\n", "    rejected = 0\n", "    accepted = 0\n", "\n", "    for i, cluster in enumerate(clusters):\n", "        analysis = analyze_cluster(cluster, config)\n", "\n", "        if analysis is None:\n", "            rejected += 1\n", "            print(f\"[REJECTED] Cluster {i}\")\n", "            continue\n", "\n", "        confidence = analysis.get('confidence', 0.0)\n", "        height_score = analysis.get('height_score', 0.0)\n", "        width_score = analysis.get('width_score', 0.0)\n", "        aspect_score = analysis.get('aspect_score', 0.0)\n", "        distribution_score = analysis.get('distribution_score', 0.0)\n", "        density_score = analysis.get('density_score', 0.0)\n", "        compactness_score = analysis.get('compactness_score', 0.0)\n", "\n", "        if confidence >= config.confidence_threshold:\n", "            center = compute_center(cluster)\n", "            detection = {\n", "                'x': float(center[0]),\n", "                'y': float(center[1]),\n", "                'z': float(center[2]),\n", "                'confidence': float(confidence),\n", "                'cluster_id': i,\n", "                'point_count': analysis['point_count'],\n", "                'dimensions': analysis['dimensions'],\n", "                'scores': {\n", "                    'height': height_score,\n", "                    'width': width_score,\n", "                    'aspect': aspect_score,\n", "                    'distribution': distribution_score,\n", "                    'density': density_score,\n", "                    'compactness': compactness_score\n", "                }\n", "            }\n", "            accepted += 1\n", "            print(f\"[ACCEPTED] Cluster {i}\")\n", "            detections.append(detection)\n", "        else:\n", "            rejected += 1\n", "            if config.debug_mode:\n", "                print(f\"[REJECTED] Cluster {i} failed confidence threshold: {analysis['confidence']:.3f} < {config.confidence_threshold:.3f}\")\n", "\n", "    print(f\"\\n=== Detection Summary ===\")\n", "    print(f\"  Valid detections: {len(detections)} / {len(clusters)}\")\n", "    print(f\"  Rejected clusters: {rejected}\")\n", "    print(f\"  Detection rate: {len(detections)/len(clusters)*100:.1f}%\")\n", "\n", "\n", "    if detections:\n", "        confidences = np.array([d['confidence'] for d in detections])\n", "        print(f\"  Confidence range: {confidences.min():.3f} - {confidences.max():.3f}\")\n", "        print(f\"  Average confidence: {confidences.mean():.3f} ± {confidences.std():.3f}\")\n", "    else:\n", "        print(\"  No valid detections found.\")\n", "\n", "    return detections\n"]}, {"cell_type": "code", "execution_count": 61, "id": "run_detection", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting geometric pile detection...\n", "\n", "=== GEOMETRIC PILE DETECTION ===\n", "Input points: 517,002\n", "Point cloud Z range: 152.43 to 182.42\n", "Elevation banding filter (1.0m - 3.5m):\n", "  Filtered points: 166,676 / 517,002 (32.2%)\n", "  Removed 350,326 points outside elevation band\n", "Clustering 166676 points with eps=0.5m, min_samples=8\n", " DBSCAN found 1 clusters and 166668 noise points\n", "Found 1 valid clusters from 1 total clusters\n", "  Cluster sizes: min=8, max=8, avg=8.0\n", "\n", "Analyzing 1 clusters...\n", "[REJECTED] Cluster width 0.662m deviates from expected 0.062m (> tol 0.005m)\n", "[REJECTED] Cluster 0\n", "\n", "=== Detection Summary ===\n", "  Valid detections: 0 / 1\n", "  Rejected clusters: 1\n", "  Detection rate: 0.0%\n", "  No valid detections found.\n", "\n", "=== DETECTION COMPLETED ===\n", "Processing time: 0.35 seconds\n", "Detected piles: 0\n", "Expected piles (IFC): 14460\n", "Detection ratio: 0.000\n"]}], "source": ["print(\"Starting geometric pile detection...\")\n", "start_time = datetime.now()\n", "\n", "# Run detection\n", "geo_detections = detect_piles_from_pointcloud(points, config)\n", "\n", "end_time = datetime.now()\n", "processing_time = (end_time - start_time).total_seconds()\n", "\n", "print(f\"\\n=== DETECTION COMPLETED ===\")\n", "print(f\"Processing time: {processing_time:.2f} seconds\")\n", "print(f\"Detected piles: {len(geo_detections)}\")\n", "print(f\"Expected piles (IFC): {config.expected_pile_count}\")\n", "print(f\"Detection ratio: {len(geo_detections)/config.expected_pile_count:.3f}\")"]}, {"cell_type": "markdown", "id": "986028c6", "metadata": {}, "source": ["## 6. Plot Confidence Score Heatmap"]}, {"cell_type": "code", "execution_count": 62, "id": "9d49a07d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No detections to plot heatmap\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import random\n", "from scipy.interpolate import griddata\n", "\n", "def plot_confidence_heatmap(detections, config, grid_resolution=50):\n", "    \"\"\"Plot heatmap of confidence scores over XY plane\"\"\"\n", "    if not detections:\n", "        print(\"No detections to plot heatmap\")\n", "        return\n", "    \n", "    # Extract coordinates and confidence scores\n", "    x_coords = [d['x'] for d in detections]\n", "    y_coords = [d['y'] for d in detections]\n", "    confidences = [d['confidence'] for d in detections]\n", "    \n", "    # Create grid for interpolation\n", "    x_min, x_max = min(x_coords), max(x_coords)\n", "    y_min, y_max = min(y_coords), max(y_coords)\n", "    \n", "    xi = np.linspace(x_min, x_max, grid_resolution)\n", "    yi = np.linspace(y_min, y_max, grid_resolution)\n", "    xi_grid, yi_grid = np.meshgrid(xi, yi)\n", "    \n", "    # Interpolate confidence values\n", "    zi = griddata((x_coords, y_coords), confidences, (xi_grid, yi_grid), method='cubic', fill_value=0)\n", "    \n", "    # Plot heatmap\n", "    plt.figure(figsize=(12, 8))\n", "    plt.contourf(xi_grid, yi_grid, zi, levels=20, cmap='viridis', alpha=0.8)\n", "    plt.colorbar(label='Confidence Score')\n", "    \n", "    # Overlay detection points\n", "    scatter = plt.scatter(x_coords, y_coords, c=confidences, cmap='viridis', \n", "                         s=50, edgecolors='white', linewidth=1)\n", "    \n", "    plt.xlabel('X Coordinate (m)')\n", "    plt.ylabel('Y Coordinate (m)')\n", "    plt.title(f'Confidence Score Heatmap ({len(detections)} detections)')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.axis('equal')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "plot_confidence_heatmap(geo_detections, config)"]}, {"cell_type": "markdown", "id": "005c07a5", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> <PERSON> Nearest Neighbor Matching"]}, {"cell_type": "code", "execution_count": 63, "id": "validation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matched detections: 0\n", "False positives: 0\n"]}], "source": ["def match_detections_kd_tree(detections, ground_truth, config):\n", "    \"\"\"Match detections to ground truth using k-d tree nearest neighbor\"\"\"\n", "    from scipy.spatial import cKDTree\n", "    import numpy as np\n", "\n", "    if not ground_truth or len(detections) == 0:\n", "        return [], []\n", "\n", "    if 'piles' not in ground_truth:\n", "        print(\"Error: Ground truth missing 'piles' structure\")\n", "        return [], []\n", "\n", "    gt_coords = np.array([[pile['x'], pile['y']] for pile in ground_truth['piles']])\n", "    detection_coords = np.array([[d['x'], d['y']] for d in detections])\n", "\n", "    # Build k-d tree for ground truth\n", "    tree = cKDTree(gt_coords)\n", "\n", "    # Find nearest neighbors\n", "    distances, indices = tree.query(detection_coords, distance_upper_bound=config.max_kd_tree_distance)\n", "\n", "    matched_detections = []\n", "    matched_gt_indices = set()\n", "\n", "    for i, (dist, gt_idx) in enumerate(zip(distances, indices)):\n", "        if dist < config.max_kd_tree_distance and gt_idx not in matched_gt_indices:\n", "            matched_detections.append({\n", "                'detection_idx': i,\n", "                'gt_idx': gt_idx,\n", "                'distance': dist,\n", "                'detection': detections[i],\n", "                'ground_truth': ground_truth['piles'][gt_idx]\n", "            })\n", "            matched_gt_indices.add(gt_idx)\n", "\n", "    matched_detection_indices = {m['detection_idx'] for m in matched_detections}\n", "    false_positives = [detections[i] for i in range(len(detections)) if i not in matched_detection_indices]\n", "\n", "    print(f\"K-d tree matching: {len(matched_detections)} matches, {len(false_positives)} false positives\")\n", "\n", "    return matched_detections, false_positives\n", "\n", "matched_detections, false_positives = match_detections_kd_tree(geo_detections, ground_truth, config)\n", "print(f\"Matched detections: {len(matched_detections)}\")\n", "print(f\"False positives: {len(false_positives)}\")\n"]}, {"cell_type": "markdown", "id": "bb5fbcdc", "metadata": {}, "source": ["## 8. Validation Against IFC Ground Truth"]}, {"cell_type": "markdown", "id": "fad3f0ea", "metadata": {}, "source": ["### 1. Plot Detection Vs Ground Truth"]}, {"cell_type": "code", "execution_count": 64, "id": "a9666a97", "metadata": {}, "outputs": [], "source": ["def plot_detection_vs_ifc_xy(detections, validation_results, ground_truth, sample_size=1000):\n", "    \"\"\"Plot false-positive detection centers vs IFC pile XY coordinates\"\"\"\n", "\n", "    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}\n", "    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]\n", "\n", "    if ground_truth is None or len(false_positives) == 0:\n", "        print(\"Nothing to plot: missing ground truth or no false positives.\")\n", "        return\n", "\n", "    ifc_coords = ground_truth['coordinates']\n", "    false_xy = np.array([[d['x'], d['y']] for d in false_positives])\n", "\n", "    # Sample for clarity\n", "    if len(false_xy) > sample_size:\n", "        false_xy = false_xy[random.sample(range(len(false_xy)), sample_size)]\n", "\n", "    fig, axes = plt.subplots(1, 2, figsize=(16, 8), sharex=True, sharey=True)\n", "\n", "    # Plot false positives\n", "    axes[0].scatter(false_xy[:, 0], false_xy[:, 1], s=5, c='red', alpha=0.5, label='False Positives')\n", "    axes[0].set_title(\"False Positive Detections (XY)\")\n", "    axes[0].set_xlabel(\"X\")\n", "    axes[0].set_ylabel(\"Y\")\n", "    axes[0].legend()\n", "    axes[0].set_aspect('equal', adjustable='box')\n", "\n", "    # Plot IFC coordinates\n", "    axes[1].scatter(ifc_coords[:, 0], ifc_coords[:, 1], s=5, c='blue', alpha=0.5, label='IFC Ground Truth')\n", "    axes[1].set_title(\"IFC Pile Coordinates (XY)\")\n", "    axes[1].set_xlabel(\"X\")\n", "    axes[1].set_ylabel(\"Y\")\n", "    axes[1].legend()\n", "    axes[1].set_aspect('equal', adjustable='box')\n", "\n", "    plt.suptitle(\"False Positives vs IFC Ground Truth (XY Plane)\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "#plot_detection_vs_ifc_xy(geo_detections, validation_results, ground_truth)"]}, {"cell_type": "markdown", "id": "a8a42199", "metadata": {}, "source": ["### 2. Plot Overlay Visualization"]}, {"cell_type": "code", "execution_count": 65, "id": "9af08dc7", "metadata": {}, "outputs": [], "source": ["def plot_overlay_visualization(detections, validation_results, ground_truth, sample_size=1000):\n", "    \"\"\"Plot overlay visualization with IFC and false positives on same axis\"\"\"\n", "    if not ground_truth or not detections:\n", "        print(\"Cannot create overlay plot: missing data\")\n", "        return\n", "    \n", "    # Extract data\n", "    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}\n", "    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]\n", "    true_positives = [d for i, d in enumerate(detections) if i in matched_indices]\n", "    \n", "    # Sample for visualization if needed\n", "    if len(false_positives) > sample_size:\n", "        false_positives = random.sample(false_positives, sample_size)\n", "    \n", "    # Create overlay plot\n", "    plt.figure(figsize=(14, 10))\n", "    \n", "    # Plot IFC ground truth (blue circles)\n", "    ifc_x = [pile['x'] for pile in ground_truth['piles']]\n", "    ifc_y = [pile['y'] for pile in ground_truth['piles']]\n", "    plt.scatter(ifc_x, ifc_y, c='blue', s=100, alpha=0.7, \n", "               marker='o', label=f'IFC Ground Truth ({len(ifc_x)})', edgecolors='darkblue')\n", "    \n", "    # Plot true positives (green squares)\n", "    if true_positives:\n", "        tp_x = [d['x'] for d in true_positives]\n", "        tp_y = [d['y'] for d in true_positives]\n", "        plt.scatter(tp_x, tp_y, c='green', s=80, alpha=0.8, \n", "                   marker='s', label=f'True Positives ({len(true_positives)})', edgecolors='darkgreen')\n", "    \n", "    # Plot false positives (red triangles)\n", "    if false_positives:\n", "        fp_x = [d['x'] for d in false_positives]\n", "        fp_y = [d['y'] for d in false_positives]\n", "        plt.scatter(fp_x, fp_y, c='red', s=60, alpha=0.6, \n", "                   marker='^', label=f'False Positives ({len(fp_x)})', edgecolors='darkred')\n", "    \n", "    plt.xlabel('X Coordinate (m)')\n", "    plt.ylabel('Y Coordinate (m)')\n", "    plt.title('Detection Results Overlay: IFC vs Geometric Detection')\n", "    plt.legend(loc='upper right')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.axis('equal')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "#plot_overlay_visualization(geo_detections, validation_results, ground_truth)"]}, {"cell_type": "markdown", "id": "a3d02ab1", "metadata": {}, "source": ["### 3. Validate Detections"]}, {"cell_type": "code", "execution_count": 66, "id": "615725ce", "metadata": {}, "outputs": [], "source": ["def validate_detections_against_ifc(detections, ground_truth, config):\n", "    \"\"\"Validate geometric detections against IFC ground truth\"\"\"\n", "    \n", "    if not ground_truth or len(detections) == 0:\n", "        print(\"Cannot validate: missing ground truth or detections\")\n", "        return None\n", "    \n", "    pile_coords = ground_truth['coordinates']\n", "    detection_coords = np.array([[d['x'], d['y'], d['z']] for d in detections])\n", "    \n", "    print(f\"\\n=== VALIDATION AGAINST IFC GROUND TRUTH ===\")\n", "    \n", "    print(\"DETECTION COORDS\")\n", "    print(f\"X: {np.min(detection_coords[:, 0]):.2f} - {np.max(detection_coords[:, 0]):.2f}\")\n", "    print(f\"Y: {np.min(detection_coords[:, 1]):.2f} - {np.max(detection_coords[:, 1]):.2f}\")\n", "    print(f\"Z: {np.min(detection_coords[:, 2]):.2f} - {np.max(detection_coords[:, 2]):.2f}\")\n", "\n", "    print(\"\\nIFC COORDS\")\n", "    print(f\"X: {np.min(pile_coords[:, 0]):.2f} - {np.max(pile_coords[:, 0]):.2f}\")\n", "    print(f\"Y: {np.min(pile_coords[:, 1]):.2f} - {np.max(pile_coords[:, 1]):.2f}\")\n", "    print(f\"Z: {np.min(pile_coords[:, 2]):.2f} - {np.max(pile_coords[:, 2]):.2f}\")\n", "\n", "    print(f\"Detections: {len(detections)}\")\n", "    print(f\"IFC piles: {len(pile_coords)}\")\n", "    \n", "    # Find matches within validation radius\n", "    true_positives = 0\n", "    matched_detections = []\n", "    matched_ifc_piles = set()\n", "    \n", "    for i, detection in enumerate(detections):\n", "        det_coord = detection_coords[i]\n", "        \n", "        # Calculate distances to all IFC piles\n", "        #distances = np.linalg.norm(pile_coords - det_coord, axis=1)\n", "        distances = np.linalg.norm(pile_coords[:, :2] - det_coord[:2], axis=1)\n", "\n", "        min_distance = np.min(distances)\n", "        closest_pile_idx = np.argmin(distances)\n", "        \n", "        if min_distance <= config.validation_radius:\n", "            true_positives += 1\n", "            matched_detections.append({\n", "                'detection_idx': i,\n", "                'ifc_pile_idx': closest_pile_idx,\n", "                'distance': min_distance,\n", "                'confidence': detection['confidence']\n", "            })\n", "            matched_ifc_piles.add(closest_pile_idx)\n", "    \n", "    false_positives = len(detections) - true_positives\n", "    false_negatives = len(pile_coords) - len(matched_ifc_piles)\n", "    \n", "    # Calculate metrics\n", "    precision = true_positives / len(detections) if len(detections) > 0 else 0\n", "    recall = true_positives / len(pile_coords) if len(pile_coords) > 0 else 0\n", "    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "    \n", "    print(f\"\\nValidation Results:\")\n", "    print(f\"  True Positives: {true_positives}\")\n", "    print(f\"  False Positives: {false_positives}\")\n", "    print(f\"  False Negatives: {false_negatives}\")\n", "    print(f\"  Precision: {precision:.3f}\")\n", "    print(f\"  Recall: {recall:.3f}\")\n", "    print(f\"  F1-Score: {f1_score:.3f}\")\n", "    \n", "    if matched_detections:\n", "        distances = [m['distance'] for m in matched_detections]\n", "        confidences = [m['confidence'] for m in matched_detections]\n", "        print(f\"  Average match distance: {np.mean(distances):.2f}m\")\n", "        print(f\"  Average confidence of matches: {np.mean(confidences):.3f}\")\n", "    \n", "    return {\n", "        'true_positives': true_positives,\n", "        'false_positives': false_positives,\n", "        'false_negatives': false_negatives,\n", "        'precision': precision,\n", "        'recall': recall,\n", "        'f1_score': f1_score,\n", "        'matched_detections': matched_detections,\n", "        'validation_radius': config.validation_radius\n", "    }"]}, {"cell_type": "code", "execution_count": 67, "id": "c2b97adf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping validation - no ground truth or detections available\n"]}], "source": ["if ground_truth and geo_detections:\n", "    # Choose matching method based on configuration\n", "    if config.use_kd_tree_matching:\n", "        print(\"\\n=== K-D TREE MATCHING ===\")\n", "        matched_detections, false_positives = match_detections_kd_tree(geo_detections, ground_truth, config)\n", "        \n", "        # Create validation results structure for compatibility\n", "        validation_results = {\n", "            'matched_detections': matched_detections,\n", "            'false_positives': len(false_positives),\n", "            'true_positives': len(matched_detections),\n", "            'false_negatives': len(ground_truth['piles']) - len(matched_detections),\n", "            'precision': len(matched_detections) / len(geo_detections) if geo_detections else 0,\n", "            'recall': len(matched_detections) / len(ground_truth['piles']) if ground_truth['piles'] else 0\n", "        }\n", "        validation_results['f1_score'] = (2 * validation_results['precision'] * validation_results['recall'] / \n", "                                         (validation_results['precision'] + validation_results['recall'])) if \\\n", "                                         (validation_results['precision'] + validation_results['recall']) > 0 else 0\n", "    else:\n", "        print(\"\\n=== RADIUS-BASED MATCHING ===\")\n", "        validation_results = validate_detections_against_ifc(geo_detections, ground_truth, config)\n", "    \n", "    # Generate visualizations    \n", "    if config.use_overlay_plots:\n", "        print(\"\\n=== OVERLAY VISUALIZATION ===\")\n", "        plot_overlay_visualization(geo_detections, validation_results, ground_truth)\n", "    else:\n", "        print(\"\\n=== SIDE-BY-SIDE VISUALIZATION ===\")\n", "        plot_detection_vs_ifc_xy(geo_detections, validation_results, ground_truth)\n", "        \n", "else:\n", "    validation_results = None\n", "    print(\"Skipping validation - no ground truth or detections available\")\n", "\n"]}, {"cell_type": "code", "execution_count": 68, "id": "2384758d", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'NoneType' object is not subscriptable", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[68]\u001b[39m\u001b[32m, line 135\u001b[39m\n\u001b[32m    132\u001b[39m     plt.show()\n\u001b[32m    134\u001b[39m \u001b[38;5;66;03m# Usage:\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m135\u001b[39m \u001b[43manalyze_false_positives\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgeo_detections\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalidation_results\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    136\u001b[39m plot_characteristics_comparison(geo_detections, validation_results)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[68]\u001b[39m\u001b[32m, line 4\u001b[39m, in \u001b[36manalyze_false_positives\u001b[39m\u001b[34m(detections, validation_results, config)\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34manalyze_false_positives\u001b[39m(detections, validation_results, config):\n\u001b[32m      2\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Analyze characteristics of false positive detections\"\"\"\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m     matched_indices = {m[\u001b[33m'\u001b[39m\u001b[33mdetection_idx\u001b[39m\u001b[33m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m m \u001b[38;5;129;01min\u001b[39;00m \u001b[43mvalidation_results\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mmatched_detections\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m}\n\u001b[32m      5\u001b[39m     false_positives = [d \u001b[38;5;28;01mfor\u001b[39;00m i, d \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(detections) \u001b[38;5;28;01mif\u001b[39;00m i \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m matched_indices]\n\u001b[32m      6\u001b[39m     true_positives = [d \u001b[38;5;28;01mfor\u001b[39;00m i, d \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(detections) \u001b[38;5;28;01mif\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m matched_indices]\n", "\u001b[31mTypeError\u001b[39m: 'NoneType' object is not subscriptable"]}], "source": ["def analyze_false_positives(detections, validation_results, config):\n", "    \"\"\"Analyze characteristics of false positive detections\"\"\"\n", "    \n", "    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}\n", "    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]\n", "    true_positives = [d for i, d in enumerate(detections) if i in matched_indices]\n", "    \n", "    if not false_positives:\n", "        print(\"No false positives to analyze\")\n", "        return\n", "    \n", "    print(f\"\\n=== FALSE POSITIVE ANALYSIS ===\")\n", "    print(f\"False positives: {len(false_positives)}\")\n", "    print(f\"True positives: {len(true_positives)}\")\n", "    \n", "    # Compare characteristics\n", "    fp_confidences = [d['confidence'] for d in false_positives]\n", "    tp_confidences = [d['confidence'] for d in true_positives]\n", "    \n", "    fp_widths = [d['dimensions']['width'] for d in false_positives]\n", "    tp_widths = [d['dimensions']['width'] for d in true_positives]\n", "    \n", "    fp_heights = [d['dimensions']['height'] for d in false_positives]\n", "    tp_heights = [d['dimensions']['height'] for d in true_positives]\n", "    \n", "    fp_aspects = [d['dimensions']['aspect_ratio'] for d in false_positives]\n", "    tp_aspects = [d['dimensions']['aspect_ratio'] for d in true_positives]\n", "    \n", "    fp_points = [d['point_count'] for d in false_positives]\n", "    tp_points = [d['point_count'] for d in true_positives]\n", "    \n", "    print(f\"\\nCONFIDENCE SCORES:\")\n", "    print(f\"  False positives: {np.mean(fp_confidences):.3f} ± {np.std(fp_confidences):.3f}\")\n", "    print(f\"  True positives:  {np.mean(tp_confidences):.3f} ± {np.std(tp_confidences):.3f}\")\n", "    \n", "    print(f\"\\nWIDTH (m):\")\n", "    print(f\"  False positives: {np.mean(fp_widths):.3f} ± {np.std(fp_widths):.3f}\")\n", "    print(f\"  True positives:  {np.mean(tp_widths):.3f} ± {np.std(tp_widths):.3f}\")\n", "    print(f\"  Expected width: {config.expected_width:.3f}\")\n", "    \n", "    print(f\"\\nHEIGHT (m):\")\n", "    print(f\"  False positives: {np.mean(fp_heights):.3f} ± {np.std(fp_heights):.3f}\")\n", "    print(f\"  True positives:  {np.mean(tp_heights):.3f} ± {np.std(tp_heights):.3f}\")\n", "    print(f\"  Expected height: {config.expected_height:.3f}\")\n", "    \n", "    print(f\"\\nASPECT RATIO:\")\n", "    print(f\"  False positives: {np.mean(fp_aspects):.3f} ± {np.std(fp_aspects):.3f}\")\n", "    print(f\"  True positives:  {np.mean(tp_aspects):.3f} ± {np.std(tp_aspects):.3f}\")\n", "    \n", "    print(f\"\\nPOINT COUNT:\")\n", "    print(f\"  False positives: {np.mean(fp_points):.1f} ± {np.std(fp_points):.1f}\")\n", "    print(f\"  True positives:  {np.mean(tp_points):.1f} ± {np.std(tp_points):.1f}\")\n", "    \n", "    # Identify outliers\n", "    confidence_threshold = np.percentile(tp_confidences, 25)  # Bottom 25% of TP confidences\n", "    width_outliers = [d for d in false_positives if abs(d['dimensions']['width'] - config.expected_width) > 0.05]\n", "    height_outliers = [d for d in false_positives if abs(d['dimensions']['height'] - config.expected_height) > 0.8]\n", "    \n", "    print(f\"\\nOUTLIER ANALYSIS:\")\n", "    print(f\"  Width outliers (>5cm from expected): {len(width_outliers)} / {len(false_positives)}\")\n", "    print(f\"  Height outliers (>80cm from expected): {len(height_outliers)} / {len(false_positives)}\")\n", "    \n", "    # Recommendations\n", "    print(f\"\\nRECOMMENDATIONS:\")\n", "    if np.mean(fp_confidences) < np.mean(tp_confidences) - 0.05:\n", "        print(f\"  ✓ Raise confidence threshold to {np.percentile(tp_confidences, 25):.3f}\")\n", "    \n", "    if len(width_outliers) > len(false_positives) * 0.3:\n", "        print(f\"  ✓ Tighten width tolerance to {np.std(tp_widths) * 2:.3f}\")\n", "    \n", "    if len(height_outliers) > len(false_positives) * 0.3:\n", "        print(f\"  ✓ Tighten height tolerance to {np.std(tp_heights) * 2:.3f}\")\n", "    \n", "    # Spatial distribution analysis\n", "    fp_x = [d['x'] for d in false_positives]\n", "    fp_y = [d['y'] for d in false_positives]\n", "    \n", "    # Check if false positives cluster in specific areas\n", "    from scipy.spatial.distance import pdist\n", "    if len(false_positives) > 5:\n", "        fp_coords = np.array([[d['x'], d['y']] for d in false_positives])\n", "        distances = pdist(fp_coords)\n", "        avg_distance = np.mean(distances)\n", "        \n", "        print(f\"\\nSPATIAL CLUSTERING:\")\n", "        print(f\"  Average distance between FPs: {avg_distance:.2f}m\")\n", "        if avg_distance < 5.0:\n", "            print(f\"  ⚠️  False positives are spatially clustered - check for systematic errors\")\n", "\n", "def plot_characteristics_comparison(detections, validation_results):\n", "    \"\"\"Plot comparison of true positive vs false positive characteristics\"\"\"\n", "    \n", "    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}\n", "    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]\n", "    true_positives = [d for i, d in enumerate(detections) if i in matched_indices]\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "    \n", "    # Confidence scores\n", "    axes[0,0].hist([d['confidence'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)\n", "    axes[0,0].hist([d['confidence'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)\n", "    axes[0,0].set_xlabel('Confidence Score')\n", "    axes[0,0].set_ylabel('Count')\n", "    axes[0,0].set_title('Confidence Score Distribution')\n", "    axes[0,0].legend()\n", "    \n", "    # Width distribution\n", "    axes[0,1].hist([d['dimensions']['width'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)\n", "    axes[0,1].hist([d['dimensions']['width'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)\n", "    axes[0,1].set_xlabel('Width (m)')\n", "    axes[0,1].set_ylabel('Count')\n", "    axes[0,1].set_title('Width Distribution')\n", "    axes[0,1].legend()\n", "    \n", "    # Height distribution\n", "    axes[1,0].hist([d['dimensions']['height'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)\n", "    axes[1,0].hist([d['dimensions']['height'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)\n", "    axes[1,0].set_xlabel('Height (m)')\n", "    axes[1,0].set_ylabel('Count')\n", "    axes[1,0].set_title('Height Distribution')\n", "    axes[1,0].legend()\n", "    \n", "    # Aspect ratio distribution\n", "    axes[1,1].hist([d['dimensions']['aspect_ratio'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)\n", "    axes[1,1].hist([d['dimensions']['aspect_ratio'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)\n", "    axes[1,1].set_xlabel('Aspect Ratio')\n", "    axes[1,1].set_ylabel('Count')\n", "    axes[1,1].set_title('Aspect Ratio Distribution')\n", "    axes[1,1].legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Usage:\n", "analyze_false_positives(geo_detections, validation_results, config)\n", "plot_characteristics_comparison(geo_detections, validation_results)"]}, {"cell_type": "markdown", "id": "50d28897", "metadata": {}, "source": ["## 9. Save Results"]}, {"cell_type": "code", "execution_count": null, "id": "save_results", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Saved 14403 detections to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/pile_detection/geometric_csf/geometric_detections_csf.csv\n", "\n", "Detection Summary:\n", "         confidence         width        height  aspect_ratio\n", "count  14403.000000  14403.000000  14403.000000  14403.000000\n", "mean       0.864329      0.062482      2.238121      1.172865\n", "std        0.078916      0.000499      0.277330      0.011074\n", "min        0.660705      0.059754      0.733237      1.125250\n", "25%        0.801812      0.062200      2.152106      1.166062\n", "50%        0.845135      0.062594      2.351878      1.172111\n", "75%        0.917212      0.062862      2.429086      1.179086\n", "max        1.073211      0.063223      2.499375      1.239043\n", "\n", "Saved metrics to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/pile_detection/geometric_csf/geometric_metrics_csf.json\n"]}], "source": ["if geo_detections:\n", "    detections_df = pd.DataFrame([\n", "        {\n", "            'x': d['x'],\n", "            'y': d['y'],\n", "            'z': d['z'],\n", "            'confidence': d['confidence'],\n", "            'point_count': d['point_count'],\n", "            'width': d['dimensions']['width'],\n", "            'depth': d['dimensions']['depth'],\n", "            'height': d['dimensions']['height'],\n", "            'aspect_ratio': d['dimensions']['aspect_ratio'],\n", "            'height_score': d['scores']['height'],\n", "            'width_score': d['scores']['width'],\n", "            'aspect_score': d['scores']['aspect'],\n", "            'distribution_score': d['scores']['distribution']\n", "        }\n", "        for d in geo_detections\n", "    ])\n", "    \n", "    detections_file = config.output_dir / f\"geometric_detections_{config.ground_method}.csv\"\n", "    detections_df.to_csv(detections_file, index=False)\n", "    print(f\"\\nSaved {len(geo_detections)} detections to: {detections_file}\")\n", "    \n", "    # Display summary statistics\n", "    print(f\"\\nDetection Summary:\")\n", "    print(detections_df[['confidence', 'width', 'height', 'aspect_ratio']].describe())\n", "\n", "# Save comprehensive metrics\n", "metrics = {\n", "    'timestamp': datetime.now().isoformat(),\n", "    'site_name': config.site_name,\n", "    'ground_method': config.ground_method,\n", "    'processing_time_seconds': processing_time,\n", "    'input_points': len(points),\n", "    'detected_piles': len(geo_detections),\n", "    'expected_piles': config.expected_pile_count,\n", "    'detection_ratio': len(geo_detections) / config.expected_pile_count,\n", "    'confidence_threshold': config.confidence_threshold,\n", "    'approach': 'geometric_rule_based'\n", "}\n", "\n", "if validation_results:\n", "    metrics.update({\n", "        'validation_precision': validation_results['precision'],\n", "        'validation_recall': validation_results['recall'],\n", "        'validation_f1_score': validation_results['f1_score'],\n", "        'true_positives': validation_results['true_positives'],\n", "        'false_positives': validation_results['false_positives'],\n", "        'false_negatives': validation_results['false_negatives']\n", "    })\n", "\n", "metrics_file = config.output_dir / f\"geometric_metrics_{config.ground_method}.json\"\n", "with open(metrics_file, 'w') as f:\n", "    json.dump(metrics, f, indent=2)\n", "\n", "print(f\"\\nSaved metrics to: {metrics_file}\")"]}, {"cell_type": "markdown", "id": "ce680e2b", "metadata": {}, "source": ["## Implemented Improvements Summary\n", "\n", "---\n", "\n", "### Elevation Banding (Z-layer Filtering)\n", "- **Z-range applied:** `1.0m – 3.5m` above ground level  \n", "- **Purpose:** Removes tall or noisy structures outside expected pile height  \n", "- **Effect:** Reduces false clusters and boosts detection precision\n", "\n", "---\n", "\n", "### Regular Grid Spacing Constraint\n", "- **Enabled:** `False`  \n", "- **Expected spacing:** `2.5m`  \n", "- **Grid tolerance:** `±1.5m`  \n", "- **Purpose:** Filters out detections that do not conform to expected layout geometry\n", "\n", "---\n", "\n", "### K-D Tree Nearest Neighbor Matching\n", "- **Enabled:** `True`  \n", "- **Max match distance:** `5.0m`  \n", "- **Purpose:** Matches detections to IFC ground truth while handling dense/sparse regions  \n", "- **Benefit:** Avoids over/under-counting near pile clusters or gaps\n", "\n", "---\n", "\n", "### Confidence Score Heatmap\n", "- **Enabled:** `True`  \n", "- **Function:** Visualizes spatial confidence levels across the site  \n", "- **Use Case:** Identifies hot zones, detection bias, or weak scoring areas\n", "\n", "---\n", "\n", "### Overlay Visualization\n", "- **Enabled:** `True`  \n", "- **Method:** Detections overlaid on orthophoto/IFC with transparency\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}