{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC-Based Pile Detection - Data Preparation\n", "\n", "This notebook prepares training data for pile detection by extracting point cloud patches around pile locations from IFC metadata.\n", "\n", "## What we will do:\n", "1. Load aligned point cloud data\n", "2. Load IFC metadata containing pile locations\n", "3. Extract point cloud patches around each pile\n", "4. Create positive samples (pile locations) and negative samples (non-pile locations)\n", "5. Save the prepared data for analysis\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup Environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["# Import required libraries\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "from datetime import datetime\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Setup notebook path and import shared utilities\n", "current_path = Path.cwd()\n", "while current_path.name != \"notebooks\":\n", "    if current_path.parent == current_path:\n", "        raise RuntimeError(\"Could not find 'notebooks' directory\")\n", "    current_path = current_path.parent\n", "\n", "notebooks_root = current_path\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "from shared.config import get_processed_data_path, find_latest_file\n", "print(f\"Notebooks root: {notebooks_root}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration:\n", "  Site: trino_enel\n", "  Ground method: ransac_pmf\n", "  Patch radius: 5.0 meters\n", "  Min points per patch: 100\n", "  Max positive samples: 5000\n", "  Max negative samples: 2500\n"]}], "source": ["# Configuration - These are the main settings for our analysis\n", "site_name = \"trino_enel\"  # Name of the construction site\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method used\n", "patch_radius = 5.0  # How far around each pile to extract points (meters)\n", "min_points_per_patch = 100  # Minimum points needed to consider a patch valid\n", "max_positive_samples = 5000  # Maximum number of pile samples to extract\n", "max_negative_samples = 2500  # Maximum number of non-pile samples to extract\n", "\n", "print(\"Configuration:\")\n", "print(f\"  Site: {site_name}\")\n", "print(f\"  Ground method: {ground_method}\")\n", "print(f\"  Patch radius: {patch_radius} meters\")\n", "print(f\"  Min points per patch: {min_points_per_patch}\")\n", "print(f\"  Max positive samples: {max_positive_samples}\")\n", "print(f\"  Max negative samples: {max_negative_samples}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud data...\n", "Loading from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "Successfully loaded 517,002 points\n"]}], "source": ["# Load the aligned point cloud data\n", "print(\"Loading point cloud data...\")\n", "\n", "# First try to find the filtered version\n", "alignment_path = get_processed_data_path(site_name, \"gcp_alignment_z_corrected\") / ground_method / \"filtered\"\n", "point_cloud_file = find_latest_file(alignment_path, \"*_gcp_aligned_z_corrected_filtered.ply\")\n", "\n", "# If filtered version doesn't exist, use the non-filtered version\n", "if not point_cloud_file.exists():\n", "    print(\"Filtered version not found, using non-filtered version...\")\n", "    alignment_path = get_processed_data_path(site_name, \"gcp_alignment_z_corrected\") / ground_method\n", "    point_cloud_file = find_latest_file(alignment_path, \"*_gcp_aligned_z_corrected.ply\")\n", "\n", "print(f\"Loading from: {point_cloud_file}\")\n", "\n", "# Load the point cloud using Open3D\n", "point_cloud = o3d.io.read_point_cloud(str(point_cloud_file))\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Successfully loaded {len(points):,} points\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud information:\n", "  Number of points: 517,002\n", "  Data shape: (517002, 3)\n", "  Data type: float64\n", "\n", "Coordinate ranges:\n", "  X: 435220.30 to 436796.35 (range: 1576.05 meters)\n", "  Y: 5010812.68 to 5012553.07 (range: 1740.39 meters)\n", "  Z: 152.43 to 182.42 (range: 29.98 meters)\n", "\n", "First 5 points:\n", "  Point 1: X=435344.09, Y=5011988.43, Z=157.60\n", "  Point 2: X=436578.57, Y=5012091.24, Z=154.69\n", "  Point 3: X=436464.55, Y=5011306.84, Z=155.10\n", "  Point 4: X=436146.57, Y=5010913.93, Z=154.13\n", "  Point 5: X=435563.17, Y=5011417.41, Z=158.16\n"]}], "source": ["# Examine the point cloud data\n", "print(\"Point cloud information:\")\n", "print(f\"  Number of points: {len(points):,}\")\n", "print(f\"  Data shape: {points.shape}\")\n", "print(f\"  Data type: {points.dtype}\")\n", "\n", "print(\"\\nCoordinate ranges:\")\n", "for i, axis in enumerate(['X', 'Y', 'Z']):\n", "    min_val = points[:, i].min()\n", "    max_val = points[:, i].max()\n", "    range_val = max_val - min_val\n", "    print(f\"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)\")\n", "\n", "print(\"\\nFirst 5 points:\")\n", "for i in range(min(5, len(points))):\n", "    print(f\"  Point {i+1}: X={points[i,0]:.2f}, Y={points[i,1]:.2f}, Z={points[i,2]:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Load IFC Metadata"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata...\n", "Loading from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "Loaded 14,460 IFC elements\n", "\n", "Available columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y', 'Z', 'GeometryExtracted', 'Longitude', 'Latitude']\n", "\n", "Element types in data:\n", "Type\n", "IfcColumn    14460\n", "Name: count, dtype: int64\n"]}], "source": ["# Load IFC metadata containing pile information\n", "print(\"Loading IFC metadata...\")\n", "\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "\n", "print(f\"Loading from: {metadata_file}\")\n", "ifc_data = pd.read_csv(metadata_file)\n", "print(f\"Loaded {len(ifc_data):,} IFC elements\")\n", "\n", "# Show what columns are available\n", "print(f\"\\nAvailable columns: {list(ifc_data.columns)}\")\n", "print(f\"\\nElement types in data:\")\n", "print(ifc_data['Type'].value_counts().head())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting pile elements...\n", "Found 14,460 pile elements\n", "Valid pile coordinates: 14,460\n", "\n", "Pile coordinate ranges:\n", "  X: 435267.20 to 436719.95 (range: 1452.75 meters)\n", "  Y: 5010900.71 to 5012462.41 (range: 1561.70 meters)\n", "  Z: 154.99 to 159.52 (range: 4.53 meters)\n", "\n", "Sample pile data:\n", "                                         Name       Type           X  \\\n", "0  TRPL_Tracker Pile:TRPL_Tracker Pile:952577  IfcColumn  435751.684   \n", "1  TRPL_Tracker Pile:TRPL_Tracker Pile:952578  IfcColumn  435751.684   \n", "2  TRPL_Tracker Pile:TRPL_Tracker Pile:952579  IfcColumn  435751.684   \n", "3  TRPL_Tracker Pile:TRPL_Tracker Pile:952580  IfcColumn  435751.684   \n", "4  TRPL_Tracker Pile:TRPL_Tracker Pile:952581  IfcColumn  435751.684   \n", "\n", "             Y        Z  \n", "0  5012179.151  158.688  \n", "1  5012187.444  158.688  \n", "2  5012195.736  158.688  \n", "3  5012170.859  158.688  \n", "4  5012204.029  158.688  \n"]}], "source": ["# Filter for pile elements using name-based search\n", "print(\"Extracting pile elements...\")\n", "\n", "# Find elements with '<PERSON><PERSON>' in their name\n", "pile_mask = ifc_data['Name'].str.contains('Pile', case=False, na=False)\n", "pile_data = ifc_data[pile_mask].copy()\n", "print(f\"Found {len(pile_data):,} pile elements\")\n", "\n", "# Extract coordinates and remove any rows with missing coordinates\n", "coordinate_columns = ['X', 'Y', 'Z']\n", "pile_coordinates = pile_data[coordinate_columns].dropna().values\n", "print(f\"Valid pile coordinates: {len(pile_coordinates):,}\")\n", "\n", "# Show coordinate ranges\n", "print(\"\\nPile coordinate ranges:\")\n", "for i, axis in enumerate(coordinate_columns):\n", "    min_val = pile_coordinates[:, i].min()\n", "    max_val = pile_coordinates[:, i].max()\n", "    range_val = max_val - min_val\n", "    print(f\"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)\")\n", "\n", "# Show sample pile data\n", "print(\"\\nSample pile data:\")\n", "print(pile_data[['Name', 'Type', 'X', 'Y', 'Z']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Create Spatial Index for Fast Searching"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating spatial index for point cloud...\n", "Spatial index created for 517,002 points\n", "\n", "Test query around first pile location:\n", "  Pile location: X=435751.68, Y=5012179.15, Z=158.69\n", "  Points within 5.0m radius: 0\n"]}], "source": ["# Create a spatial index (KD-Tree) for fast point searching\n", "print(\"Creating spatial index for point cloud...\")\n", "\n", "# Build KD-Tree for efficient nearest neighbor searches\n", "point_tree = cKDTree(points)\n", "print(f\"Spatial index created for {len(points):,} points\")\n", "\n", "# Test the spatial index with a sample query\n", "if len(pile_coordinates) > 0:\n", "    test_pile = pile_coordinates[0]\n", "    test_indices = point_tree.query_ball_point(test_pile, patch_radius)\n", "    print(f\"\\nTest query around first pile location:\")\n", "    print(f\"  Pile location: X={test_pile[0]:.2f}, Y={test_pile[1]:.2f}, Z={test_pile[2]:.2f}\")\n", "    print(f\"  Points within {patch_radius}m radius: {len(test_indices)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Extract Positive Samples (<PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Patch extraction function defined\n"]}], "source": ["# Function to extract points around a location\n", "def extract_patch_around_location(center_point, radius, tree, all_points, min_points=100):\n", "    \"\"\"\n", "    Extract points within a radius around a center point.\n", "    \n", "    Args:\n", "        center_point: [x, y, z] coordinates of center\n", "        radius: search radius in meters\n", "        tree: KD-Tree for spatial searching\n", "        all_points: array of all point coordinates\n", "        min_points: minimum points required for valid patch\n", "    \n", "    Returns:\n", "        patch_points: array of points in patch, or None if insufficient points\n", "    \"\"\"\n", "    # Find all points within radius\n", "    point_indices = tree.query_ball_point(center_point, radius)\n", "    \n", "    # Check if we have enough points\n", "    if len(point_indices) < min_points:\n", "        return None\n", "    \n", "    # Extract the actual point coordinates\n", "    patch_points = all_points[point_indices]\n", "    \n", "    return patch_points\n", "\n", "print(\"Patch extraction function defined\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting positive samples around pile locations...\n", "  Extracted 100 positive patches...\n", "  Extracted 200 positive patches...\n", "  Extracted 300 positive patches...\n", "  Extracted 400 positive patches...\n", "  Extracted 500 positive patches...\n", "  Extracted 600 positive patches...\n", "  Extracted 700 positive patches...\n", "  Extracted 800 positive patches...\n", "  Extracted 900 positive patches...\n", "  Extracted 1000 positive patches...\n", "  Extracted 1100 positive patches...\n", "  Extracted 1200 positive patches...\n", "  Extracted 1300 positive patches...\n", "  Extracted 1400 positive patches...\n", "  Extracted 1500 positive patches...\n", "  Extracted 1600 positive patches...\n", "  Extracted 1700 positive patches...\n", "  Extracted 1800 positive patches...\n", "  Extracted 1900 positive patches...\n", "  Extracted 2000 positive patches...\n", "  Extracted 2100 positive patches...\n", "  Extracted 2200 positive patches...\n", "  Extracted 2300 positive patches...\n", "  Extracted 2400 positive patches...\n", "  Extracted 2500 positive patches...\n", "  Extracted 2600 positive patches...\n", "  Extracted 2700 positive patches...\n", "  Extracted 2800 positive patches...\n", "  Extracted 2900 positive patches...\n", "  Extracted 3000 positive patches...\n", "  Extracted 3100 positive patches...\n", "  Extracted 3200 positive patches...\n", "  Extracted 3300 positive patches...\n", "  Extracted 3400 positive patches...\n", "  Extracted 3500 positive patches...\n", "  Extracted 3600 positive patches...\n", "  Extracted 3700 positive patches...\n", "  Extracted 3800 positive patches...\n", "  Extracted 3900 positive patches...\n", "  Extracted 4000 positive patches...\n", "  Extracted 4100 positive patches...\n", "  Extracted 4200 positive patches...\n", "  Extracted 4300 positive patches...\n", "  Extracted 4400 positive patches...\n", "  Extracted 4500 positive patches...\n", "  Extracted 4600 positive patches...\n", "  Extracted 4700 positive patches...\n", "  Extracted 4800 positive patches...\n", "  Extracted 4900 positive patches...\n", "  Extracted 5000 positive patches...\n", "Reached maximum positive samples (5000)\n", "\n", "Positive sample extraction complete:\n", "  Successful extractions: 5000\n", "  Failed extractions: 6229\n", "  Success rate: 44.5%\n"]}], "source": ["# Extract positive samples (patches around pile locations)\n", "print(\"Extracting positive samples around pile locations...\")\n", "\n", "positive_patches = []\n", "positive_info = []\n", "successful_extractions = 0\n", "failed_extractions = 0\n", "\n", "# Try different radii if the default doesn't work\n", "radii_to_try = [patch_radius, patch_radius * 1.5, patch_radius * 2.0]\n", "\n", "for i, pile_location in enumerate(pile_coordinates):\n", "    if successful_extractions >= max_positive_samples:\n", "        print(f\"Reached maximum positive samples ({max_positive_samples})\")\n", "        break\n", "    \n", "    patch_extracted = False\n", "    \n", "    # Try different radii to get enough points\n", "    for radius in radii_to_try:\n", "        patch_points = extract_patch_around_location(\n", "            pile_location, radius, point_tree, points, min_points_per_patch\n", "        )\n", "        \n", "        if patch_points is not None:\n", "            positive_patches.append(patch_points)\n", "            positive_info.append({\n", "                'pile_index': i,\n", "                'pile_location': pile_location.tolist(),\n", "                'radius_used': radius,\n", "                'num_points': len(patch_points)\n", "            })\n", "            successful_extractions += 1\n", "            patch_extracted = True\n", "            \n", "            if successful_extractions % 100 == 0:\n", "                print(f\"  Extracted {successful_extractions} positive patches...\")\n", "            break\n", "    \n", "    if not patch_extracted:\n", "        failed_extractions += 1\n", "\n", "print(f\"\\nPositive sample extraction complete:\")\n", "print(f\"  Successful extractions: {successful_extractions}\")\n", "print(f\"  Failed extractions: {failed_extractions}\")\n", "print(f\"  Success rate: {successful_extractions/(successful_extractions+failed_extractions)*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Extract Negative Samples (Non-<PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting negative samples from non-pile areas...\n", "Point cloud bounds for negative sampling:\n", "  X: 435220.30 to 436796.35\n", "  Y: 5010812.68 to 5012553.07\n", "  Z: 152.43 to 182.42\n", "Minimum distance from piles: 15.0 meters\n"]}], "source": ["# Extract negative samples (patches from areas without piles)\n", "print(\"Extracting negative samples from non-pile areas...\")\n", "\n", "# Calculate point cloud bounds for random sampling\n", "x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "z_min, z_max = points[:, 2].min(), points[:, 2].max()\n", "\n", "print(f\"Point cloud bounds for negative sampling:\")\n", "print(f\"  X: {x_min:.2f} to {x_max:.2f}\")\n", "print(f\"  Y: {y_min:.2f} to {y_max:.2f}\")\n", "print(f\"  Z: {z_min:.2f} to {z_max:.2f}\")\n", "\n", "# Minimum distance from pile locations for negative samples\n", "min_distance_from_piles = patch_radius * 3.0\n", "print(f\"Minimum distance from piles: {min_distance_from_piles:.1f} meters\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating negative samples (target: 2500)...\n", "\n", "Negative sample extraction complete:\n", "  Generated samples: 1\n", "  Attempts made: 25000\n", "  Success rate: 0.0%\n"]}], "source": ["# Generate negative samples\n", "negative_patches = []\n", "negative_info = []\n", "negative_attempts = 0\n", "max_attempts = max_negative_samples * 10  # Try up to 10x the target number\n", "\n", "print(f\"Generating negative samples (target: {max_negative_samples})...\")\n", "\n", "while len(negative_patches) < max_negative_samples and negative_attempts < max_attempts:\n", "    negative_attempts += 1\n", "    \n", "    # Generate random location within point cloud bounds\n", "    random_x = np.random.uniform(x_min, x_max)\n", "    random_y = np.random.uniform(y_min, y_max)\n", "    random_z = np.random.uniform(z_min, z_max)\n", "    random_location = np.array([random_x, random_y, random_z])\n", "    \n", "    # Check if location is far enough from all pile locations\n", "    distances_to_piles = np.linalg.norm(pile_coordinates - random_location, axis=1)\n", "    min_distance = distances_to_piles.min()\n", "    \n", "    if min_distance >= min_distance_from_piles:\n", "        # Try to extract patch at this location\n", "        patch_points = extract_patch_around_location(\n", "            random_location, patch_radius, point_tree, points, min_points_per_patch\n", "        )\n", "        \n", "        if patch_points is not None:\n", "            negative_patches.append(patch_points)\n", "            negative_info.append({\n", "                'location': random_location.tolist(),\n", "                'radius_used': patch_radius,\n", "                'num_points': len(patch_points),\n", "                'min_distance_to_pile': min_distance\n", "            })\n", "            \n", "            if len(negative_patches) % 100 == 0:\n", "                print(f\"  Generated {len(negative_patches)} negative patches...\")\n", "\n", "print(f\"\\nNegative sample extraction complete:\")\n", "print(f\"  Generated samples: {len(negative_patches)}\")\n", "print(f\"  Attempts made: {negative_attempts}\")\n", "print(f\"  Success rate: {len(negative_patches)/negative_attempts*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Save Prepared Data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving prepared data to: ../data/output_runs/pile_detection_20250721_192209\n", "Output directory saved to output_dir.txt\n"]}], "source": ["# Create output directory with timestamp\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "output_dir = Path(f\"../data/output_runs/pile_detection_{timestamp}\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Saving prepared data to: {output_dir}\")\n", "\n", "# Save the output directory path for the next notebook\n", "with open(\"output_dir.txt\", \"w\") as f:\n", "    f.write(str(output_dir))\n", "\n", "print(f\"Output directory saved to output_dir.txt\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to: ../data/output_runs/pile_detection_20250721_192209/extracted_patches.json\n", "\n", "Summary of saved data:\n", "  Positive patches: 5000\n", "  Negative patches: 1\n", "  Total pile coordinates: 14460\n", "  File size: 59.7 MB\n"]}], "source": ["# Prepare data for saving (convert numpy arrays to lists for JSON)\n", "save_data = {\n", "    'positive_patches': [patch.tolist() for patch in positive_patches],\n", "    'negative_patches': [patch.tolist() for patch in negative_patches],\n", "    'positive_info': positive_info,\n", "    'negative_info': negative_info,\n", "    'pile_coords': pile_coordinates.tolist(),\n", "    'parameters': {\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'patch_radius': patch_radius,\n", "        'min_points_per_patch': min_points_per_patch,\n", "        'max_positive_samples': max_positive_samples,\n", "        'max_negative_samples': max_negative_samples,\n", "        'timestamp': timestamp\n", "    }\n", "}\n", "\n", "# Save to JSON file\n", "output_file = output_dir / \"extracted_patches.json\"\n", "with open(output_file, 'w') as f:\n", "    json.dump(save_data, f, indent=2)\n", "\n", "print(f\"Data saved to: {output_file}\")\n", "print(f\"\\nSummary of saved data:\")\n", "print(f\"  Positive patches: {len(positive_patches)}\")\n", "print(f\"  Negative patches: {len(negative_patches)}\")\n", "print(f\"  Total pile coordinates: {len(pile_coordinates)}\")\n", "print(f\"  File size: {output_file.stat().st_size / 1024 / 1024:.1f} MB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "Data preparation is now complete! Here's what we accomplished:\n", "\n", "1. **Loaded point cloud data** with spatial coordinates\n", "2. **Extracted pile locations** from IFC metadata\n", "3. **Created positive samples** by extracting point patches around pile locations\n", "4. **Created negative samples** by extracting patches from non-pile areas\n", "5. **Saved all data** for analysis in the next notebook\n", "\n", "The prepared data is now ready for feature analysis and rule-based classification in the next notebook: `02_pile_detection_analysis_validation.ipynb`"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}