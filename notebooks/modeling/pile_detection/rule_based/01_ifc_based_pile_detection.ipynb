import sys
from pathlib import Path

# Setup notebook root path
current_path = Path(__file__).resolve() if '__file__' in globals() else Path.cwd()

while current_path.name != "notebooks":
    if current_path.parent == current_path:
        raise RuntimeError("Could not find 'notebooks' directory in path hierarchy.")
    current_path = current_path.parent

notebooks_root = current_path
print(f"Notebooks root: {notebooks_root}")

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Import required libraries
import sys
from pathlib import Path
import numpy as np
import pandas as pd
import open3d as o3d
from scipy.spatial import cKDTree
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')


# Import shared utilities
from shared.config import get_processed_data_path, find_latest_file

# Configuration parameters
site_name = "trino_enel"
ground_method = "ransac_pmf"
patch_size = 5.0  # meters radius around each pile
min_points_per_patch = 50
save_results = True

print("Configuration:")
print(f"  Site: {site_name}")
print(f"  Ground segmentation method: {ground_method}")
print(f"  Patch size: {patch_size}m radius")
print(f"  Minimum points per patch: {min_points_per_patch}")
print(f"  Save results: {save_results}")

print("Loading IFC metadata...")

ifc_metadata_path = get_processed_data_path(site_name, "ifc_metadata")
metadata_file = find_latest_file(ifc_metadata_path, "*enhanced_metadata.csv")

print(f"Loading metadata from: {metadata_file}")
ifc_df = pd.read_csv(metadata_file)
print(f"Loaded {len(ifc_df):,} IFC elements")

# Display basic metadata information
print(f"Available columns: {list(ifc_df.columns)}")
print(f"Element types: {ifc_df['Type'].value_counts().head()}")

# Filter for pile elements
pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)
pile_df = ifc_df[pile_mask].copy()
print(f"Found {len(pile_df):,} pile elements")

# Extract coordinates
coord_cols = ['X', 'Y', 'Z']
pile_coords = pile_df[coord_cols].dropna().values
print(f"Valid coordinates: {len(pile_coords):,}")

for i, col in enumerate(coord_cols):
    min_val = pile_coords[:, i].min()
    max_val = pile_coords[:, i].max()
    print(f"{col}: {min_val:.2f} to {max_val:.2f}")

print("\nSample pile data:")
print(pile_df[['Name', 'Type', 'X', 'Y', 'Z']].head())

# Load aligned point cloud data
print("Loading aligned point cloud data...")

# Try to load filtered version first
alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method / "filtered"
aligned_file = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected_filtered.ply")

# Fallback to non-filtered version if filtered not available
if not aligned_file.exists():
    print("Filtered version not found, using non-filtered version...")
    alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method
    aligned_file = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected.ply")

print(f"Loading point cloud from: {aligned_file}")
pcd = o3d.io.read_point_cloud(str(aligned_file))
points = np.asarray(pcd.points)

print(f"Successfully loaded {len(points):,} points")
print(f"Point cloud bounds:")
print(f"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}")
print(f"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}")
print(f"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}")

def simple_patch_quality(patch_points, min_z_std=1.0):
    if len(patch_points) == 0:
        return 0.0
    if patch_points.shape[1] > 2:
        z_std = np.std(patch_points[:, 2])
        return 1.0 if z_std < min_z_std else 0.0
    return 0.5

def extract_pile_patches(points, pile_coords, patch_radius=[5.0, 7.5, 10.0, 12.5], min_points=20, max_points=300):
    """
    Extract point cloud patches around pile locations.
    """
    tree = cKDTree(points[:, :2])
    patches, patch_info = [], []

    for i, pile_center in enumerate(pile_coords):
        for radius in patch_radius:
            indices = tree.query_ball_point(pile_center[:2], radius)
            if len(indices) >= min_points:
                patch_points = points[indices]
                quality = simple_patch_quality(patch_points)

                if quality == 0.0:
                    print(f"Pile {i}: Z variation too high, skipped at radius {radius}")
                    continue

                # Limit number of points
                if len(indices) > max_points:
                    selected_idx = np.random.choice(indices, max_points, replace=False)
                    patch_points = points[selected_idx]
                else:
                    patch_points = points[indices]

                centered_points = patch_points - pile_center

                patches.append(centered_points)
                patch_info.append({
                    'pile_id': i,
                    'center': pile_center,
                    'num_points': len(patch_points),
                    'quality_score': quality,
                    'label': 1
                })

                print(f"Pile {i}: radius {radius}, points {len(patch_points)}, quality {quality:.2f}")
                break
        else:
            print(f"Pile {i}: skipped, not enough points")

    return patches, patch_info


print("Extracting pile patches...")
pile_patches, pile_patch_info = extract_pile_patches(
    points=points,
    pile_coords=pile_coords,
    patch_radius=[5.0, 7.5, 10.0, 12.5],
    min_points=20,
    max_points=200,
)

print(f"Extracted {len(pile_patches)} pile patches")
if pile_patch_info:
    avg_points = np.mean([info['num_points'] for info in pile_patch_info])
    avg_quality = np.mean([info['quality_score'] for info in pile_patch_info])
    print(f"Average points per patch: {avg_points:.0f}")
    print(f"Average quality score: {avg_quality:.3f}")


def filter_patches_by_quality(patches, patch_info, min_quality=0.7, max_patches=None):
    """Filter patches by quality score"""
    good_indices = [
        i for i, info in enumerate(patch_info) 
        if info['quality_score'] >= min_quality
    ]
    
    if max_patches and len(good_indices) > max_patches:
        quality_scores = [patch_info[i]['quality_score'] for i in good_indices]
        sorted_indices = sorted(
            range(len(good_indices)), 
            key=lambda x: quality_scores[x], 
            reverse=True
        )
        good_indices = [good_indices[i] for i in sorted_indices[:max_patches]]
    
    filtered_patches = [patches[i] for i in good_indices]
    filtered_info = [patch_info[i] for i in good_indices]
    
    return filtered_patches, filtered_info

# Filter for high quality patches
high_quality_patches, high_quality_info = filter_patches_by_quality(
    pile_patches, pile_patch_info,
    min_quality=0.8,
    max_patches=5000
)

print(f"High quality patches: {len(high_quality_patches)}")
if high_quality_info:
    avg_quality = np.mean([info['quality_score'] for info in high_quality_info])
    print(f"Average quality: {avg_quality:.3f}")

def generate_negative_samples(points, pile_coords, patch_radius=8.0, min_points=30, num_samples=None):
    """Generate negative samples away from pile locations"""
    if num_samples is None:
        num_samples = len(pile_coords)
    
    # Create exclusion zones around piles
    pile_tree = cKDTree(pile_coords[:, :2])
    exclusion_radius = patch_radius * 2
    
    # Sample random locations
    point_bounds = {
        'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),
        'y_min': points[:, 1].min(), 'y_max': points[:, 1].max()
    }
    
    tree = cKDTree(points[:, :2])
    negative_patches = []
    negative_patch_info = []
    
    attempts = 0
    max_attempts = num_samples * 10
    
    while len(negative_patches) < num_samples and attempts < max_attempts:
        center = np.array([
            np.random.uniform(point_bounds['x_min'], point_bounds['x_max']),
            np.random.uniform(point_bounds['y_min'], point_bounds['y_max'])
        ])
        
        # Check distance to nearest pile
        distances_to_piles = pile_tree.query(center, k=1)[0]
        if distances_to_piles < exclusion_radius:
            attempts += 1
            continue
        
        # Extract patch
        indices = tree.query_ball_point(center, patch_radius)
        
        if len(indices) >= min_points:
            patch_points = points[indices]
            center_3d = np.array([center[0], center[1], np.median(patch_points[:, 2])])
            centered_points = patch_points - center_3d
            
            negative_patches.append(centered_points)
            negative_patch_info.append({
                'patch_id': len(negative_patches) - 1,
                'center': center_3d,
                'num_points': len(indices),
                'label': 0
            })
        
        attempts += 1
    
    return negative_patches, negative_patch_info

print("Generating negative samples...")
negative_patches, negative_patch_info = generate_negative_samples(
    points, pile_coords, patch_size, min_points_per_patch, len(high_quality_patches)
)

print(f"Generated {len(negative_patches)} negative patches")
if negative_patch_info:
    avg_points = np.mean([info['num_points'] for info in negative_patch_info])
    print(f"Average points per negative patch: {avg_points:.0f}")

# Create basic overview visualization
fig, axes = plt.subplots(1, 2, figsize=(12, 5))

# Plot 1: Point cloud overview with pile locations
ax1 = axes[0]
point_sample = points[::100]  # Sample for performance
ax1.scatter(point_sample[:, 0], point_sample[:, 1], c='lightgray', s=0.5, alpha=0.3)

# Plot pile centers
pile_centers = np.array([info['center'] for info in high_quality_info])
if len(pile_centers) > 0:
    ax1.scatter(pile_centers[:, 0], pile_centers[:, 1], c='red', s=10, alpha=0.7)

ax1.set_title(f'Point Cloud Overview\n{len(high_quality_patches)} Detected Piles')
ax1.set_xlabel('X')
ax1.set_ylabel('Y')
ax1.axis('equal')
ax1.grid(True, alpha=0.3)

# Plot 2: Quality distribution
ax2 = axes[1]
if high_quality_info:
    qualities = [info['quality_score'] for info in high_quality_info]
    ax2.hist(qualities, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(np.mean(qualities), color='red', linestyle='--', 
                label=f'Mean: {np.mean(qualities):.3f}')
    ax2.set_xlabel('Quality Score')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Patch Quality Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

import numpy as np
from pathlib import Path
from datetime import datetime
import json

def make_json_serializable(obj):
    if isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, (np.integer, np.floating)):
        return obj.item()
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, Path):
        return str(obj)
    else:
        return obj

if save_results:
    output_dir = Path("../data/output_runs") / f"pile_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    output_dir.mkdir(parents=True, exist_ok=True)

    patch_data = {
        'positive_patches': [patch.tolist() for patch in high_quality_patches],
        'negative_patches': [patch.tolist() for patch in negative_patches],
        'positive_info': high_quality_info,
        'negative_info': negative_patch_info,
        'pile_coords': pile_coords.tolist(),
        'parameters': {
            'site_name': site_name,
            'ground_method': ground_method,
            'patch_size': patch_size,
            'min_points_per_patch': min_points_per_patch
        }
    }

    # Ensure everything is JSON serializable
    patch_data_serializable = make_json_serializable(patch_data)

    patch_file = output_dir / "extracted_patches.json"
    with open(patch_file, 'w') as f:
        json.dump(patch_data_serializable, f, indent=2)

    print(f"Saved patch data to: {patch_file}")
    print(f"Positive patches: {len(high_quality_patches)}")
    print(f"Negative patches: {len(negative_patches)}")

    with open("output_dir.txt", "w") as f:
        f.write(str(output_dir))


def analyze_patch_features(patch_points):
    """Extract geometric features from a point patch"""
    if len(patch_points) == 0:
        return {}
    
    # Basic statistics
    features = {
        'num_points': len(patch_points),
        'height_range': patch_points[:, 2].max() - patch_points[:, 2].min(),
        'height_std': np.std(patch_points[:, 2]),
        'height_mean': np.mean(patch_points[:, 2]),
        'xy_extent_x': patch_points[:, 0].max() - patch_points[:, 0].min(),
        'xy_extent_y': patch_points[:, 1].max() - patch_points[:, 1].min()
    }
    
    # Density features
    xy_area = features['xy_extent_x'] * features['xy_extent_y']
    features['point_density'] = features['num_points'] / max(xy_area, 1e-6)
    
    # Vertical distribution
    z_values = patch_points[:, 2]
    features['z_percentile_25'] = np.percentile(z_values, 25)
    features['z_percentile_75'] = np.percentile(z_values, 75)
    features['z_iqr'] = features['z_percentile_75'] - features['z_percentile_25']
    
    # Clustering analysis
    if len(patch_points) > 10:
        clustering = DBSCAN(eps=0.5, min_samples=5).fit(patch_points)
        features['num_clusters'] = len(set(clustering.labels_)) - (1 if -1 in clustering.labels_ else 0)
        features['noise_ratio'] = np.sum(clustering.labels_ == -1) / len(clustering.labels_)
    else:
        features['num_clusters'] = 0
        features['noise_ratio'] = 1.0
    
    return features

# Analyze all patches
print("Analyzing patch features...")

pile_features = []
for i, patch in enumerate(pile_patches):
    features = analyze_patch_features(patch)
    features['label'] = 1
    features['patch_type'] = 'pile'
    pile_features.append(features)

negative_features = []
for i, patch in enumerate(negative_patches):
    features = analyze_patch_features(patch)
    features['label'] = 0
    features['patch_type'] = 'negative'
    negative_features.append(features)

# Combine all features
all_features = pile_features + negative_features
features_df = pd.DataFrame(all_features)

print(f"Feature analysis complete:")
print(f"  Pile patches: {len(pile_features)}")
print(f"  Negative patches: {len(negative_features)}")
print(f"  Total features: {len(features_df.columns)}")

# Feature comparison analysis
print("=== FEATURE COMPARISON: PILE vs NEGATIVE ===")

feature_cols = ['height_range', 'height_std', 'point_density', 'num_clusters', 'noise_ratio']

for feature in feature_cols:
    pile_values = features_df[features_df['label'] == 1][feature]
    negative_values = features_df[features_df['label'] == 0][feature]
    
    print(f"\n{feature.upper()}:")
    print(f"  Pile patches: {pile_values.mean():.3f} ± {pile_values.std():.3f}")
    print(f"  Negative patches: {negative_values.mean():.3f} ± {negative_values.std():.3f}")
    print(f"  Separation ratio: {abs(pile_values.mean() - negative_values.mean()) / (pile_values.std() + negative_values.std()):.2f}")

def classify_pile_rules(features):
    """Simple rule-based pile classification"""
    score = 0
    
    # Rule 1: Height characteristics
    if features['height_range'] > 2.0:  # Piles should have significant height
        score += 1
    
    # Rule 2: Point density
    if features['point_density'] > 50:  # Dense point clusters
        score += 1
    
    # Rule 3: Vertical structure
    if features['height_std'] > 0.5:  # Vertical variation
        score += 1
    
    # Rule 4: Clustering
    if features['num_clusters'] >= 1 and features['noise_ratio'] < 0.5:
        score += 1
    
    # Rule 5: Aspect ratio
    if features['xy_extent_x'] < 3.0 and features['xy_extent_y'] < 3.0:  # Compact footprint
        score += 1
    
    return score >= 3  # Threshold for pile classification

# Apply rule-based classification
print("Applying rule-based classification...")

predictions = []
for _, row in features_df.iterrows():
    prediction = classify_pile_rules(row)
    predictions.append(int(prediction))

features_df['predicted'] = predictions

# Calculate performance metrics
true_labels = features_df['label'].values
predicted_labels = features_df['predicted'].values

tp = np.sum((true_labels == 1) & (predicted_labels == 1))
tn = np.sum((true_labels == 0) & (predicted_labels == 0))
fp = np.sum((true_labels == 0) & (predicted_labels == 1))
fn = np.sum((true_labels == 1) & (predicted_labels == 0))

accuracy = (tp + tn) / len(true_labels)
precision = tp / (tp + fp) if (tp + fp) > 0 else 0
recall = tp / (tp + fn) if (tp + fn) > 0 else 0
f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

print(f"\n=== RULE-BASED CLASSIFICATION RESULTS ===")
print(f"Accuracy: {accuracy:.3f}")
print(f"Precision: {precision:.3f}")
print(f"Recall: {recall:.3f}")
print(f"F1-Score: {f1_score:.3f}")
print(f"\nConfusion Matrix:")
print(f"  True Positives: {tp}")
print(f"  True Negatives: {tn}")
print(f"  False Positives: {fp}")
print(f"  False Negatives: {fn}")

if save_results:
    print("=== SAVING TRAINING DATA ===")
    
    # Create output directory
    output_dir = Path("../data/output_runs") / f"ifc_pile_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save features
    features_file = output_dir / "pile_detection_features.csv"
    features_df.to_csv(features_file, index=False)
    print(f"Saved features: {features_file}")
    
    # Save patch data for ML training
    training_data = {
        'positive_patches': [patch.tolist() for patch in pile_patches],
        'negative_patches': [patch.tolist() for patch in negative_patches],
        'positive_info': pile_patch_info,
        'negative_info': negative_patch_info,
        'parameters': {
            'site_name': site_name,
            'ground_method': ground_method,
            'patch_size': patch_size,
            'min_points_per_patch': min_points_per_patch
        },
        'performance': {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1_score)
        }
    }
    
    training_file = output_dir / "training_data.json"
    with open(training_file, 'w') as f:
        json.dump(training_data, f, indent=2)
    print(f"Saved training data: {training_file}")
    
    # Save summary
    summary = {
        'dataset_summary': {
            'total_patches': len(all_features),
            'positive_patches': len(pile_features),
            'negative_patches': len(negative_features),
            'avg_points_per_patch': float(np.mean([f['num_points'] for f in all_features]))
        },
        'rule_based_performance': {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1_score)
        },
        'next_steps': [
            "Use training_data.json for PointNet++ implementation",
            "Use training_data.json for DGCNN implementation",
            "Compare ML results with rule-based baseline"
        ]
    }
    
    summary_file = output_dir / "summary.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"Saved summary: {summary_file}")
    
    print(f"\nAll results saved to: {output_dir}")
    print(f"Ready for PointNet++ and DGCNN implementation.")

else:
    print("Skipping save - save_results=False")