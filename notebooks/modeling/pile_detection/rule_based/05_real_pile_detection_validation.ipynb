{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Real Pile Detection Validation\n", "\n", "This notebook validates **actual detected pile coordinates** against ground truth data, providing real validation metrics.\n", "\n", "**Purpose**: Validate detected piles (from detection algorithms) against ground truth survey data\n", "\n", "**Input**: \n", "- Detected pile coordinates (from detection pipeline)\n", "- Ground truth validation data (Trino_PIles.csv)\n", "- KML pile regions (optional)\n", "\n", "**Output**: Real validation metrics (precision, recall, spatial accuracy)\n", "\n", "**Key Difference**: This validates **detected results** vs **ground truth**, not metadata vs metadata\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"csf\"  # Options: csf, pmf, ransac\n", "detection_run_timestamp = \"latest\"  # Specific timestamp or \"latest\"\n", "matching_tolerance = 2.0  # meters - distance threshold for matching\n", "confidence_threshold = 0.5  # minimum confidence for detected piles\n", "save_results = True\n", "create_visualizations = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Environment Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Spatial analysis\n", "from scipy.spatial.distance import cdist\n", "from sklearn.metrics import precision_score, recall_score, f1_score\n", "import seaborn as sns\n", "\n", "print(\"Real Pile Detection Validation\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Matching tolerance: {matching_tolerance}m\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Configure Paths and Load Detection Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure paths\n", "base_path = Path(\"../../../..\")\n", "data_path = base_path / \"data\"\n", "\n", "# Detection results path\n", "detection_base = data_path / \"output_runs\" / \"pile_detection\" / \"comprehensive_detection\"\n", "\n", "# Find latest detection results if timestamp not specified\n", "if detection_run_timestamp == \"latest\":\n", "    detection_dirs = [d for d in detection_base.glob(f\"{site_name}_{ground_method}_*\") if d.is_dir()]\n", "    if not detection_dirs:\n", "        raise FileNotFoundError(f\"No detection results found in {detection_base}\")\n", "    \n", "    detection_dir = max(detection_dirs, key=lambda d: d.name)\n", "    print(f\"Using latest detection results: {detection_dir.name}\")\n", "else:\n", "    detection_dir = detection_base / f\"{site_name}_{ground_method}_{detection_run_timestamp}\"\n", "    if not detection_dir.exists():\n", "        raise FileNotFoundError(f\"Detection results not found: {detection_dir}\")\n", "\n", "# Load detected pile coordinates\n", "detection_files = list(detection_dir.glob(f\"{site_name}_detected_pile_coordinates_*.csv\"))\n", "if not detection_files:\n", "    raise FileNotFoundError(f\"No detected pile coordinates found in {detection_dir}\")\n", "\n", "detection_file = detection_files[0]  # Use the first/latest file\n", "detected_piles_df = pd.read_csv(detection_file)\n", "\n", "print(f\"\\nLoaded detected piles: {len(detected_piles_df)} piles\")\n", "print(f\"Detection file: {detection_file.name}\")\n", "print(f\"Columns: {list(detected_piles_df.columns)}\")\n", "\n", "# Filter by confidence threshold\n", "if 'confidence' in detected_piles_df.columns:\n", "    high_confidence_piles = detected_piles_df[detected_piles_df['confidence'] >= confidence_threshold]\n", "    print(f\"High confidence piles (>={confidence_threshold}): {len(high_confidence_piles)}\")\n", "else:\n", "    high_confidence_piles = detected_piles_df\n", "    print(\"No confidence column found, using all detections\")\n", "\n", "# Load detection metadata\n", "metadata_files = list(detection_dir.glob(f\"{site_name}_detection_metadata_*.json\"))\n", "if metadata_files:\n", "    with open(metadata_files[0], 'r') as f:\n", "        detection_metadata = json.load(f)\n", "    print(f\"Detection metadata loaded: {detection_metadata.get('detection_methods_used', 'unknown')} methods\")\n", "else:\n", "    detection_metadata = {}\n", "    print(\"No detection metadata found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Load Ground Truth Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load ground truth validation data\n", "validation_data_paths = [\n", "    data_path / \"processed\" / site_name / \"validation\" / \"Trino_PIles.csv\",\n", "    data_path / \"raw\" / site_name / \"validation\" / \"Trino_PIles.csv\",\n", "    data_path / \"validation\" / \"Trino_PIles.csv\"\n", "]\n", "\n", "ground_truth_df = None\n", "for val_path in validation_data_paths:\n", "    if val_path.exists():\n", "        ground_truth_df = pd.read_csv(val_path)\n", "        print(f\"\\nLoaded ground truth data: {val_path}\")\n", "        break\n", "\n", "if ground_truth_df is None:\n", "    raise FileNotFoundError(f\"Ground truth validation data not found in any of: {[str(p) for p in validation_data_paths]}\")\n", "\n", "print(f\"Ground truth piles: {len(ground_truth_df)}\")\n", "print(f\"Ground truth columns: {list(ground_truth_df.columns)}\")\n", "print(\"\\nGround truth sample:\")\n", "print(ground_truth_df.head())\n", "\n", "# Extract coordinates from ground truth\n", "# Try different possible column names\n", "coord_columns = {\n", "    'x': ['x', 'X', 'longitude', 'lon', 'easting'],\n", "    'y': ['y', 'Y', 'latitude', 'lat', 'northing'],\n", "    'z': ['z', 'Z', 'elevation', 'height', 'altitude']\n", "}\n", "\n", "gt_coords = {}\n", "for coord, possible_names in coord_columns.items():\n", "    for name in possible_names:\n", "        if name in ground_truth_df.columns:\n", "            gt_coords[coord] = name\n", "            break\n", "    \n", "    if coord not in gt_coords:\n", "        print(f\"Warning: Could not find {coord} coordinate column in ground truth data\")\n", "\n", "print(f\"\\nGround truth coordinate mapping: {gt_coords}\")\n", "\n", "# Extract ground truth coordinates\n", "if 'x' in gt_coords and 'y' in gt_coords:\n", "    if 'z' in gt_coords:\n", "        gt_coordinates = ground_truth_df[[gt_coords['x'], gt_coords['y'], gt_coords['z']]].values\n", "        print(f\"Using 3D coordinates for ground truth\")\n", "    else:\n", "        gt_coordinates = ground_truth_df[[gt_coords['x'], gt_coords['y']]].values\n", "        print(f\"Using 2D coordinates for ground truth (no Z data)\")\n", "    \n", "    print(f\"Ground truth coordinate ranges:\")\n", "    print(f\"  X: {gt_coordinates[:, 0].min():.2f} to {gt_coordinates[:, 0].max():.2f}\")\n", "    print(f\"  Y: {gt_coordinates[:, 1].min():.2f} to {gt_coordinates[:, 1].max():.2f}\")\n", "    if gt_coordinates.shape[1] > 2:\n", "        print(f\"  Z: {gt_coordinates[:, 2].min():.2f} to {gt_coordinates[:, 2].max():.2f}\")\n", "else:\n", "    raise ValueError(\"Could not identify X and Y coordinate columns in ground truth data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Extract Detected Pile Coordinates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract detected pile coordinates\n", "detected_coords = high_confidence_piles[['x', 'y', 'z']].values\n", "\n", "print(f\"\\nDetected pile coordinates:\")\n", "print(f\"  Count: {len(detected_coords)}\")\n", "print(f\"  X range: {detected_coords[:, 0].min():.2f} to {detected_coords[:, 0].max():.2f}\")\n", "print(f\"  Y range: {detected_coords[:, 1].min():.2f} to {detected_coords[:, 1].max():.2f}\")\n", "print(f\"  Z range: {detected_coords[:, 2].min():.2f} to {detected_coords[:, 2].max():.2f}\")\n", "\n", "# Check coordinate system compatibility\n", "print(f\"\\nCoordinate system analysis:\")\n", "print(f\"  Detected X range: {detected_coords[:, 0].max() - detected_coords[:, 0].min():.1f}m\")\n", "print(f\"  Ground truth X range: {gt_coordinates[:, 0].max() - gt_coordinates[:, 0].min():.1f}m\")\n", "print(f\"  Detected Y range: {detected_coords[:, 1].max() - detected_coords[:, 1].min():.1f}m\")\n", "print(f\"  Ground truth Y range: {gt_coordinates[:, 1].max() - gt_coordinates[:, 1].min():.1f}m\")\n", "\n", "# Check for coordinate system offset\n", "x_offset = abs(detected_coords[:, 0].mean() - gt_coordinates[:, 0].mean())\n", "y_offset = abs(detected_coords[:, 1].mean() - gt_coordinates[:, 1].mean())\n", "\n", "print(f\"\\nCoordinate offset analysis:\")\n", "print(f\"  X offset (mean difference): {x_offset:.1f}m\")\n", "print(f\"  Y offset (mean difference): {y_offset:.1f}m\")\n", "\n", "if x_offset > 1000 or y_offset > 1000:\n", "    print(\"  WARNING: Large coordinate offset detected - may indicate different coordinate systems\")\n", "elif x_offset > 100 or y_offset > 100:\n", "    print(\"  NOTICE: Moderate coordinate offset detected - may need alignment correction\")\n", "else:\n", "    print(\"  Coordinate systems appear compatible\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Spatial Matching and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def perform_spatial_matching(detected_coords, gt_coordinates, tolerance):\n", "    \"\"\"Perform spatial matching between detected and ground truth piles.\"\"\"\n", "    \n", "    print(f\"\\n=== SPATIAL MATCHING ===\")\n", "    print(f\"Matching tolerance: {tolerance}m\")\n", "    print(f\"Detected piles: {len(detected_coords)}\")\n", "    print(f\"Ground truth piles: {len(gt_coordinates)}\")\n", "    \n", "    # Use 2D matching (X,Y only) for compatibility\n", "    detected_2d = detected_coords[:, :2]\n", "    gt_2d = gt_coordinates[:, :2]\n", "    \n", "    # Calculate pairwise distances\n", "    distances = cdist(detected_2d, gt_2d)\n", "    \n", "    # Find matches within tolerance\n", "    matches = []\n", "    matched_detected = set()\n", "    matched_gt = set()\n", "    \n", "    # For each detected pile, find closest ground truth pile\n", "    for i, detected_pile in enumerate(detected_2d):\n", "        min_distance = distances[i].min()\n", "        closest_gt_idx = distances[i].argmin()\n", "        \n", "        if min_distance <= tolerance and closest_gt_idx not in matched_gt:\n", "            matches.append({\n", "                'detected_idx': i,\n", "                'gt_idx': closest_gt_idx,\n", "                'distance': min_distance,\n", "                'detected_coords': detected_coords[i],\n", "                'gt_coords': gt_coordinates[closest_gt_idx]\n", "            })\n", "            matched_detected.add(i)\n", "            matched_gt.add(closest_gt_idx)\n", "    \n", "    # Calculate validation metrics\n", "    true_positives = len(matches)\n", "    false_positives = len(detected_coords) - true_positives\n", "    false_negatives = len(gt_coordinates) - true_positives\n", "    \n", "    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0\n", "    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0\n", "    f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "    \n", "    # Spatial accuracy metrics\n", "    if matches:\n", "        match_distances = [m['distance'] for m in matches]\n", "        mean_spatial_error = np.mean(match_distances)\n", "        rmse_spatial_error = np.sqrt(np.mean(np.array(match_distances)**2))\n", "        max_spatial_error = np.max(match_distances)\n", "        std_spatial_error = np.std(match_distances)\n", "    else:\n", "        mean_spatial_error = rmse_spatial_error = max_spatial_error = std_spatial_error = 0.0\n", "    \n", "    validation_results = {\n", "        'matches': matches,\n", "        'true_positives': true_positives,\n", "        'false_positives': false_positives,\n", "        'false_negatives': false_negatives,\n", "        'precision': precision,\n", "        'recall': recall,\n", "        'f1_score': f1,\n", "        'mean_spatial_error': mean_spatial_error,\n", "        'rmse_spatial_error': rmse_spatial_error,\n", "        'max_spatial_error': max_spatial_error,\n", "        'std_spatial_error': std_spatial_error,\n", "        'matching_tolerance': tolerance,\n", "        'total_detected': len(detected_coords),\n", "        'total_ground_truth': len(gt_coordinates)\n", "    }\n", "    \n", "    print(f\"\\nMatching results:\")\n", "    print(f\"  Successful matches: {true_positives}\")\n", "    print(f\"  Unmatched detections (false positives): {false_positives}\")\n", "    print(f\"  Missed ground truth (false negatives): {false_negatives}\")\n", "    \n", "    print(f\"\\nValidation metrics:\")\n", "    print(f\"  Precision: {precision:.3f}\")\n", "    print(f\"  Recall: {recall:.3f}\")\n", "    print(f\"  F1-Score: {f1:.3f}\")\n", "    \n", "    if matches:\n", "        print(f\"\\nSpatial accuracy:\")\n", "        print(f\"  Mean spatial error: {mean_spatial_error:.2f}m\")\n", "        print(f\"  RMSE: {rmse_spatial_error:.2f}m\")\n", "        print(f\"  Max error: {max_spatial_error:.2f}m\")\n", "        print(f\"  Std deviation: {std_spatial_error:.2f}m\")\n", "    \n", "    return validation_results\n", "\n", "# Perform validation\n", "validation_results = perform_spatial_matching(detected_coords, gt_coordinates, matching_tolerance)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Create Validation Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if create_visualizations:\n", "    print(\"\\n=== CREATING VALIDATION VISUALIZATIONS ===\")\n", "    \n", "    # Create comprehensive validation visualization\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Spatial distribution comparison\n", "    ax1.scatter(gt_coordinates[:, 0], gt_coordinates[:, 1], \n", "               c='red', s=100, alpha=0.7, marker='s', \n", "               label=f'Ground Truth ({len(gt_coordinates)})', edgecolors='darkred')\n", "    \n", "    ax1.scatter(detected_coords[:, 0], detected_coords[:, 1], \n", "               c='blue', s=60, alpha=0.7, marker='o', \n", "               label=f'Detected ({len(detected_coords)})', edgecolors='darkblue')\n", "    \n", "    # Draw connection lines for matches\n", "    if validation_results['matches']:\n", "        for match in validation_results['matches']:\n", "            det_coords = match['detected_coords']\n", "            gt_coords = match['gt_coords']\n", "            ax1.plot([det_coords[0], gt_coords[0]], [det_coords[1], gt_coords[1]], \n", "                    'green', alpha=0.5, linewidth=1)\n", "    \n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title(f'Pile Detection Validation - Spatial Distribution\\n'\n", "                 f'Matches: {validation_results[\"true_positives\"]}, '\n", "                 f'Precision: {validation_results[\"precision\"]:.3f}, '\n", "                 f'Recall: {validation_results[\"recall\"]:.3f}')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.legend()\n", "    ax1.set_aspect('equal', adjustable='box')\n", "    \n", "    # 2. Validation metrics bar chart\n", "    metrics = ['Precision', 'Recall', 'F1-Score']\n", "    values = [validation_results['precision'], validation_results['recall'], validation_results['f1_score']]\n", "    colors = ['skyblue', 'lightcoral', 'lightgreen']\n", "    \n", "    bars = ax2.bar(metrics, values, color=colors, alpha=0.7, edgecolor='black')\n", "    ax2.set_ylabel('Score')\n", "    ax2.set_title('Validation Metrics')\n", "    ax2.set_ylim(0, 1)\n", "    ax2.grid(True, alpha=0.3, axis='y')\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, values):\n", "        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02, \n", "                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 3. Spatial error distribution\n", "    if validation_results['matches']:\n", "        match_distances = [m['distance'] for m in validation_results['matches']]\n", "        ax3.hist(match_distances, bins=15, alpha=0.7, color='orange', edgecolor='black')\n", "        ax3.axvline(validation_results['mean_spatial_error'], color='red', linestyle='--', \n", "                   label=f'Mean: {validation_results[\"mean_spatial_error\"]:.2f}m')\n", "        ax3.axvline(matching_tolerance, color='blue', linestyle='--', \n", "                   label=f'Tolerance: {matching_tolerance}m')\n", "        ax3.set_xlabel('Spatial Error (m)')\n", "        ax3.set_ylabel('Number of Matches')\n", "        ax3.set_title('Spatial Error Distribution')\n", "        ax3.grid(True, alpha=0.3)\n", "        ax3.legend()\n", "    else:\n", "        ax3.text(0.5, 0.5, 'No matches found', ha='center', va='center', \n", "                transform=ax3.transAxes, fontsize=14)\n", "        ax3.set_title('Spatial Error Distribution (No Matches)')\n", "    \n", "    # 4. Detection summary pie chart\n", "    labels = ['True Positives', 'False Positives', 'False Negatives']\n", "    sizes = [validation_results['true_positives'], \n", "             validation_results['false_positives'], \n", "             validation_results['false_negatives']]\n", "    colors = ['lightgreen', 'lightcoral', 'lightyellow']\n", "    \n", "    # Only include non-zero values\n", "    non_zero_data = [(label, size, color) for label, size, color in zip(labels, sizes, colors) if size > 0]\n", "    \n", "    if non_zero_data:\n", "        labels_nz, sizes_nz, colors_nz = zip(*non_zero_data)\n", "        wedges, texts, autotexts = ax4.pie(sizes_nz, labels=labels_nz, colors=colors_nz, \n", "                                          autopct='%1.1f%%', startangle=90)\n", "        ax4.set_title('Detection Results Breakdown')\n", "    else:\n", "        ax4.text(0.5, 0.5, 'No data to display', ha='center', va='center', \n", "                transform=ax4.transAxes, fontsize=14)\n", "        ax4.set_title('Detection Results (No Data)')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_results:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        viz_file = detection_dir / f\"{site_name}_validation_visualization_{timestamp}.png\"\n", "        plt.savefig(viz_file, dpi=300, bbox_inches='tight')\n", "        print(f\"Saved validation visualization: {viz_file}\")\n", "    \n", "    plt.show()\n", "    \n", "else:\n", "    print(\"\\nVisualization disabled (create_visualizations=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Detailed Analysis and Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\" * 60)\n", "print(\"DETAILED VALIDATION ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Performance analysis\n", "print(f\"\\n📊 DETECTION PERFORMANCE:\")\n", "print(f\"  Precision: {validation_results['precision']:.3f} ({validation_results['true_positives']}/{validation_results['true_positives'] + validation_results['false_positives']})\")\n", "print(f\"  Recall: {validation_results['recall']:.3f} ({validation_results['true_positives']}/{validation_results['true_positives'] + validation_results['false_negatives']})\")\n", "print(f\"  F1-Score: {validation_results['f1_score']:.3f}\")\n", "\n", "# Spatial accuracy analysis\n", "if validation_results['matches']:\n", "    print(f\"\\n📏 SPATIAL ACCURACY:\")\n", "    print(f\"  Mean spatial error: {validation_results['mean_spatial_error']:.2f}m\")\n", "    print(f\"  RMSE: {validation_results['rmse_spatial_error']:.2f}m\")\n", "    print(f\"  Maximum error: {validation_results['max_spatial_error']:.2f}m\")\n", "    print(f\"  Standard deviation: {validation_results['std_spatial_error']:.2f}m\")\n", "    print(f\"  Matching tolerance: {validation_results['matching_tolerance']:.2f}m\")\n", "    \n", "    # Error analysis\n", "    match_distances = [m['distance'] for m in validation_results['matches']]\n", "    within_1m = sum(1 for d in match_distances if d <= 1.0)\n", "    within_2m = sum(1 for d in match_distances if d <= 2.0)\n", "    \n", "    print(f\"\\n🎯 ERROR DISTRIBUTION:\")\n", "    print(f\"  Matches within 1m: {within_1m}/{len(match_distances)} ({within_1m/len(match_distances)*100:.1f}%)\")\n", "    print(f\"  Matches within 2m: {within_2m}/{len(match_distances)} ({within_2m/len(match_distances)*100:.1f}%)\")\n", "\n", "# Detection method analysis\n", "if 'detection_method' in high_confidence_piles.columns:\n", "    method_performance = {}\n", "    for method in high_confidence_piles['detection_method'].unique():\n", "        method_piles = high_confidence_piles[high_confidence_piles['detection_method'] == method]\n", "        method_coords = method_piles[['x', 'y', 'z']].values\n", "        \n", "        # Quick validation for this method\n", "        method_validation = perform_spatial_matching(method_coords, gt_coordinates, matching_tolerance)\n", "        method_performance[method] = {\n", "            'count': len(method_coords),\n", "            'precision': method_validation['precision'],\n", "            'recall': method_validation['recall'],\n", "            'f1_score': method_validation['f1_score']\n", "        }\n", "    \n", "    print(f\"\\n🔧 METHOD PERFORMANCE:\")\n", "    for method, perf in method_performance.items():\n", "        print(f\"  {method.upper()}:\")\n", "        print(f\"    Detections: {perf['count']}\")\n", "        print(f\"    Precision: {perf['precision']:.3f}\")\n", "        print(f\"    Recall: {perf['recall']:.3f}\")\n", "        print(f\"    F1-Score: {perf['f1_score']:.3f}\")\n", "\n", "# Coverage analysis\n", "print(f\"\\n📍 COVERAGE ANALYSIS:\")\n", "detection_rate = validation_results['true_positives'] / validation_results['total_ground_truth']\n", "false_positive_rate = validation_results['false_positives'] / validation_results['total_detected'] if validation_results['total_detected'] > 0 else 0\n", "\n", "print(f\"  Detection rate: {detection_rate:.3f} ({validation_results['true_positives']}/{validation_results['total_ground_truth']} ground truth piles found)\")\n", "print(f\"  False positive rate: {false_positive_rate:.3f} ({validation_results['false_positives']}/{validation_results['total_detected']} detections are false positives)\")\n", "print(f\"  Missed piles: {validation_results['false_negatives']} ground truth piles not detected\")\n", "\n", "# Quality assessment\n", "print(f\"\\n⭐ QUALITY ASSESSMENT:\")\n", "if validation_results['f1_score'] >= 0.8:\n", "    quality = \"Excellent\"\n", "elif validation_results['f1_score'] >= 0.6:\n", "    quality = \"Good\"\n", "elif validation_results['f1_score'] >= 0.4:\n", "    quality = \"Fair\"\n", "else:\n", "    quality = \"Poor\"\n", "\n", "print(f\"  Overall quality: {quality} (F1-Score: {validation_results['f1_score']:.3f})\")\n", "\n", "if validation_results['matches'] and validation_results['mean_spatial_error'] <= 1.0:\n", "    spatial_quality = \"High\"\n", "elif validation_results['matches'] and validation_results['mean_spatial_error'] <= 2.0:\n", "    spatial_quality = \"Medium\"\n", "else:\n", "    spatial_quality = \"Low\"\n", "\n", "print(f\"  Spatial accuracy: {spatial_quality} (Mean error: {validation_results['mean_spatial_error']:.2f}m)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Save Validation Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if save_results:\n", "    print(\"\\n=== SAVING VALIDATION RESULTS ===\")\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 1. Save validation metrics (JSON)\n", "    validation_summary = {\n", "        'validation_timestamp': timestamp,\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'detection_file': str(detection_file),\n", "        'ground_truth_file': str([p for p in validation_data_paths if p.exists()][0]),\n", "        'matching_tolerance': matching_tolerance,\n", "        'confidence_threshold': confidence_threshold,\n", "        'validation_metrics': {\n", "            'precision': validation_results['precision'],\n", "            'recall': validation_results['recall'],\n", "            'f1_score': validation_results['f1_score'],\n", "            'true_positives': validation_results['true_positives'],\n", "            'false_positives': validation_results['false_positives'],\n", "            'false_negatives': validation_results['false_negatives']\n", "        },\n", "        'spatial_accuracy': {\n", "            'mean_spatial_error': validation_results['mean_spatial_error'],\n", "            'rmse_spatial_error': validation_results['rmse_spatial_error'],\n", "            'max_spatial_error': validation_results['max_spatial_error'],\n", "            'std_spatial_error': validation_results['std_spatial_error']\n", "        },\n", "        'data_summary': {\n", "            'total_detected_piles': validation_results['total_detected'],\n", "            'total_ground_truth_piles': validation_results['total_ground_truth'],\n", "            'high_confidence_detections': len(high_confidence_piles),\n", "            'successful_matches': len(validation_results['matches'])\n", "        },\n", "        'detection_metadata': detection_metadata\n", "    }\n", "    \n", "    metrics_file = detection_dir / f\"{site_name}_real_validation_metrics_{timestamp}.json\"\n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(validation_summary, f, indent=2)\n", "    print(f\"Saved validation metrics: {metrics_file}\")\n", "    \n", "    # 2. Save detailed match results (CSV)\n", "    if validation_results['matches']:\n", "        match_data = []\n", "        for i, match in enumerate(validation_results['matches']):\n", "            detected_pile = high_confidence_piles.iloc[match['detected_idx']]\n", "            gt_pile = ground_truth_df.iloc[match['gt_idx']]\n", "            \n", "            match_record = {\n", "                'match_id': i + 1,\n", "                'detected_pile_id': detected_pile.get('pile_id', f\"det_{match['detected_idx']}\"),\n", "                'detected_x': match['detected_coords'][0],\n", "                'detected_y': match['detected_coords'][1],\n", "                'detected_z': match['detected_coords'][2],\n", "                'detected_confidence': detected_pile.get('confidence', 0.0),\n", "                'detected_method': detected_pile.get('detection_method', 'unknown'),\n", "                'gt_x': match['gt_coords'][0],\n", "                'gt_y': match['gt_coords'][1],\n", "                'gt_z': match['gt_coords'][2] if len(match['gt_coords']) > 2 else 0.0,\n", "                'spatial_error': match['distance'],\n", "                'x_error': match['detected_coords'][0] - match['gt_coords'][0],\n", "                'y_error': match['detected_coords'][1] - match['gt_coords'][1],\n", "                'z_error': match['detected_coords'][2] - (match['gt_coords'][2] if len(match['gt_coords']) > 2 else 0.0)\n", "            }\n", "            match_data.append(match_record)\n", "        \n", "        matches_df = pd.DataFrame(match_data)\n", "        matches_file = detection_dir / f\"{site_name}_detailed_matches_{timestamp}.csv\"\n", "        matches_df.to_csv(matches_file, index=False)\n", "        print(f\"Saved detailed matches: {matches_file}\")\n", "    \n", "    # 3. Save unmatched detections and ground truth\n", "    unmatched_detected_indices = set(range(len(high_confidence_piles))) - {m['detected_idx'] for m in validation_results['matches']}\n", "    unmatched_gt_indices = set(range(len(ground_truth_df))) - {m['gt_idx'] for m in validation_results['matches']}\n", "    \n", "    if unmatched_detected_indices:\n", "        unmatched_detected = high_confidence_piles.iloc[list(unmatched_detected_indices)]\n", "        unmatched_det_file = detection_dir / f\"{site_name}_unmatched_detections_{timestamp}.csv\"\n", "        unmatched_detected.to_csv(unmatched_det_file, index=False)\n", "        print(f\"Saved unmatched detections: {unmatched_det_file}\")\n", "    \n", "    if unmatched_gt_indices:\n", "        unmatched_gt = ground_truth_df.iloc[list(unmatched_gt_indices)]\n", "        unmatched_gt_file = detection_dir / f\"{site_name}_missed_ground_truth_{timestamp}.csv\"\n", "        unmatched_gt.to_csv(unmatched_gt_file, index=False)\n", "        print(f\"Saved missed ground truth: {unmatched_gt_file}\")\n", "    \n", "    print(f\"\\nAll validation results saved to: {detection_dir}\")\n", "    \n", "else:\n", "    print(\"\\nSaving disabled (save_results=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 9: Generate Validation Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if save_results:\n", "    print(\"\\n=== GENERATING VALIDATION REPORT ===\")\n", "    \n", "    # Generate comprehensive validation report\n", "    report_content = f\"\"\"\n", "# Real Pile Detection Validation Report\n", "\n", "**Site**: {site_name}\n", "**Ground Method**: {ground_method}\n", "**Validation Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n", "**Matching Tolerance**: {matching_tolerance} meters\n", "**Confidence Threshold**: {confidence_threshold}\n", "\n", "## Executive Summary\n", "\n", "This report presents the validation results of pile detection algorithms against ground truth survey data. The validation compares **actual detected pile coordinates** with **ground truth pile locations**, providing real performance metrics.\n", "\n", "### Key Results\n", "- **Detection Performance**: Precision {validation_results['precision']:.3f}, Recall {validation_results['recall']:.3f}, F1-Score {validation_results['f1_score']:.3f}\n", "- **Spatial Accuracy**: Mean error {validation_results['mean_spatial_error']:.2f}m, RMSE {validation_results['rmse_spatial_error']:.2f}m\n", "- **Coverage**: {validation_results['true_positives']}/{validation_results['total_ground_truth']} ground truth piles detected ({validation_results['true_positives']/validation_results['total_ground_truth']*100:.1f}%)\n", "\n", "## Data Sources\n", "\n", "### Detection Results\n", "- **Source**: {detection_file.name}\n", "- **Total Detections**: {len(detected_piles_df):,}\n", "- **High Confidence Detections**: {len(high_confidence_piles):,} (confidence ≥ {confidence_threshold})\n", "- **Detection Methods**: {', '.join(detection_metadata.get('detection_methods_used', ['unknown']))}\n", "\n", "### Ground Truth Data\n", "- **Source**: Ground truth survey data (Trino_PIles.csv)\n", "- **Total Ground Truth Piles**: {len(ground_truth_df):,}\n", "- **Coordinate System**: {gt_coords}\n", "\n", "## Validation Results\n", "\n", "### Detection Performance Metrics\n", "- **Precision**: {validation_results['precision']:.3f} ({validation_results['true_positives']}/{validation_results['true_positives'] + validation_results['false_positives']} correct detections)\n", "- **Recall**: {validation_results['recall']:.3f} ({validation_results['true_positives']}/{validation_results['true_positives'] + validation_results['false_negatives']} ground truth piles found)\n", "- **F1-Score**: {validation_results['f1_score']:.3f} (harmonic mean of precision and recall)\n", "\n", "### Spatial Accuracy Metrics\n", "\"\"\"\n", "    \n", "    if validation_results['matches']:\n", "        match_distances = [m['distance'] for m in validation_results['matches']]\n", "        within_1m = sum(1 for d in match_distances if d <= 1.0)\n", "        within_2m = sum(1 for d in match_distances if d <= 2.0)\n", "        \n", "        report_content += f\"\"\"\n", "- **Mean Spatial Error**: {validation_results['mean_spatial_error']:.2f} meters\n", "- **RMSE**: {validation_results['rmse_spatial_error']:.2f} meters\n", "- **Maximum Error**: {validation_results['max_spatial_error']:.2f} meters\n", "- **Standard Deviation**: {validation_results['std_spatial_error']:.2f} meters\n", "- **Matches within 1m**: {within_1m}/{len(match_distances)} ({within_1m/len(match_distances)*100:.1f}%)\n", "- **Matches within 2m**: {within_2m}/{len(match_distances)} ({within_2m/len(match_distances)*100:.1f}%)\n", "\"\"\"\n", "    else:\n", "        report_content += \"\\n- **No successful matches found within tolerance**\\n\"\n", "    \n", "    report_content += f\"\"\"\n", "\n", "### Detection Breakdown\n", "- **True Positives**: {validation_results['true_positives']} (correctly detected piles)\n", "- **False Positives**: {validation_results['false_positives']} (incorrect detections)\n", "- **False Negatives**: {validation_results['false_negatives']} (missed ground truth piles)\n", "\n", "## Quality Assessment\n", "\n", "### Overall Performance\n", "\"\"\"\n", "    \n", "    # Quality assessment\n", "    if validation_results['f1_score'] >= 0.8:\n", "        quality = \"Excellent\"\n", "        quality_desc = \"The detection algorithm performs exceptionally well with high precision and recall.\"\n", "    elif validation_results['f1_score'] >= 0.6:\n", "        quality = \"Good\"\n", "        quality_desc = \"The detection algorithm performs well with acceptable precision and recall.\"\n", "    elif validation_results['f1_score'] >= 0.4:\n", "        quality = \"Fair\"\n", "        quality_desc = \"The detection algorithm shows moderate performance with room for improvement.\"\n", "    else:\n", "        quality = \"Poor\"\n", "        quality_desc = \"The detection algorithm requires significant improvement in precision and/or recall.\"\n", "    \n", "    report_content += f\"**{quality}** - {quality_desc}\\n\\n\"\n", "    \n", "    # Spatial accuracy assessment\n", "    if validation_results['matches']:\n", "        if validation_results['mean_spatial_error'] <= 1.0:\n", "            spatial_quality = \"High\"\n", "            spatial_desc = \"Detected pile locations are highly accurate with sub-meter precision.\"\n", "        elif validation_results['mean_spatial_error'] <= 2.0:\n", "            spatial_quality = \"Medium\"\n", "            spatial_desc = \"Detected pile locations show acceptable accuracy within the matching tolerance.\"\n", "        else:\n", "            spatial_quality = \"Low\"\n", "            spatial_desc = \"Detected pile locations show significant spatial errors requiring investigation.\"\n", "        \n", "        report_content += f\"### Spatial Accuracy\\n**{spatial_quality}** - {spatial_desc}\\n\\n\"\n", "    \n", "    report_content += f\"\"\"\n", "## Recommendations\n", "\n", "### Immediate Actions\n", "\"\"\"\n", "    \n", "    # Generate recommendations based on results\n", "    recommendations = []\n", "    \n", "    if validation_results['precision'] < 0.7:\n", "        recommendations.append(\"- **Reduce False Positives**: Increase confidence thresholds or refine detection criteria to reduce incorrect detections.\")\n", "    \n", "    if validation_results['recall'] < 0.7:\n", "        recommendations.append(\"- **Improve Detection Coverage**: Lower confidence thresholds or expand detection parameters to find more ground truth piles.\")\n", "    \n", "    if validation_results['matches'] and validation_results['mean_spatial_error'] > 1.5:\n", "        recommendations.append(\"- **Improve Spatial Accuracy**: Investigate coordinate system alignment and detection centroid calculation methods.\")\n", "    \n", "    if validation_results['false_negatives'] > validation_results['true_positives']:\n", "        recommendations.append(\"- **Address Detection Gaps**: Analyze missed pile locations to identify systematic detection failures.\")\n", "    \n", "    if not recommendations:\n", "        recommendations.append(\"- **Maintain Current Performance**: The detection algorithm is performing well within acceptable parameters.\")\n", "    \n", "    for rec in recommendations:\n", "        report_content += f\"\\n{rec}\"\n", "    \n", "    report_content += f\"\"\"\n", "\n", "### Future Improvements\n", "1. **Parameter Optimization**: Use validation results to fine-tune detection parameters for better performance.\n", "2. **Method Comparison**: Compare different detection methods to identify the most effective approach.\n", "3. **Training Data**: Use validated detections to create training datasets for machine learning approaches.\n", "4. **Coordinate Alignment**: Investigate and correct any systematic coordinate offsets between detection and ground truth data.\n", "\n", "## Technical Details\n", "\n", "### Validation Parameters\n", "- **Matching Method**: Nearest neighbor with distance threshold\n", "- **Distance Calculation**: 2D Euclidean distance (X,Y coordinates)\n", "- **Matching Tolerance**: {matching_tolerance} meters\n", "- **Confidence Threshold**: {confidence_threshold}\n", "\n", "### Output Files\n", "- **Validation Metrics**: {site_name}_real_validation_metrics_{timestamp}.json\n", "- **Detailed Matches**: {site_name}_detailed_matches_{timestamp}.csv\n", "- **Unmatched Detections**: {site_name}_unmatched_detections_{timestamp}.csv\n", "- **Missed Ground Truth**: {site_name}_missed_ground_truth_{timestamp}.csv\n", "- **Validation Visualization**: {site_name}_validation_visualization_{timestamp}.png\n", "\n", "---\n", "*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Real Pile Detection Validation Pipeline*\n", "\"\"\"\n", "    \n", "    # Save the report\n", "    report_file = detection_dir / f\"{site_name}_validation_report_{timestamp}.md\"\n", "    with open(report_file, 'w') as f:\n", "        f.write(report_content)\n", "    \n", "    print(f\"Validation report saved: {report_file}\")\n", "    \n", "else:\n", "    print(\"\\nReport generation disabled (save_results=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 10: Final Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\" * 80)\n", "print(\"REAL PILE DETECTION VALIDATION - FINAL SUMMARY\")\n", "print(\"=\" * 80)\n", "\n", "print(f\"\\n🎯 VALIDATION COMPLETED SUCCESSFULLY\")\n", "print(f\"  Site: {site_name}\")\n", "print(f\"  Ground method: {ground_method}\")\n", "print(f\"  Validation type: REAL (detected vs ground truth)\")\n", "print(f\"  Matching tolerance: {matching_tolerance}m\")\n", "\n", "print(f\"\\n📊 KEY RESULTS:\")\n", "print(f\"  Precision: {validation_results['precision']:.3f}\")\n", "print(f\"  Recall: {validation_results['recall']:.3f}\")\n", "print(f\"  F1-Score: {validation_results['f1_score']:.3f}\")\n", "if validation_results['matches']:\n", "    print(f\"  Mean spatial error: {validation_results['mean_spatial_error']:.2f}m\")\n", "    print(f\"  RMSE: {validation_results['rmse_spatial_error']:.2f}m\")\n", "\n", "print(f\"\\n🔢 DATA SUMMARY:\")\n", "print(f\"  Detected piles (high confidence): {len(high_confidence_piles)}\")\n", "print(f\"  Ground truth piles: {len(ground_truth_df)}\")\n", "print(f\"  Successful matches: {validation_results['true_positives']}\")\n", "print(f\"  False positives: {validation_results['false_positives']}\")\n", "print(f\"  False negatives: {validation_results['false_negatives']}\")\n", "\n", "# Quality assessment\n", "if validation_results['f1_score'] >= 0.8:\n", "    quality_emoji = \"🟢\"\n", "    quality = \"EXCELLENT\"\n", "elif validation_results['f1_score'] >= 0.6:\n", "    quality_emoji = \"🟡\"\n", "    quality = \"GOOD\"\n", "elif validation_results['f1_score'] >= 0.4:\n", "    quality_emoji = \"🟠\"\n", "    quality = \"FAIR\"\n", "else:\n", "    quality_emoji = \"🔴\"\n", "    quality = \"POOR\"\n", "\n", "print(f\"\\n{quality_emoji} OVERALL QUALITY: {quality}\")\n", "\n", "if save_results:\n", "    print(f\"\\n💾 OUTPUT FILES SAVED:\")\n", "    print(f\"  Directory: {detection_dir}\")\n", "    print(f\"  Key files:\")\n", "    print(f\"    - Validation metrics (JSON)\")\n", "    print(f\"    - Detailed matches (CSV)\")\n", "    print(f\"    - Unmatched detections (CSV)\")\n", "    print(f\"    - Missed ground truth (CSV)\")\n", "    if create_visualizations:\n", "        print(f\"    - Validation visualization (PNG)\")\n", "    print(f\"    - Comprehensive report (Markdown)\")\n", "\n", "print(f\"\\n🔄 NEXT STEPS:\")\n", "print(f\"  1. Review validation results and identify improvement opportunities\")\n", "print(f\"  2. Adjust detection parameters based on performance metrics\")\n", "print(f\"  3. Investigate false positives and false negatives\")\n", "print(f\"  4. Use validated results for as-built vs as-planned analysis\")\n", "print(f\"  5. Consider training ML models with validated detection data\")\n", "\n", "print(f\"\\n✅ REAL VALIDATION COMPLETE\")\n", "print(\"=\" * 80)\n", "\n", "# Key difference highlight\n", "print(f\"\\n🎉 IMPORTANT: This validation compares ACTUAL DETECTED PILES vs GROUND TRUTH\")\n", "print(f\"   (Not metadata vs metadata - this provides real validation metrics!)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook has successfully implemented **real pile detection validation** that addresses the circular validation problem:\n", "\n", "### **Key Achievements:**\n", "\n", "1. **Real Validation** - Compares **actual detected pile coordinates** against **ground truth survey data**\n", "2. **Comprehensive Metrics** - Provides precision, recall, F1-score, and spatial accuracy measurements\n", "3. **Detailed Analysis** - Includes method-specific performance, error distribution, and coverage analysis\n", "4. **Professional Reporting** - Generates comprehensive validation reports with recommendations\n", "\n", "### **Validation Process:**\n", "\n", "1. **Load Detection Results** - From comprehensive detection pipeline\n", "2. **Load Ground Truth** - From survey data (Trino_PIles.csv)\n", "3. **Spatial Matching** - Using configurable distance tolerance\n", "4. **Calculate Metrics** - Real performance measurements\n", "5. **Generate Reports** - Detailed analysis and recommendations\n", "\n", "### **Key Differences from Previous Validation:**\n", "\n", "| **Previous (Circular)** | **Current (Real)** |\n", "|------------------------|--------------------|\n", "| IFC metadata vs Ground truth | **Detected coordinates vs Ground truth** |\n", "| Artificially high metrics | **Real performance metrics** |\n", "| Limited actionable insights | **Actionable improvement recommendations** |\n", "| Metadata comparison | **Algorithm validation** |\n", "\n", "### **Output Files:**\n", "- **Validation Metrics** (JSON) - Complete performance data\n", "- **Detailed Matches** (CSV) - Individual pile matching results\n", "- **Unmatched Data** (CSV) - False positives and false negatives\n", "- **Validation Report** (Markdown) - Comprehensive analysis document\n", "- **Visualizations** (PNG) - Spatial distribution and performance plots\n", "\n", "### **Real Value:**\n", "This validation provides **actual algorithm performance** against **real ground truth data**, enabling:\n", "- Genuine performance assessment\n", "- Parameter optimization guidance\n", "- Method comparison capabilities\n", "- Confidence in detection results\n", "- Academic research validation\n", "\n", "**Use these results for your dissertation as evidence of real AI-enabled as-built verification performance!**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}