{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DBSCAN Parameter Exploration\n", "\n", "This notebook explores the actual point cloud data to determine optimal DBSCAN parameters for pile detection.\n", "\n", "**Purpose**: Find data-driven parameters instead of guessing\n", "\n", "**Analysis**:\n", "1. Load point cloud and examine point density\n", "2. Analyze elevated point distribution\n", "3. Use k-distance plots to find optimal eps\n", "4. Examine ground truth pile dimensions\n", "5. Recommend optimal DBSCAN parameters\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DBSCAN Parameter Exploration\n", "Goal: Find optimal parameters based on actual data\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.cluster import DBSCAN\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"DBSCAN Parameter Exploration\")\n", "print(\"Goal: Find optimal parameters based on actual data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud: ../../../../data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "Loaded point cloud: 517,002 points\n", "Point cloud bounds:\n", "  X: 435220.30 to 436796.35 (range: 1576.05m)\n", "  Y: 5010812.68 to 5012553.07 (range: 1740.39m)\n", "  Z: 152.43 to 182.42 (range: 29.98m)\n"]}], "source": ["# Configure paths\n", "base_path = Path(\"../../../..\")\n", "data_path = base_path / \"data\"\n", "site_name = \"trino_enel\"\n", "\n", "# Try to find point cloud file\n", "possible_point_clouds = [\n", "    data_path / \"processed\" / site_name / \"gcp_alignment_z_corrected\" / \"ransac_pmf\" / \"filtered\" / f\"{site_name}_gcp_aligned_z_corrected_filtered.ply\",\n", "    data_path / \"processed\" / site_name / \"ground_segmentation\" / \"ransac\" / f\"{site_name}_ransac_non_ground.ply\",\n", "    data_path / \"processed\" / site_name / \"ground_segmentation\" / \"pmf\" / f\"{site_name}_pmf_non_ground.ply\",\n", "    data_path / \"processed\" / site_name / \"ground_segmentation\" / \"csf\" / f\"{site_name}_csf_non_ground.ply\",\n", "    data_path / \"raw\" / site_name / \"point_cloud\" / f\"{site_name}.ply\"\n", "]\n", "\n", "point_cloud_file = None\n", "for pc_path in possible_point_clouds:\n", "    if pc_path.exists():\n", "        point_cloud_file = pc_path\n", "        break\n", "\n", "if point_cloud_file is None:\n", "    raise FileNotFoundError(f\"No point cloud found in any of the expected locations\")\n", "\n", "print(f\"Loading point cloud: {point_cloud_file}\")\n", "\n", "# Load point cloud\n", "pcd = o3d.io.read_point_cloud(str(point_cloud_file))\n", "points = np.asarray(pcd.points)\n", "\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f} (range: {points[:, 0].max() - points[:, 0].min():.2f}m)\")\n", "print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f} (range: {points[:, 1].max() - points[:, 1].min():.2f}m)\")\n", "print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f} (range: {points[:, 2].max() - points[:, 2].min():.2f}m)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Analyze Point Density"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overall Point Density Analysis:\n", "  Total area: 2742937.26 m²\n", "  Overall density: 0.19 points/m²\n", "  Average point spacing: 2.303 m\n", "\n", "Nearest Neighbor Distance Analysis:\n", "  Mean NN distance: 5.510 m\n", "  Median NN distance: 4.837 m\n", "  95th percentile: 11.930 m\n", "  99th percentile: 17.063 m\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate overall point density\n", "x_range = points[:, 0].max() - points[:, 0].min()\n", "y_range = points[:, 1].max() - points[:, 1].min()\n", "total_area = x_range * y_range\n", "overall_density = len(points) / total_area\n", "\n", "print(f\"Overall Point Density Analysis:\")\n", "print(f\"  Total area: {total_area:.2f} m²\")\n", "print(f\"  Overall density: {overall_density:.2f} points/m²\")\n", "print(f\"  Average point spacing: {1/np.sqrt(overall_density):.3f} m\")\n", "\n", "# Sample a subset for nearest neighbor analysis\n", "sample_size = min(10000, len(points))\n", "sample_indices = np.random.choice(len(points), sample_size, replace=False)\n", "sample_points = points[sample_indices]\n", "\n", "# Calculate nearest neighbor distances\n", "nbrs = NearestNeighbors(n_neighbors=2, algorithm='kd_tree').fit(sample_points[:, :2])\n", "distances, indices = nbrs.kneighbors(sample_points[:, :2])\n", "nearest_distances = distances[:, 1]  # Exclude self (distance 0)\n", "\n", "print(f\"\\nNearest Neighbor Distance Analysis:\")\n", "print(f\"  Mean NN distance: {nearest_distances.mean():.3f} m\")\n", "print(f\"  Median NN distance: {np.median(nearest_distances):.3f} m\")\n", "print(f\"  95th percentile: {np.percentile(nearest_distances, 95):.3f} m\")\n", "print(f\"  99th percentile: {np.percentile(nearest_distances, 99):.3f} m\")\n", "\n", "# Plot nearest neighbor distance distribution\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.hist(nearest_distances, bins=50, alpha=0.7, edgecolor='black')\n", "plt.axvline(nearest_distances.mean(), color='red', linestyle='--', label=f'Mean: {nearest_distances.mean():.3f}m')\n", "plt.axvline(np.median(nearest_distances), color='green', linestyle='--', label=f'Median: {np.median(nearest_distances):.3f}m')\n", "plt.xlabel('Nearest Neighbor Distance (m)')\n", "plt.ylabel('Frequency')\n", "plt.title('Nearest Neighbor Distance Distribution')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.hist(nearest_distances, bins=50, alpha=0.7, edgecolor='black', cumulative=True, density=True)\n", "plt.axhline(0.95, color='red', linestyle='--', label='95th percentile')\n", "plt.axvline(np.percentile(nearest_distances, 95), color='red', linestyle='--')\n", "plt.xlabel('Nearest Neighbor Distance (m)')\n", "plt.ylabel('Cumulative Probability')\n", "plt.title('Cumulative Distribution')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: <PERSON><PERSON><PERSON> Elevated Points (Potential Pile Region)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimated ground level: 154.23m\n", "\n", "Elevated Points Analysis (0.3m - 4.0m above ground):\n", "  Total elevated points: 363,421\n", "  Percentage of total: 70.3%\n", "  Elevated region area: 2742361.15 m²\n", "  Elevated point density: 0.13 points/m²\n", "  Average spacing in elevated region: 2.747 m\n", "\n", "Elevated Points NN Distance Analysis:\n", "  Mean NN distance: 7.357 m\n", "  Median NN distance: 6.359 m\n", "  95th percentile: 16.647 m\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Estimate ground level\n", "ground_z = np.percentile(points[:, 2], 5)\n", "print(f\"Estimated ground level: {ground_z:.2f}m\")\n", "\n", "# Filter elevated points (potential pile region)\n", "min_height = 0.3  # meters above ground\n", "max_height = 4.0  # meters above ground\n", "elevated_mask = (points[:, 2] >= ground_z + min_height) & (points[:, 2] <= ground_z + max_height)\n", "elevated_points = points[elevated_mask]\n", "\n", "print(f\"\\nElevated Points Analysis ({min_height}m - {max_height}m above ground):\")\n", "print(f\"  Total elevated points: {len(elevated_points):,}\")\n", "print(f\"  Percentage of total: {len(elevated_points)/len(points)*100:.1f}%\")\n", "\n", "if len(elevated_points) > 0:\n", "    # Calculate density in elevated region\n", "    elev_x_range = elevated_points[:, 0].max() - elevated_points[:, 0].min()\n", "    elev_y_range = elevated_points[:, 1].max() - elevated_points[:, 1].min()\n", "    elev_area = elev_x_range * elev_y_range\n", "    elev_density = len(elevated_points) / elev_area\n", "    \n", "    print(f\"  Elevated region area: {elev_area:.2f} m²\")\n", "    print(f\"  Elevated point density: {elev_density:.2f} points/m²\")\n", "    print(f\"  Average spacing in elevated region: {1/np.sqrt(elev_density):.3f} m\")\n", "    \n", "    # Analyze nearest neighbors in elevated points\n", "    if len(elevated_points) > 100:\n", "        elev_sample_size = min(5000, len(elevated_points))\n", "        elev_sample_indices = np.random.choice(len(elevated_points), elev_sample_size, replace=False)\n", "        elev_sample = elevated_points[elev_sample_indices]\n", "        \n", "        nbrs_elev = NearestNeighbors(n_neighbors=2, algorithm='kd_tree').fit(elev_sample[:, :2])\n", "        elev_distances, _ = nbrs_elev.kneighbors(elev_sample[:, :2])\n", "        elev_nn_distances = elev_distances[:, 1]\n", "        \n", "        print(f\"\\nElevated Points NN Distance Analysis:\")\n", "        print(f\"  Mean NN distance: {elev_nn_distances.mean():.3f} m\")\n", "        print(f\"  Median NN distance: {np.median(elev_nn_distances):.3f} m\")\n", "        print(f\"  95th percentile: {np.percentile(elev_nn_distances, 95):.3f} m\")\n", "        \n", "        # Plot elevated points distribution\n", "        plt.figure(figsize=(15, 5))\n", "        \n", "        plt.subplot(1, 3, 1)\n", "        plt.scatter(elevated_points[::10, 0], elevated_points[::10, 1], s=1, alpha=0.5)\n", "        plt.xlabel('X (m)')\n", "        plt.ylabel('Y (m)')\n", "        plt.title(f'Elevated Points Distribution\\n({len(elevated_points):,} points)')\n", "        plt.axis('equal')\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        plt.subplot(1, 3, 2)\n", "        plt.hist(elevated_points[:, 2], bins=30, alpha=0.7, edgecolor='black')\n", "        plt.axvline(ground_z, color='red', linestyle='--', label=f'Ground: {ground_z:.2f}m')\n", "        plt.xlabel('Z (m)')\n", "        plt.ylabel('Frequency')\n", "        plt.title('Elevated Points Height Distribution')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        plt.subplot(1, 3, 3)\n", "        plt.hist(elev_nn_distances, bins=30, alpha=0.7, edgecolor='black')\n", "        plt.axvline(elev_nn_distances.mean(), color='red', linestyle='--', label=f'Mean: {elev_nn_distances.mean():.3f}m')\n", "        plt.xlabel('NN Distance (m)')\n", "        plt.ylabel('Frequency')\n", "        plt.title('Elevated Points NN Distance')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "else:\n", "    print(\"  No elevated points found - check height filtering parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: K-Distance Plot for Optimal Eps"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculating k-distance plot for optimal eps determination...\n", "k=3: Mean=19.004m, Median=17.703m, 95th=33.830m\n", "k=4: Mean=22.451m, Median=21.028m, 95th=38.083m\n", "k=5: Mean=25.446m, Median=23.863m, 95th=42.584m\n", "k=6: Mean=28.214m, Median=26.863m, 95th=46.128m\n", "k=8: Mean=32.888m, Median=31.199m, 95th=52.481m\n", "k=10: Mean=37.289m, Median=35.646m, 95th=58.194m\n"]}, {"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 1500x1000 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Recommended eps values based on k-distance analysis:\n", "  Conservative (tight clusters): 9.633m\n", "  Moderate (balanced): 13.312m\n", "  Liberal (loose clusters): 16.647m\n"]}], "source": ["if len(elevated_points) > 100:\n", "    print(\"Calculating k-distance plot for optimal eps determination...\")\n", "    \n", "    # Use a reasonable sample for k-distance calculation\n", "    k_sample_size = min(3000, len(elevated_points))\n", "    k_sample_indices = np.random.choice(len(elevated_points), k_sample_size, replace=False)\n", "    k_sample = elevated_points[k_sample_indices]\n", "    \n", "    # Calculate k-distances for different k values\n", "    k_values = [3, 4, 5, 6, 8, 10]\n", "    \n", "    plt.figure(figsize=(15, 10))\n", "    \n", "    for i, k in enumerate(k_values):\n", "        # Calculate k-nearest neighbors\n", "        nbrs_k = NearestNeighbors(n_neighbors=k+1, algorithm='kd_tree').fit(k_sample[:, :2])\n", "        k_distances, _ = nbrs_k.kneighbors(k_sample[:, :2])\n", "        k_dist = k_distances[:, k]  # k-th nearest neighbor distance\n", "        \n", "        # Sort distances for plotting\n", "        k_dist_sorted = np.sort(k_dist)[::-1]  # Descending order\n", "        \n", "        plt.subplot(2, 3, i+1)\n", "        plt.plot(range(len(k_dist_sorted)), k_dist_sorted, 'b-', linewidth=1)\n", "        \n", "        # Find potential elbow point (simple method)\n", "        # Look for point where slope changes significantly\n", "        if len(k_dist_sorted) > 100:\n", "            # Calculate second derivative to find elbow\n", "            smooth_window = max(10, len(k_dist_sorted) // 100)\n", "            smoothed = np.convolve(k_dist_sorted, np.ones(smooth_window)/smooth_window, mode='valid')\n", "            if len(smoothed) > 20:\n", "                second_deriv = np.diff(smoothed, 2)\n", "                elbow_idx = np.argmax(second_deriv) + smooth_window//2\n", "                if elbow_idx < len(k_dist_sorted):\n", "                    elbow_value = k_dist_sorted[elbow_idx]\n", "                    plt.axhline(elbow_value, color='red', linestyle='--', \n", "                               label=f'Suggested eps: {elbow_value:.3f}m')\n", "        \n", "        plt.xlabel('Points (sorted by distance)')\n", "        plt.ylabel(f'{k}-NN Distance (m)')\n", "        plt.title(f'K-Distance Plot (k={k})')\n", "        plt.grid(True, alpha=0.3)\n", "        plt.legend()\n", "        \n", "        # Print statistics\n", "        print(f\"k={k}: Mean={k_dist.mean():.3f}m, Median={np.median(k_dist):.3f}m, 95th={np.percentile(k_dist, 95):.3f}m\")\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Recommend eps values based on analysis\n", "    print(f\"\\nRecommended eps values based on k-distance analysis:\")\n", "    print(f\"  Conservative (tight clusters): {np.percentile(elev_nn_distances, 75):.3f}m\")\n", "    print(f\"  Moderate (balanced): {np.percentile(elev_nn_distances, 90):.3f}m\")\n", "    print(f\"  Liberal (loose clusters): {np.percentile(elev_nn_distances, 95):.3f}m\")\n", "    \n", "else:\n", "    print(\"Insufficient elevated points for k-distance analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Load Ground Truth for Reference"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Loaded ground truth data: ../../../../data/processed/trino_enel/validation/Trino_PIles.csv\n", "Ground truth piles: 14599\n", "Columns: ['S.No', 'Table_no', 'Easting', 'Northing']\n", "Could not identify coordinate columns in ground truth data\n"]}], "source": ["# Try to load ground truth data for pile size reference\n", "validation_data_paths = [\n", "    data_path / \"processed\" / site_name / \"validation\" / \"Trino_PIles.csv\",\n", "    data_path / \"raw\" / site_name / \"validation\" / \"Trino_PIles.csv\",\n", "    data_path / \"validation\" / \"Trino_PIles.csv\"\n", "]\n", "\n", "ground_truth_df = None\n", "for val_path in validation_data_paths:\n", "    if val_path.exists():\n", "        try:\n", "            ground_truth_df = pd.read_csv(val_path)\n", "            print(f\"\\nLoaded ground truth data: {val_path}\")\n", "            print(f\"Ground truth piles: {len(ground_truth_df)}\")\n", "            print(f\"Columns: {list(ground_truth_df.columns)}\")\n", "            break\n", "        except Exception as e:\n", "            print(f\"Error loading {val_path}: {e}\")\n", "\n", "if ground_truth_df is not None:\n", "    # Try to extract coordinates\n", "    coord_columns = {\n", "        'x': ['x', 'X', 'longitude', 'lon', 'easting'],\n", "        'y': ['y', 'Y', 'latitude', 'lat', 'northing']\n", "    }\n", "    \n", "    gt_coords = {}\n", "    for coord, possible_names in coord_columns.items():\n", "        for name in possible_names:\n", "            if name in ground_truth_df.columns:\n", "                gt_coords[coord] = name\n", "                break\n", "    \n", "    if 'x' in gt_coords and 'y' in gt_coords:\n", "        gt_x = ground_truth_df[gt_coords['x']].values\n", "        gt_y = ground_truth_df[gt_coords['y']].values\n", "        \n", "        print(f\"\\nGround Truth Spatial Analysis:\")\n", "        print(f\"  X range: {gt_x.min():.2f} to {gt_x.max():.2f}\")\n", "        print(f\"  Y range: {gt_y.min():.2f} to {gt_y.max():.2f}\")\n", "        \n", "        # Calculate pile spacing from ground truth\n", "        if len(gt_x) > 1:\n", "            gt_points = np.column_stack([gt_x, gt_y])\n", "            nbrs_gt = NearestNeighbors(n_neighbors=2, algorithm='kd_tree').fit(gt_points)\n", "            gt_distances, _ = nbrs_gt.kneighbors(gt_points)\n", "            gt_nn_distances = gt_distances[:, 1]\n", "            \n", "            print(f\"\\nGround Truth Pile Spacing:\")\n", "            print(f\"  Mean spacing: {gt_nn_distances.mean():.2f}m\")\n", "            print(f\"  Median spacing: {np.median(gt_nn_distances):.2f}m\")\n", "            print(f\"  Min spacing: {gt_nn_distances.min():.2f}m\")\n", "            print(f\"  Max spacing: {gt_nn_distances.max():.2f}m\")\n", "            \n", "            # This gives us insight into expected pile cluster separation\n", "            print(f\"\\nImplications for DBSCAN:\")\n", "            print(f\"  eps should be < {gt_nn_distances.min():.2f}m to avoid merging adjacent piles\")\n", "            print(f\"  eps should be > point spacing within piles (from NN analysis above)\")\n", "    else:\n", "        print(\"Could not identify coordinate columns in ground truth data\")\n", "else:\n", "    print(\"\\nNo ground truth data found for reference\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Test Different DBSCAN Parameters"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing different DBSCAN parameter combinations...\n", "\n", "Testing DBSCAN on 5,000 elevated points...\n", "eps\tmin_samples\tclusters\tnoise_points\tlargest_cluster\n", "------------------------------------------------------------\n", "0.1\t5\t\t0\t5000\t\t0\n", "0.1\t10\t\t0\t5000\t\t0\n", "0.1\t15\t\t0\t5000\t\t0\n", "0.1\t20\t\t0\t5000\t\t0\n", "0.1\t30\t\t0\t5000\t\t0\n", "0.2\t5\t\t0\t5000\t\t0\n", "0.2\t10\t\t0\t5000\t\t0\n", "0.2\t15\t\t0\t5000\t\t0\n", "0.2\t20\t\t0\t5000\t\t0\n", "0.2\t30\t\t0\t5000\t\t0\n", "0.3\t5\t\t0\t5000\t\t0\n", "0.3\t10\t\t0\t5000\t\t0\n", "0.3\t15\t\t0\t5000\t\t0\n", "0.3\t20\t\t0\t5000\t\t0\n", "0.3\t30\t\t0\t5000\t\t0\n", "0.5\t5\t\t0\t5000\t\t0\n", "0.5\t10\t\t0\t5000\t\t0\n", "0.5\t15\t\t0\t5000\t\t0\n", "0.5\t20\t\t0\t5000\t\t0\n", "0.5\t30\t\t0\t5000\t\t0\n", "0.8\t5\t\t0\t5000\t\t0\n", "0.8\t10\t\t0\t5000\t\t0\n", "0.8\t15\t\t0\t5000\t\t0\n", "0.8\t20\t\t0\t5000\t\t0\n", "0.8\t30\t\t0\t5000\t\t0\n", "1.0\t5\t\t0\t5000\t\t0\n", "1.0\t10\t\t0\t5000\t\t0\n", "1.0\t15\t\t0\t5000\t\t0\n", "1.0\t20\t\t0\t5000\t\t0\n", "1.0\t30\t\t0\t5000\t\t0\n", "1.5\t5\t\t0\t5000\t\t0\n", "1.5\t10\t\t0\t5000\t\t0\n", "1.5\t15\t\t0\t5000\t\t0\n", "1.5\t20\t\t0\t5000\t\t0\n", "1.5\t30\t\t0\t5000\t\t0\n", "2.0\t5\t\t0\t5000\t\t0\n", "2.0\t10\t\t0\t5000\t\t0\n", "2.0\t15\t\t0\t5000\t\t0\n", "2.0\t20\t\t0\t5000\t\t0\n", "2.0\t30\t\t0\t5000\t\t0\n", "\n", "No good parameter combinations found with current criteria\n", "Consider adjusting height filtering or using different clustering approach\n"]}], "source": ["if len(elevated_points) > 100:\n", "    print(\"Testing different DBSCAN parameter combinations...\")\n", "    \n", "    # Use a sample for testing\n", "    test_sample_size = min(5000, len(elevated_points))\n", "    test_indices = np.random.choice(len(elevated_points), test_sample_size, replace=False)\n", "    test_points = elevated_points[test_indices]\n", "    \n", "    # Test different eps values\n", "    eps_values = [0.1, 0.2, 0.3, 0.5, 0.8, 1.0, 1.5, 2.0]\n", "    min_samples_values = [5, 10, 15, 20, 30]\n", "    \n", "    results = []\n", "    \n", "    print(f\"\\nTesting DBSCAN on {len(test_points):,} elevated points...\")\n", "    print(\"eps\\tmin_samples\\tclusters\\tnoise_points\\tlargest_cluster\")\n", "    print(\"-\" * 60)\n", "    \n", "    for eps in eps_values:\n", "        for min_samples in min_samples_values:\n", "            # Run DBSCAN\n", "            clustering = DBSCAN(eps=eps, min_samples=min_samples)\n", "            cluster_labels = clustering.fit_predict(test_points[:, :2])\n", "            \n", "            # Analyze results\n", "            unique_labels = set(cluster_labels)\n", "            unique_labels.discard(-1)  # Remove noise\n", "            n_clusters = len(unique_labels)\n", "            n_noise = list(cluster_labels).count(-1)\n", "            \n", "            largest_cluster = 0\n", "            if n_clusters > 0:\n", "                cluster_sizes = [list(cluster_labels).count(label) for label in unique_labels]\n", "                largest_cluster = max(cluster_sizes)\n", "            \n", "            results.append({\n", "                'eps': eps,\n", "                'min_samples': min_samples,\n", "                'n_clusters': n_clusters,\n", "                'n_noise': n_noise,\n", "                'largest_cluster': largest_cluster,\n", "                'noise_ratio': n_noise / len(test_points)\n", "            })\n", "            \n", "            print(f\"{eps:.1f}\\t{min_samples}\\t\\t{n_clusters}\\t{n_noise}\\t\\t{largest_cluster}\")\n", "    \n", "    # Convert to DataFrame for analysis\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    # Find good parameter combinations\n", "    # Good parameters: reasonable number of clusters, not too much noise, reasonable cluster sizes\n", "    good_results = results_df[\n", "        (results_df['n_clusters'] > 0) & \n", "        (results_df['n_clusters'] < 1000) &  # Not too many tiny clusters\n", "        (results_df['noise_ratio'] < 0.8) &  # Not too much noise\n", "        (results_df['largest_cluster'] < 500)  # Not one giant cluster\n", "    ]\n", "    \n", "    if len(good_results) > 0:\n", "        print(f\"\\nPromising parameter combinations:\")\n", "        print(good_results.sort_values('n_clusters').to_string(index=False))\n", "        \n", "        # Recommend best parameters\n", "        best_result = good_results.loc[good_results['n_clusters'].idxmax()]\n", "        print(f\"\\nRecommended parameters (most clusters with reasonable constraints):\")\n", "        print(f\"  eps: {best_result['eps']}\")\n", "        print(f\"  min_samples: {best_result['min_samples']}\")\n", "        print(f\"  Expected clusters: {best_result['n_clusters']}\")\n", "        print(f\"  Noise ratio: {best_result['noise_ratio']:.2f}\")\n", "    else:\n", "        print(\"\\nNo good parameter combinations found with current criteria\")\n", "        print(\"Consider adjusting height filtering or using different clustering approach\")\n", "        \n", "else:\n", "    print(\"Insufficient elevated points for DBSCAN testing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Final Recommendations"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "FINAL DBSCAN PARAMETER RECOMMENDATIONS\n", "============================================================\n", "\n", "Based on data analysis:\n", "1. Point cloud contains 517,002 total points\n", "2. Elevated region contains 363,421 points\n", "3. Overall point density: 0.19 points/m²\n", "4. Average point spacing: 2.303m\n", "5. Elevated points NN distance: 7.357m (mean)\n", "\n", "RECOMMENDED PARAMETERS:\n", "  eps: 11.74 meters\n", "  min_samples: 5\n", "\n", "NEXT STEPS:\n", "1. Update detection pipeline with recommended parameters\n", "2. Run detection and validate against ground truth\n", "3. Fine-tune parameters based on validation results\n", "4. Consider alternative clustering methods if DBSCAN fails\n", "\n", "============================================================\n"]}], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"FINAL DBSCAN PARAMETER RECOMMENDATIONS\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nBased on data analysis:\")\n", "print(f\"1. Point cloud contains {len(points):,} total points\")\n", "print(f\"2. Elevated region contains {len(elevated_points):,} points\")\n", "print(f\"3. Overall point density: {overall_density:.2f} points/m²\")\n", "\n", "if len(elevated_points) > 0:\n", "    print(f\"4. Average point spacing: {1/np.sqrt(overall_density):.3f}m\")\n", "    print(f\"5. Elevated points NN distance: {elev_nn_distances.mean():.3f}m (mean)\")\n", "\n", "print(f\"\\nRECOMMENDED PARAMETERS:\")\n", "if 'best_result' in locals():\n", "    print(f\"  eps: {best_result['eps']} meters\")\n", "    print(f\"  min_samples: {int(best_result['min_samples'])}\")\n", "    print(f\"  Expected to find: {int(best_result['n_clusters'])} clusters\")\n", "else:\n", "    # Fallback recommendations based on typical values\n", "    if len(elevated_points) > 0:\n", "        recommended_eps = max(0.3, np.percentile(elev_nn_distances, 85))\n", "        recommended_min_samples = max(5, int(elev_density * 0.25))  # 25% of expected points per m²\n", "    else:\n", "        recommended_eps = 0.5\n", "        recommended_min_samples = 10\n", "    \n", "    print(f\"  eps: {recommended_eps:.2f} meters\")\n", "    print(f\"  min_samples: {recommended_min_samples}\")\n", "\n", "print(f\"\\nNEXT STEPS:\")\n", "print(f\"1. Update detection pipeline with recommended parameters\")\n", "print(f\"2. Run detection and validate against ground truth\")\n", "print(f\"3. Fine-tune parameters based on validation results\")\n", "print(f\"4. Consider alternative clustering methods if DBSCAN fails\")\n", "\n", "print(f\"\\n\" + \"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}